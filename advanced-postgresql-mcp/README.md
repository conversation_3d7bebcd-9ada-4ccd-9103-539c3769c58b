# Advanced PostgreSQL MCP Server

A comprehensive PostgreSQL Database Management MCP (Model Context Protocol) Server that provides powerful database assessment, monitoring, and optimization capabilities. This tool is designed for database administrators, developers, and AI agents to perform thorough PostgreSQL database analysis and optimization.

## 🚀 Features

### 📊 **Database Inspection & Analysis**
- **Schema Discovery**: List and explore database schemas
- **Table Analysis**: Get detailed table structures, relationships, and metadata
- **Comprehensive Overview**: Complete database assessment with performance metrics
- **Relationship Mapping**: Analyze foreign key relationships and table dependencies

### ⚡ **Performance Monitoring**
- **Slow Query Analysis**: Identify and analyze slow-running queries using `pg_stat_statements`
- **Query Cost Analysis**: Detailed resource cost analysis with optimization recommendations
- **Blocking Query Detection**: Find queries causing locks and blocking issues
- **Table Usage Patterns**: Analyze table access patterns and optimization opportunities

### 🏥 **Health Monitoring**
- **Index Usage Analysis**: Identify unused and underperforming indexes
- **Vacuum/Analyze Status**: Monitor table maintenance status
- **Buffer Cache Analysis**: Analyze cache hit ratios and memory usage
- **Connection Monitoring**: Track active connections and database load

### 💰 **Cost Optimization**
- **Storage Optimization**: Identify storage savings opportunities
- **Index Optimization**: Find over-indexed tables and unused indexes
- **Dead Tuple Analysis**: Detect tables needing maintenance
- **Size Analysis**: Comprehensive database and table size reporting

### 🔒 **Enterprise Security**
- **SQL Injection Protection**: Advanced query validation and sanitization
- **Input Validation**: Comprehensive identifier and parameter validation
- **Query Complexity Limits**: Prevent resource-intensive operations
- **Read-Only Mode**: Safe query execution with comprehensive restrictions
- **Audit Logging**: Structured logging for all operations

### 🛠 **SQL Execution & Analysis**
- **Safe Query Execution**: Execute SELECT queries with security validation
- **Query Explanation**: Get detailed execution plans with performance insights
- **Query Optimization**: Receive actionable optimization recommendations

## 📋 Prerequisites

- Python 3.12 or higher
- PostgreSQL database (9.6 or higher recommended)
- Access to target PostgreSQL database

## 🔧 Installation

1. **Clone or download the MCP server files**

2. **Install dependencies using uv (recommended)**:
```bash
cd advanced-postgresql-mcp
uv sync
```

### Required PostgreSQL Extensions

For full functionality, install these extensions in your PostgreSQL database:

```sql
-- For slow query analysis (requires restart and configuration)
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Add to postgresql.conf and restart:
-- shared_preload_libraries = 'pg_stat_statements'
-- pg_stat_statements.max = 10000
-- pg_stat_statements.track = all
```

## 🚀 Usage

### Starting the Server

```bash
cd advanced-postgresql-mcp
python server.py
```

The server will start on `http://localhost:3003` with streamable HTTP transport.

### Authentication (Optional)

The server supports Bearer token authentication. When enabled, use the generated token:

```bash
# Server will display the Bearer token on startup
Bearer Token for Authentication:
eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
```

## 🛠 Available Tools

### 🔍 **Inspection Tools**

#### `list_schemas`
List all database schemas (excluding system schemas).

**Usage:**
```json
{
  "name": "list_schemas"
}
```

**Response:**
```json
{
  "schemas": ["public", "analytics", "reporting"],
  "total_count": 3,
  "timestamp": "2024-01-15T10:30:00"
}
```

#### `list_tables`
List tables in a specific schema.

**Parameters:**
- `schema` (optional): Schema name (default: "public")

**Usage:**
```json
{
  "name": "list_tables",
  "arguments": {
    "schema": "public"
  }
}
```

#### `get_table_structure`
Get detailed table structure including columns, constraints, indexes, and relationships.

**Parameters:**
- `table_name`: Name of the table
- `schema` (optional): Schema name (default: "public")

**Usage:**
```json
{
  "name": "get_table_structure",
  "arguments": {
    "table_name": "users",
    "schema": "public"
  }
}
```

#### `get_database_overview`
Get comprehensive database overview with performance and security analysis.

**Usage:**
```json
{
  "name": "get_database_overview"
}
```

### 📊 **Statistics & Performance**

#### `get_table_stats`
Get detailed table statistics and performance metrics.

**Parameters:**
- `table_name`: Name of the table
- `schema` (optional): Schema name (default: "public")

**Usage:**
```json
{
  "name": "get_table_stats",
  "arguments": {
    "table_name": "orders",
    "schema": "public"
  }
}
```

#### `get_slow_queries`
Identify slow-running queries from pg_stat_statements.

**Parameters:**
- `limit` (optional): Number of queries to return (default: 10, max: 50)

**Usage:**
```json
{
  "name": "get_slow_queries",
  "arguments": {
    "limit": 20
  }
}
```

#### `analyze_table_usage`
Detailed analysis of table usage patterns with optimization suggestions.

**Parameters:**
- `table_name`: Name of the table
- `schema` (optional): Schema name (default: "public")

### 🔍 **SQL Execution**

#### `execute_sql`
Execute safe SQL queries with security validation.

**Parameters:**
- `query`: SQL query to execute
- `read_only` (optional): Enable read-only mode (default: true)

**Usage:**
```json
{
  "name": "execute_sql",
  "arguments": {
    "query": "SELECT COUNT(*) FROM users WHERE active = true",
    "read_only": true
  }
}
```

#### `explain_query`
Get query execution plan for performance analysis.

**Parameters:**
- `query`: SQL query to explain
- `analyze` (optional): Include actual execution statistics (default: false)

**Usage:**
```json
{
  "name": "explain_query",
  "arguments": {
    "query": "SELECT * FROM users WHERE email = '<EMAIL>'",
    "analyze": true
  }
}
```

#### `query_cost_analysis`
Analyze query resource costs with detailed recommendations.

**Parameters:**
- `query`: SQL query to analyze

### 🏥 **Health Monitoring**

#### `get_index_usage`
Analyze index usage and identify unused indexes.

**Parameters:**
- `schema` (optional): Schema name (default: "public")

#### `vacuum_analyze_status`
Check when tables were last vacuumed/analyzed.

**Usage:**
```json
{
  "name": "vacuum_analyze_status"
}
```

#### `get_blocking_queries`
Find queries causing locks and blocking issues.

### 💰 **Optimization**

#### `storage_optimization_report`
Identify storage savings opportunities with detailed recommendations.

**Usage:**
```json
{
  "name": "storage_optimization_report"
}
```

## 🔒 Security Features

### Input Validation
- **SQL Injection Protection**: Advanced query parsing and validation
- **Identifier Validation**: PostgreSQL identifier compliance checking
- **Parameter Sanitization**: Automatic limit and input sanitization
- **Reserved Word Handling**: Supports reserved words as table names (automatically quoted)
- **System Object Protection**: Prevents access to dangerous system schemas

### Query Security
- **Read-Only Mode**: Restricts to SELECT, EXPLAIN, SHOW, DESCRIBE queries
- **Query Complexity Limits**: Prevents resource-intensive operations
- **Multi-Statement Prevention**: Blocks SQL injection attempts
- **Dangerous Function Blocking**: Prevents access to system functions

### Access Control
- **Schema Validation**: Prevents access to system schemas
- **Table Name Validation**: Comprehensive identifier validation
- **Connection Limits**: Configurable query and connection limits
- **Audit Logging**: Complete operation logging for security monitoring

## 🏗 Architecture

### Middleware Stack
1. **StructuredLoggingMiddleware**: Comprehensive request/response logging
2. **SecurityValidationMiddleware**: Input validation and security checks
3. **QueryComplexityMiddleware**: Query complexity analysis and limits
4. **DatabaseConnectionMiddleware**: Connection health monitoring
5. **DatabaseErrorMiddleware**: Enhanced error handling and messaging

### Security Layers
- **Input Sanitization**: Multi-layer input validation
- **Query Analysis**: Advanced SQL parsing and safety checks
- **Connection Management**: Secure database connection handling
- **Error Handling**: Security-conscious error messaging

## 🔧 Development

### Running Tests

```bash
# Run security tests
python test_security.py

# Run with verbose output
python test_security.py -v
```

### Code Quality

The project follows strict code quality standards:
- **Type Hints**: Full type annotation coverage
- **Error Handling**: Comprehensive exception handling
- **Logging**: Structured logging throughout
- **Documentation**: Detailed docstrings and comments

### Development Setup

1. **Install development dependencies**:
```bash
uv sync --dev
```

2. **Set up pre-commit hooks**:
```bash
pre-commit install
```

3. **Run code formatting**:
```bash
black .
isort .
```

## 📊 Performance Recommendations

### Database Optimization
- **Enable pg_stat_statements** for query analysis
- **Configure appropriate work_mem** for complex queries
- **Set up regular VACUUM and ANALYZE** schedules
- **Monitor index usage** and remove unused indexes
- **Implement connection pooling** for high-load environments

### MCP Server Optimization
- **Use connection pooling** for database connections
- **Configure appropriate timeouts** for long-running queries
- **Enable query result caching** for frequent queries
- **Monitor server performance** with structured logging

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow PEP 8 style guidelines
- Add comprehensive tests for new features
- Update documentation for any API changes
- Ensure security validation for all inputs

---

**Built with ❤️ for PostgreSQL database management and optimization**
