"""Configuration module for PostgreSQL MCP server."""

import logging
import os

from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)


def get_database_uri() -> str:
    """Get the database URI from environment variables."""
    database_uri = os.getenv("DATABASE_URI")
    if not database_uri:
        raise ValueError("DATABASE_URI environment variable is not set")
    return database_uri
