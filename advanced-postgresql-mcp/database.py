"""Database connection and utility functions."""

import logging
from datetime import datetime
from typing import Any

from config import get_database_uri
from sqlalchemy import Engine, create_engine

logger = logging.getLogger(__name__)


def get_db_connection() -> Engine:
    """Get or create database connection."""
    database_uri = get_database_uri()
    engine = create_engine(database_uri)
    logger.info("Database connection established")
    return engine


def serialize_datetime(obj: Any) -> str:
    """Serialize datetime objects to ISO format."""
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
