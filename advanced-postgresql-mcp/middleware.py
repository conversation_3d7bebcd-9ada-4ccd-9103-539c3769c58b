"""Custom middleware for the PostgreSQL MCP server."""

import logging

import sqlalchemy.exc
from database import get_db_connection
from fastmcp.exceptions import ToolError
from fastmcp.server.middleware import Middleware, MiddlewareContext
from security import is_safe_query, sanitize_limit, validate_identifier, validate_schema_table_name
from sqlalchemy import text

logger = logging.getLogger(__name__)


class DatabaseConnectionMiddleware(Middleware):
    """Ensures database connection health before tool execution."""

    async def on_call_tool(self, context: MiddlewareContext, call_next):
        # Check database connection health before tool execution
        try:
            engine = get_db_connection()
            with engine.connect() as conn:
                # Simple health check query
                conn.execute(text("SELECT 1"))
            logger.debug("Database connection health check passed")
        except Exception as e:
            logger.error(f"Database connection health check failed: {e}")
            raise ToolError("Database connection unavailable. Please check database connectivity.")

        # Proceed with tool execution
        return await call_next(context)


class DatabaseErrorMiddleware(Middleware):
    """Handles database-specific errors with better error messages."""

    async def on_call_tool(self, context: MiddlewareContext, call_next):
        try:
            return await call_next(context)
        except sqlalchemy.exc.OperationalError as e:
            # Handle database connection issues
            logger.error(f"Database operational error in {context.message.name}: {e}")
            if "connection" in str(e).lower():
                raise ToolError("Database connection lost. Please retry the operation.")
            else:
                raise ToolError(
                    "Database operational error. Please check your query and try again."
                )
        except sqlalchemy.exc.ProgrammingError as e:
            # Handle SQL syntax errors
            logger.error(f"SQL programming error in {context.message.name}: {e}")
            raise ToolError("Invalid SQL operation. Please check your query syntax.")
        except sqlalchemy.exc.DataError as e:
            # Handle data-related errors
            logger.error(f"Data error in {context.message.name}: {e}")
            raise ToolError("Data type or constraint error. Please check your data values.")
        except sqlalchemy.exc.IntegrityError as e:
            # Handle constraint violations
            logger.error(f"Integrity error in {context.message.name}: {e}")
            raise ToolError("Database integrity constraint violation.")


class SecurityValidationMiddleware(Middleware):
    """Enhanced security validation middleware with comprehensive input validation."""

    async def on_call_tool(self, context: MiddlewareContext, call_next):
        tool_name = context.message.name
        arguments = context.message.arguments or {}

        logger.debug(f"Security validation for tool: {tool_name}")

        # Validate SQL queries for SQL execution tools
        if tool_name in ["execute_sql", "explain_query", "query_cost_analysis"]:
            query = arguments.get("query", "")
            if not query:
                raise ToolError("Query parameter is required and cannot be empty.")

            if not isinstance(query, str):
                raise ToolError("Query parameter must be a string.")

            # Check query length (prevent DoS attacks)
            if len(query) > 10000:  # 10KB limit
                raise ToolError("Query is too long. Maximum allowed length is 10,000 characters.")

            if not is_safe_query(query):
                logger.warning(f"Unsafe query blocked in {tool_name}: {query[:100]}...")
                raise ToolError(
                    "Query not allowed. Only SELECT, EXPLAIN, SHOW, DESCRIBE queries are permitted. "
                    "The query must not contain dangerous keywords, multiple statements, or suspicious patterns."
                )

            # Additional validation for execute_sql
            if tool_name == "execute_sql":
                read_only = arguments.get("read_only", True)
                if not isinstance(read_only, bool):
                    raise ToolError("read_only parameter must be a boolean.")

        # Enhanced validation for tools that accept table/schema names
        schema_table_tools = {
            "get_table_structure": {"required": ["table_name"], "optional": ["schema"]},
            "get_table_stats": {"required": ["table_name"], "optional": ["schema"]},
            "analyze_table_usage": {"required": ["table_name"], "optional": ["schema"]},
        }

        schema_only_tools = {
            "list_tables": {"optional": ["schema"]},
            "get_index_usage": {"optional": ["schema"]},
            "vacuum_analyze_status": {"optional": ["schema"]},
        }

        # Validate schema and table combinations
        if tool_name in schema_table_tools:
            table_name = arguments.get("table_name")
            schema = arguments.get("schema", "public")

            if not table_name:
                raise ToolError("table_name parameter is required.")

            # Use enhanced validation
            is_valid, error_msg = validate_schema_table_name(schema, table_name)
            if not is_valid:
                logger.warning(f"Invalid identifiers in {tool_name}: {error_msg}")
                raise ToolError(error_msg)

        # Validate schema-only tools
        elif tool_name in schema_only_tools:
            schema = arguments.get("schema", "public")
            if schema and not validate_identifier(schema):
                logger.warning(f"Invalid schema in {tool_name}: {schema}")
                raise ToolError(
                    f"Invalid schema name: {schema}. Only alphanumeric characters and underscores are allowed."
                )

        # Validate and sanitize limits for performance tools
        if tool_name == "get_slow_queries":
            limit = arguments.get("limit", 10)
            sanitized_limit = sanitize_limit(limit, default=10, max_limit=50)
            if sanitized_limit != limit:
                logger.info(f"Limit sanitized in {tool_name}: {limit} -> {sanitized_limit}")
                # Update the arguments with sanitized value
                arguments["limit"] = sanitized_limit
                context.message.arguments = arguments

        # Validate boolean parameters
        boolean_params = {
            "execute_sql": ["read_only"],
            "explain_query": ["analyze"],
        }

        if tool_name in boolean_params:
            for param in boolean_params[tool_name]:
                value = arguments.get(param)
                if value is not None and not isinstance(value, bool):
                    raise ToolError(f"Parameter '{param}' must be a boolean value.")

        # Rate limiting check (basic implementation)
        # In production, you'd want a more sophisticated rate limiter
        if hasattr(context, "client_id"):
            # This would be implemented with a proper rate limiting system
            pass

        # Log successful validation
        logger.debug(f"Security validation passed for {tool_name}")

        return await call_next(context)


class QueryComplexityMiddleware(Middleware):
    """Middleware to prevent overly complex queries that could cause performance issues."""

    async def on_call_tool(self, context: MiddlewareContext, call_next):
        tool_name = context.message.name
        arguments = context.message.arguments or {}

        # Check query complexity for SQL execution tools
        if tool_name in ["execute_sql", "explain_query", "query_cost_analysis"]:
            query = arguments.get("query", "")
            if query:
                complexity_score = self._calculate_query_complexity(query)
                if complexity_score > 100:  # Arbitrary threshold
                    logger.warning(
                        f"High complexity query blocked in {tool_name}: score={complexity_score}"
                    )
                    raise ToolError(
                        f"Query complexity too high (score: {complexity_score}). "
                        "Please simplify your query to reduce resource usage."
                    )

        return await call_next(context)

    def _calculate_query_complexity(self, query: str) -> int:
        """Calculate a simple complexity score for a query."""
        score = 0
        query_upper = query.upper()

        # Count various complexity factors
        score += query_upper.count("JOIN") * 5
        score += query_upper.count("SUBQUERY") * 10
        score += query_upper.count("UNION") * 8
        score += query_upper.count("GROUP BY") * 3
        score += query_upper.count("ORDER BY") * 2
        score += query_upper.count("HAVING") * 4
        score += query_upper.count("CASE") * 2
        score += query_upper.count("EXISTS") * 6
        score += query_upper.count("IN (SELECT") * 8

        # Penalize deeply nested parentheses
        max_nesting = 0
        current_nesting = 0
        for char in query:
            if char == "(":
                current_nesting += 1
                max_nesting = max(max_nesting, current_nesting)
            elif char == ")":
                current_nesting -= 1

        score += max_nesting * 3

        # Penalize very long queries
        score += len(query) // 100

        return score
