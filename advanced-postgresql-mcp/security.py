"""Security validation functions for PostgreSQL MCP server."""

import re
from typing import Any


def is_safe_query(query: str) -> bool:
    """Check if a query is safe for read-only execution with comprehensive security checks."""
    if not query or not isinstance(query, str):
        return False

    # Check for suspicious comment patterns that might indicate injection attempts
    # Allow proper block comments but reject suspicious patterns
    if (
        query.strip().endswith("--")
        or query.strip().endswith("/*")
        or (query.strip().endswith("*/") and not query.strip().startswith("/*"))
    ):
        return False

    # Remove comments first
    query_clean = re.sub(r"--.*$", "", query, flags=re.MULTILINE)  # Remove line comments
    query_clean = re.sub(r"/\*.*?\*/", "", query_clean, flags=re.DOTALL)  # Remove block comments
    query_clean = query_clean.strip()

    if not query_clean:
        return False

    # Convert to uppercase for pattern matching
    query_upper = query_clean.upper()

    # Check for multiple statements (SQL injection attempt)
    # Allow only one trailing semicolon
    semicolon_count = query_clean.count(";")
    if semicolon_count > 1:
        return False
    if semicolon_count == 1 and not query_clean.rstrip().endswith(";"):
        return False

    # Remove trailing semicolon for pattern matching
    query_for_matching = query_upper.rstrip(";").strip()

    # Dangerous keywords that should never appear in read-only queries
    # Note: ANALYZE is handled separately for EXPLAIN context
    dangerous_keywords = [
        "DROP",
        "DELETE",
        "INSERT",
        "UPDATE",
        "CREATE",
        "ALTER",
        "TRUNCATE",
        "GRANT",
        "REVOKE",
        "COMMIT",
        "ROLLBACK",
        "SAVEPOINT",
        "RELEASE",
        "LOCK",
        "UNLOCK",
        "SET",
        "RESET",
        "COPY",
        "BULK",
        "MERGE",
        "CALL",
        "EXEC",
        "EXECUTE",
        "DECLARE",
        "CURSOR",
        "FETCH",
        "OPEN",
        "CLOSE",
        "DEALLOCATE",
        "PREPARE",
        "BEGIN",
        "START",
        "END",
        "TRANSACTION",
        "WORK",
        "CHECKPOINT",
        "VACUUM",
        "REINDEX",
        "CLUSTER",
        "LISTEN",
        "NOTIFY",
        "UNLISTEN",
        "LOAD",
        "IMPORT",
        "EXPORT",
        "BACKUP",
        "RESTORE",
    ]

    # Check for dangerous keywords (but allow them in string literals)
    # Simple check: if keyword appears outside of quotes
    for keyword in dangerous_keywords:
        # Look for the keyword as a separate word (not part of another word)
        pattern = r"\b" + re.escape(keyword) + r"\b"
        if re.search(pattern, query_for_matching):
            # Additional check: make sure it's not in a string literal
            # This is a simplified check - in production, you'd want a proper SQL parser
            if not _is_in_string_literal(query_clean, keyword):
                return False

    # Special handling for ANALYZE - only allow in EXPLAIN context
    if "ANALYZE" in query_for_matching:
        if not query_for_matching.startswith("EXPLAIN"):
            return False

    # Check for dangerous functions
    dangerous_functions = [
        "PG_READ_FILE",
        "PG_WRITE_FILE",
        "PG_LS_DIR",
        "PG_STAT_FILE",
        "COPY_FROM_PROGRAM",
        "COPY_TO_PROGRAM",
        "DBG_ASSERT_ENABLED",
        "PG_RELOAD_CONF",
        "PG_ROTATE_LOGFILE",
        "PG_CANCEL_BACKEND",
        "PG_TERMINATE_BACKEND",
        "LO_IMPORT",
        "LO_EXPORT",
    ]

    for func in dangerous_functions:
        if func in query_upper:
            return False

    # Allow only specific safe statement types
    safe_patterns = [
        r"^SELECT\s",  # SELECT statements
        r"^WITH\s+\w+.*AS\s*\(.*\)\s*SELECT\s",  # CTEs with SELECT
        r"^EXPLAIN\s+(\([^)]*\)\s+)?(SELECT|WITH)\s",  # EXPLAIN with optional options
        r"^SHOW\s+\w+",  # SHOW statements
        r"^DESCRIBE\s+\w+",  # DESCRIBE statements
        r"^DESC\s+\w+",  # DESC statements
        r"^\\D\s*\w+",  # psql describe commands (\d, \dt, etc.)
        r"^\\L\s*",  # psql list commands (\l)
    ]

    # Check if query matches any safe pattern
    matches_safe_pattern = any(
        re.match(pattern, query_for_matching, re.IGNORECASE | re.DOTALL)
        for pattern in safe_patterns
    )

    if not matches_safe_pattern:
        return False

    # Additional security checks
    # Check for potential command injection attempts
    command_injection_patterns = [
        r";\s*\\!",  # psql shell escape
        r";\s*COPY\s+.*TO\s+PROGRAM",  # COPY to program
        r";\s*COPY\s+.*FROM\s+PROGRAM",  # COPY from program
        r"\$\$.*\$\$",  # Dollar-quoted strings (can hide malicious code)
    ]

    for pattern in command_injection_patterns:
        if re.search(pattern, query_upper, re.IGNORECASE):
            return False

    # Check for excessive nesting or complexity that might indicate an attack
    if (
        query_clean.count("(") > 20
        or query_clean.count("SELECT") > 5  # Reduced from 10 to 5
        or query_upper.count("UNION") > 1
    ):  # Limit UNION operations to 1
        return False

    return True


def _is_in_string_literal(query: str, keyword: str) -> bool:
    """Helper function to check if a keyword appears inside a string literal."""
    # This is a simplified implementation
    # In production, you'd want a proper SQL parser
    in_single_quote = False
    in_double_quote = False
    i = 0
    keyword_upper = keyword.upper()
    query_upper = query.upper()

    while i < len(query):
        char = query[i]

        if char == "'" and not in_double_quote:
            in_single_quote = not in_single_quote
        elif char == '"' and not in_single_quote:
            in_double_quote = not in_double_quote
        elif not in_single_quote and not in_double_quote:
            # Check if keyword starts at this position
            if query_upper[i : i + len(keyword_upper)] == keyword_upper:
                # Check word boundaries
                before_ok = i == 0 or not query[i - 1].isalnum()
                after_ok = (
                    i + len(keyword_upper) >= len(query)
                    or not query[i + len(keyword_upper)].isalnum()
                )
                if before_ok and after_ok:
                    return False  # Found keyword outside string literal
        i += 1

    return True  # Keyword only found inside string literals


def validate_identifier(identifier: str) -> bool:
    """Validate SQL identifier with enhanced security checks."""
    if not identifier or not isinstance(identifier, str):
        return False

    # Check length limits (PostgreSQL identifier limit is 63 characters)
    if len(identifier) > 63:
        return False

    # Basic pattern: must start with letter or underscore, followed by letters, digits, or underscores
    if not re.match(r"^[a-zA-Z_][a-zA-Z0-9_]*$", identifier):
        return False

    # Reject identifiers that are too short (single character identifiers can be risky)
    if len(identifier) < 1:
        return False

    # Check against PostgreSQL reserved words that are truly dangerous for identifiers
    # Note: Most reserved words can be used as table names if quoted, but some are particularly risky
    dangerous_reserved_words = {
        "PG_CATALOG",
        "INFORMATION_SCHEMA",
        "PG_TOAST",
        "PG_TEMP",  # System schema names only
    }

    # Only reject truly dangerous reserved words that could cause security issues
    if identifier.upper() in dangerous_reserved_words:
        return False

    # Check for potentially dangerous patterns
    dangerous_patterns = [
        r"^pg_",  # PostgreSQL system objects
        r"^information_schema",  # Information schema
        r".*\$\$.*",  # Dollar quoting
        r".*--.*",  # SQL comments
        r".*/\*.*",  # Block comments
        r".*\*/.*",  # Block comments
        r'.*[\'"`].*',  # Quotes
        r".*[;].*",  # Semicolons
        r".*[\(\)].*",  # Parentheses
        r".*[\[\]].*",  # Brackets
        r".*[<>].*",  # Angle brackets
        r".*[=!].*",  # Operators
        r".*[\+\-\*/].*",  # Math operators
        r".*[%&|^~].*",  # Bitwise operators
        r".*[@#].*",  # Special characters
    ]

    for pattern in dangerous_patterns:
        if re.match(pattern, identifier, re.IGNORECASE):
            return False

    # Additional checks for common attack patterns
    if any(char in identifier for char in [" ", "\t", "\n", "\r", "\0"]):
        return False

    # Check for Unicode normalization attacks (basic check)
    if identifier != identifier.encode("ascii", "ignore").decode("ascii"):
        return False

    return True


def validate_schema_table_name(schema: str, table: str) -> tuple[bool, str]:
    """Validate both schema and table names together with additional context checks."""
    if not validate_identifier(schema):
        return False, f"Invalid schema name: {schema}"

    if not validate_identifier(table):
        return False, f"Invalid table name: {table}"

    # Additional combined validation
    combined_length = len(schema) + len(table) + 1  # +1 for the dot
    if combined_length > 100:  # More reasonable limit for combined identifier
        return False, "Combined schema.table name too long"

    # Check for suspicious combinations
    if schema.lower() == table.lower():
        return False, "Schema and table names should not be identical"

    return True, "Valid"


def sanitize_limit(limit: Any, default: int = 10, max_limit: int = 1000) -> int:
    """Sanitize and validate limit parameters for queries."""
    if limit is None:
        return default

    try:
        limit_int = int(limit)
    except (ValueError, TypeError):
        return default

    if limit_int < 1:
        return default

    if limit_int > max_limit:
        return max_limit

    return limit_int
