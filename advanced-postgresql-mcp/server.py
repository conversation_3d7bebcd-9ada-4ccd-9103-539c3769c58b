"""PostgreSQL Database Management MCP Server - Refactored Version."""

import logging

from fastmcp import FastMCP
from fastmcp.server.auth.providers.bearer import RSAKeyPair
from fastmcp.server.middleware.logging import StructuredLoggingMiddleware
from middleware import (
    DatabaseConnectionMiddleware,
    DatabaseErrorMiddleware,
    QueryComplexityMiddleware,
    SecurityValidationMiddleware,
)
from tools.health import get_index_usage, vacuum_analyze_status
from tools.inspection import get_table_structure, list_schemas, list_tables
from tools.optimization import storage_optimization_report
from tools.overview import get_database_overview
from tools.performance import analyze_table_usage, get_blocking_queries, get_slow_queries
from tools.sql_execution import execute_sql, explain_query, query_cost_analysis
from tools.statistics import get_table_stats

logger = logging.getLogger(__name__)

# Generate key pair for Bearer Token auth (for testing - use proper IdP in production)
key_pair = RSAKeyPair.generate()

# Create FastMCP server
mcp = FastMCP(
    name="postgresql_assessment",
    # auth=auth  # Uncomment when authentication is needed
)

# Add comprehensive middleware stack
mcp.add_middleware(
    StructuredLoggingMiddleware(include_payloads=True, log_level=logging.INFO, logger=logger)
)
mcp.add_middleware(SecurityValidationMiddleware())
mcp.add_middleware(QueryComplexityMiddleware())
mcp.add_middleware(DatabaseConnectionMiddleware())
mcp.add_middleware(DatabaseErrorMiddleware())

# Register inspection tools
mcp.tool("list_schemas")(list_schemas)
mcp.tool("list_tables")(list_tables)
mcp.tool("get_table_structure")(get_table_structure)

# Register statistics tools
mcp.tool("get_table_stats")(get_table_stats)

# Register overview tools
mcp.tool("get_database_overview")(get_database_overview)

# Register SQL execution tools
mcp.tool("execute_sql")(execute_sql)
mcp.tool("explain_query")(explain_query)
mcp.tool("query_cost_analysis")(query_cost_analysis)

# Register performance tools
mcp.tool("get_slow_queries")(get_slow_queries)
mcp.tool("get_blocking_queries")(get_blocking_queries)
mcp.tool("analyze_table_usage")(analyze_table_usage)

# Register health monitoring tools
mcp.tool("get_index_usage")(get_index_usage)
mcp.tool("vacuum_analyze_status")(vacuum_analyze_status)

# Register optimization tools
mcp.tool("storage_optimization_report")(storage_optimization_report)


if __name__ == "__main__":
    # Generate a test token for development
    test_token = key_pair.create_token(
        subject="dev-user",
        issuer="https://postgresql-assessment.local",
        audience="postgresql-assessment-server",
        scopes=["database:read", "database:assess"],
    )

    print("=" * 60)
    print("PostgreSQL Database Management MCP Server")
    print("Tony's Comprehensive DBA Toolkit - Refactored")
    print("=" * 60)
    # print("Bearer Token for Authentication:")
    # print(test_token)
    print("=" * 60)
    print("\n🔧 INSPECTION TOOLS:")
    print("- list_schemas: Get all database schemas")
    print("- list_tables: Get tables in a schema")
    print("- get_table_structure: Get detailed table structure")
    print("- get_table_stats: Get table statistics and performance")
    print("- get_database_overview: Complete database assessment with performance metrics")
    print("\n⚡ SQL EXECUTION & PERFORMANCE:")
    print("- execute_sql: Execute safe SQL queries")
    print("- explain_query: Get query execution plans")
    print("- get_slow_queries: Identify slow queries")
    print("- get_blocking_queries: Find blocking queries")
    print("- analyze_table_usage: Detailed table usage analysis")
    print("- query_cost_analysis: Analyze query resource costs")
    print("\n🏥 HEALTH & MONITORING:")
    print("- get_index_usage: Analyze index usage")
    print("- vacuum_analyze_status: Check vacuum/analyze status")
    print("\n💰 COST OPTIMIZATION:")
    print("- storage_optimization_report: Storage savings opportunities")
    print("=" * 60)

    # Run the MCP server
    mcp.run(transport="streamable-http", port=3003, host="0.0.0.0")
