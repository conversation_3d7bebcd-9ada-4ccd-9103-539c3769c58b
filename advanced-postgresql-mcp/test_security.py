#!/usr/bin/env python3
"""Test script to verify enhanced security functions are properly applied."""

import sys
from datetime import datetime

from database import serialize_datetime
from security import is_safe_query, sanitize_limit, validate_identifier, validate_schema_table_name


def test_is_safe_query():
    """Test the enhanced is_safe_query function."""
    print("Testing enhanced is_safe_query function...")

    # Safe queries
    safe_queries = [
        "SELECT * FROM users",
        "SELECT id, name FROM products WHERE price > 100",
        "EXPLAIN SELECT * FROM orders",
        "EXPLAIN (ANALYZE, BUFFERS) SELECT * FROM users",
        "SHOW TABLES",
        "DESCRIBE users",
        "WITH cte AS (SELECT * FROM users) SELECT * FROM cte",
        "  SELECT  *  FROM  users  ",  # With extra spaces
        "-- Comment\nSELECT * FROM users",  # With comments
        "/* Block comment */ SELECT * FROM users",
        "SELECT * FROM users;",  # With trailing semicolon
        "SELECT 'DROP TABLE' as message FROM users",  # Dangerous keyword in string
    ]

    # Unsafe queries
    unsafe_queries = [
        "DROP TABLE users",
        "INSERT INTO users VALUES (1, 'test')",
        "UPDATE users SET name = 'test'",
        "DELETE FROM users",
        "CREATE TABLE test (id INT)",
        "ALTER TABLE users ADD COLUMN test VARCHAR(50)",
        "TRUNCATE TABLE users",
        "SELECT * FROM users; DROP TABLE users;",  # SQL injection attempt
        "SELECT * FROM users; INSERT INTO logs VALUES ('hack');",  # Multiple statements
        "COPY users TO PROGRAM 'cat > /tmp/users.txt'",  # Command injection
        "SELECT pg_read_file('/etc/passwd')",  # Dangerous function
        "SELECT * FROM users WHERE id = 1; --",  # Comment injection
        "",  # Empty query
        "   ",  # Whitespace only
        "SELECT * FROM users WHERE name = 'test'; SELECT * FROM passwords;",  # Multiple selects
        "SELECT * FROM users UNION ALL SELECT * FROM passwords UNION ALL SELECT * FROM secrets",  # Too many unions
    ]

    print("  Testing safe queries...")
    for query in safe_queries:
        if not is_safe_query(query):
            print(f"    ❌ FAILED: '{query}' should be safe but was rejected")
            return False
        else:
            print(f"    ✅ PASSED: '{query}' correctly identified as safe")

    print("  Testing unsafe queries...")
    for query in unsafe_queries:
        if is_safe_query(query):
            print(f"    ❌ FAILED: '{query}' should be unsafe but was accepted")
            return False
        else:
            print(f"    ✅ PASSED: '{query}' correctly identified as unsafe")

    print("✅ Enhanced is_safe_query tests passed!\n")
    return True


def test_validate_identifier():
    """Test the enhanced validate_identifier function."""
    print("Testing enhanced validate_identifier function...")

    # Valid identifiers
    valid_identifiers = [
        "users",
        "user",  # Allow reserved word "user" since it can be quoted
        "user_table",
        "UserTable",
        "table123",
        "_private_table",
        "a",
        "A",
        "_",
        "table_with_underscores_123",
        "my_table_name",
        "order",  # Allow reserved word "order" since it can be quoted
        "group",  # Allow reserved word "group" since it can be quoted
    ]

    # Invalid identifiers
    invalid_identifiers = [
        "123table",  # Starts with number
        "user-table",  # Contains hyphen
        "user table",  # Contains space
        "user.table",  # Contains dot
        "user@table",  # Contains special character
        "",  # Empty string
        "user;table",  # Contains semicolon
        "user'table",  # Contains quote
        'user"table',  # Contains double quote
        "user`table",  # Contains backtick
        "user(table)",  # Contains parentheses
        "user[table]",  # Contains brackets
        "pg_stat_activity",  # System table pattern
        "information_schema",  # System schema (dangerous reserved word)
        "pg_catalog",  # System schema (dangerous reserved word)
        "user$$table",  # Dollar quoting
        "user--comment",  # SQL comment
        "user/*comment*/",  # Block comment
        "a" * 64,  # Too long (over 63 chars)
        "user\ttable",  # Tab character
        "user\ntable",  # Newline
        "user table",  # Space
        "usér_table",  # Non-ASCII character
    ]

    print("  Testing valid identifiers...")
    for identifier in valid_identifiers:
        if not validate_identifier(identifier):
            print(f"    ❌ FAILED: '{identifier}' should be valid but was rejected")
            return False
        else:
            print(f"    ✅ PASSED: '{identifier}' correctly identified as valid")

    print("  Testing invalid identifiers...")
    for identifier in invalid_identifiers:
        if validate_identifier(identifier):
            print(f"    ❌ FAILED: '{identifier}' should be invalid but was accepted")
            return False
        else:
            print(f"    ✅ PASSED: '{identifier}' correctly identified as invalid")

    print("✅ Enhanced validate_identifier tests passed!\n")
    return True


def test_validate_schema_table_name():
    """Test the validate_schema_table_name function."""
    print("Testing validate_schema_table_name function...")

    # Valid combinations
    valid_combinations = [
        ("public", "users"),
        ("myschema", "my_table"),
        ("_private", "table123"),
        ("schema1", "table_name"),
    ]

    # Invalid combinations
    invalid_combinations = [
        ("invalid-schema", "users"),  # Invalid schema
        ("public", "invalid-table"),  # Invalid table
        ("", "users"),  # Empty schema
        ("public", ""),  # Empty table
        ("public", "public"),  # Same name
        ("a" * 50, "b" * 50),  # Combined too long
    ]

    print("  Testing valid combinations...")
    for schema, table in valid_combinations:
        is_valid, msg = validate_schema_table_name(schema, table)
        if not is_valid:
            print(f"    ❌ FAILED: '{schema}.{table}' should be valid but was rejected: {msg}")
            return False
        else:
            print(f"    ✅ PASSED: '{schema}.{table}' correctly identified as valid")

    print("  Testing invalid combinations...")
    for schema, table in invalid_combinations:
        is_valid, msg = validate_schema_table_name(schema, table)
        if is_valid:
            print(f"    ❌ FAILED: '{schema}.{table}' should be invalid but was accepted")
            return False
        else:
            print(f"    ✅ PASSED: '{schema}.{table}' correctly identified as invalid: {msg}")

    print("✅ validate_schema_table_name tests passed!\n")
    return True


def test_sanitize_limit():
    """Test the sanitize_limit function."""
    print("Testing sanitize_limit function...")

    test_cases = [
        # (input, expected_output, description)
        (10, 10, "Valid integer"),
        ("10", 10, "String number"),
        (0, 10, "Zero (should use default)"),
        (-5, 10, "Negative (should use default)"),
        (2000, 1000, "Too large (should cap at max)"),
        (None, 10, "None (should use default)"),
        ("invalid", 10, "Invalid string (should use default)"),
        (10.5, 10, "Float (should convert to int)"),
        ("", 10, "Empty string (should use default)"),
    ]

    for input_val, expected, description in test_cases:
        result = sanitize_limit(input_val, default=10, max_limit=1000)
        if result == expected:
            print(f"    ✅ PASSED: {description} - {input_val} -> {result}")
        else:
            print(f"    ❌ FAILED: {description} - {input_val} -> {result}, expected {expected}")
            return False

    print("✅ sanitize_limit tests passed!\n")
    return True


def test_serialize_datetime():
    """Test the serialize_datetime function."""
    print("Testing serialize_datetime function...")

    # Test with datetime object
    test_datetime = datetime(2024, 6, 26, 12, 0, 0)
    try:
        result = serialize_datetime(test_datetime)
        expected = "2024-06-26T12:00:00"
        if result == expected:
            print(f"    ✅ PASSED: datetime serialized correctly: {result}")
        else:
            print(f"    ❌ FAILED: Expected '{expected}', got '{result}'")
            return False
    except Exception as e:
        print(f"    ❌ FAILED: Exception occurred: {e}")
        return False

    # Test with non-datetime object (should raise TypeError)
    try:
        serialize_datetime("not a datetime")
        print("    ❌ FAILED: Should have raised TypeError for non-datetime object")
        return False
    except TypeError:
        print("    ✅ PASSED: TypeError correctly raised for non-datetime object")
    except Exception as e:
        print(f"    ❌ FAILED: Wrong exception type: {e}")
        return False

    print("✅ serialize_datetime tests passed!\n")
    return True


def main():
    """Run all enhanced security tests."""
    print("=" * 70)
    print("PostgreSQL MCP Enhanced Security Functions Test Suite")
    print("=" * 70)
    print()

    tests = [
        test_is_safe_query,
        test_validate_identifier,
        test_validate_schema_table_name,
        test_sanitize_limit,
        test_serialize_datetime,
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1

    print("=" * 70)
    print(f"Test Results: {passed} passed, {failed} failed")
    print("=" * 70)

    if failed == 0:
        print("🎉 All enhanced security tests passed!")
        print("\nSecurity features implemented:")
        print("✅ Enhanced SQL injection protection")
        print("✅ Comprehensive identifier validation")
        print("✅ Reserved word checking")
        print("✅ Command injection prevention")
        print("✅ Query complexity limits")
        print("✅ Input sanitization")
        print("✅ Length limits and DoS protection")
        return 0
    else:
        print("⚠️  Some security tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
