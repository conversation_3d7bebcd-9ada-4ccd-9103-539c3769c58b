"""Database health monitoring tools."""

import logging
from datetime import datetime
from typing import Any

from database import get_db_connection, serialize_datetime
from fastmcp import Context
from security import validate_identifier
from sqlalchemy import text

logger = logging.getLogger(__name__)


async def get_index_usage(ctx: Context, schema: str = "public") -> dict[str, Any]:
    """Analyze index usage and identify unused indexes."""
    try:
        if not validate_identifier(schema):
            return {"error": "Invalid schema name"}

        engine = get_db_connection()

        query = text("""
            SELECT
                schemaname,
                relname as tablename,
                indexrelname,
                idx_scan,
                idx_tup_read,
                idx_tup_fetch,
                pg_size_pretty(pg_relation_size(indexrelid)) as index_size,
                pg_relation_size(indexrelid) as index_size_bytes
            FROM pg_stat_user_indexes
            WHERE schemaname = :schema_name
            ORDER BY idx_scan ASC, pg_relation_size(indexrelid) DESC
        """)

        with engine.connect() as conn:
            result = conn.execute(query, {"schema_name": schema}).fetchall()

            indexes = []
            unused_indexes = []
            total_index_size = 0

            for row in result:
                index_info = {
                    "schema": row[0],
                    "table": row[1],
                    "index_name": row[2],
                    "scans": row[3] or 0,
                    "tuples_read": row[4] or 0,
                    "tuples_fetched": row[5] or 0,
                    "size": row[6],
                    "size_bytes": row[7] or 0,
                }

                indexes.append(index_info)
                total_index_size += index_info["size_bytes"]

                if index_info["scans"] == 0:
                    unused_indexes.append(index_info)

            return {
                "schema": schema,
                "total_indexes": len(indexes),
                "unused_indexes_count": len(unused_indexes),
                "total_index_size_bytes": total_index_size,
                "total_index_size": f"{total_index_size / (1024**3):.2f} GB"
                if total_index_size > 0
                else "0 GB",
                "indexes": indexes,
                "unused_indexes": unused_indexes,
                "recommendations": [
                    f"Consider dropping {len(unused_indexes)} unused indexes to save space"
                ]
                if unused_indexes
                else ["All indexes are being used"],
                "timestamp": serialize_datetime(datetime.now()),
            }

    except Exception as e:
        logger.error(f"Error analyzing index usage: {str(e)}")
        return {"error": str(e), "timestamp": serialize_datetime(datetime.now())}


async def vacuum_analyze_status(ctx: Context) -> dict[str, Any]:
    """Check when tables were last vacuumed/analyzed."""
    try:
        engine = get_db_connection()

        query = text("""
            SELECT
                schemaname,
                relname,
                last_vacuum,
                last_autovacuum,
                last_analyze,
                last_autoanalyze,
                vacuum_count,
                autovacuum_count,
                analyze_count,
                autoanalyze_count,
                n_dead_tup,
                n_live_tup
            FROM pg_stat_user_tables
            ORDER BY last_vacuum DESC NULLS LAST, last_autovacuum DESC NULLS LAST
        """)

        with engine.connect() as conn:
            result = conn.execute(query).fetchall()

            tables = []
            needs_vacuum = []
            needs_analyze = []

            for row in result:
                table_info = {
                    "schema": row[0],
                    "table": row[1],
                    "last_vacuum": row[2].isoformat() if row[2] else None,
                    "last_autovacuum": row[3].isoformat() if row[3] else None,
                    "last_analyze": row[4].isoformat() if row[4] else None,
                    "last_autoanalyze": row[5].isoformat() if row[5] else None,
                    "vacuum_count": row[6] or 0,
                    "autovacuum_count": row[7] or 0,
                    "analyze_count": row[8] or 0,
                    "autoanalyze_count": row[9] or 0,
                    "dead_tuples": row[10] or 0,
                    "live_tuples": row[11] or 0,
                }

                tables.append(table_info)

                # Check if needs vacuum (>20% dead tuples)
                total_tuples = table_info["live_tuples"] + table_info["dead_tuples"]
                if total_tuples > 0 and (table_info["dead_tuples"] / total_tuples) > 0.2:
                    needs_vacuum.append(table_info)

                # Check if never analyzed
                if not table_info["last_analyze"] and not table_info["last_autoanalyze"]:
                    needs_analyze.append(table_info)

            return {
                "total_tables": len(tables),
                "tables_needing_vacuum": len(needs_vacuum),
                "tables_needing_analyze": len(needs_analyze),
                "tables": tables,
                "needs_vacuum": needs_vacuum,
                "needs_analyze": needs_analyze,
                "recommendations": [
                    f"Consider running VACUUM on {len(needs_vacuum)} tables with high dead tuple ratios"
                ]
                if needs_vacuum
                else ["Vacuum status looks good"],
                "timestamp": serialize_datetime(datetime.now()),
            }

    except Exception as e:
        logger.error(f"Error checking vacuum/analyze status: {str(e)}")
        return {"error": str(e), "timestamp": serialize_datetime(datetime.now())}
