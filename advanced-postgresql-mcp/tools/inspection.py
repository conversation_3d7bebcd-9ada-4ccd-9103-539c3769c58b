"""Database inspection tools."""

import logging
from datetime import UTC, datetime
from typing import Any

from database import get_db_connection, serialize_datetime
from fastmcp import Context
from security import validate_identifier
from sqlalchemy import inspect

logger = logging.getLogger(__name__)


async def list_schemas(ctx: Context) -> dict[str, Any]:
    """List all database schemas."""
    try:
        engine = get_db_connection()
        inspector = inspect(engine)

        all_schemas = inspector.get_schema_names()
        # Filter out system schemas
        user_schemas = [
            schema
            for schema in all_schemas
            if schema not in ["information_schema", "pg_catalog", "pg_toast"]
        ]

        logger.info(f"Retrieved {len(user_schemas)} user schemas")
        # Import timezone from datetime to create an aware datetime object

        return {
            "schemas": user_schemas,
            "total_count": len(user_schemas),
            "timestamp": serialize_datetime(datetime.now(UTC)),
        }
    except Exception as e:
        logger.error(f"Error listing schemas: {str(e)}")
        return {"error": str(e), "schemas": []}


async def list_tables(ctx: Context, schema: str = "public") -> dict[str, Any]:
    """List tables in a schema."""
    try:
        # Validate schema name
        if not validate_identifier(schema):
            return {"error": f"Invalid schema name: {schema}", "tables": [], "schema": schema}

        engine = get_db_connection()
        inspector = inspect(engine)

        tables = inspector.get_table_names(schema=schema)

        logger.info(f"Retrieved {len(tables)} tables from schema '{schema}'")
        return {
            "schema": schema,
            "tables": tables,
            "total_count": len(tables),
            "timestamp": serialize_datetime(datetime.now(UTC)),
        }
    except Exception as e:
        logger.error(f"Error listing tables in schema '{schema}': {str(e)}")
        return {"error": str(e), "tables": [], "schema": schema}


def _get_table_structure_impl(table_name: str, schema: str = "public") -> dict[str, Any]:
    """Internal implementation for table structure retrieval."""
    try:
        # Validate identifiers
        if not validate_identifier(table_name):
            return {
                "error": f"Invalid table name: {table_name}",
                "table_name": table_name,
                "schema": schema,
            }

        if not validate_identifier(schema):
            return {
                "error": f"Invalid schema name: {schema}",
                "table_name": table_name,
                "schema": schema,
            }

        engine = get_db_connection()
        inspector = inspect(engine)

        structure = {
            "table_name": table_name,
            "schema": schema,
            "columns": [],
            "primary_keys": [],
            "foreign_keys": [],
            "indexes": [],
            "constraints": [],
        }

        # Get columns
        columns = inspector.get_columns(table_name, schema=schema)
        for col in columns:
            structure["columns"].append(
                {
                    "name": col["name"],
                    "type": str(col["type"]),
                    "nullable": col["nullable"],
                    "default": str(col.get("default")) if col.get("default") is not None else None,
                    "autoincrement": col.get("autoincrement", False),
                    "comment": col.get("comment"),
                }
            )

        # Get primary keys
        pk = inspector.get_pk_constraint(table_name, schema=schema)
        structure["primary_keys"] = pk.get("constrained_columns", [])

        # Get foreign keys
        fks = inspector.get_foreign_keys(table_name, schema=schema)
        for fk in fks:
            structure["foreign_keys"].append(
                {
                    "name": fk.get("name"),
                    "constrained_columns": fk["constrained_columns"],
                    "referred_table": fk["referred_table"],
                    "referred_columns": fk["referred_columns"],
                    "referred_schema": fk.get("referred_schema"),
                }
            )

        # Get indexes
        indexes = inspector.get_indexes(table_name, schema=schema)
        for idx in indexes:
            structure["indexes"].append(
                {
                    "name": idx["name"],
                    "columns": idx["column_names"],
                    "unique": idx["unique"],
                }
            )

        # Get check constraints
        try:
            checks = inspector.get_check_constraints(table_name, schema=schema)
            structure["constraints"] = checks
        except Exception as e:
            logger.warning(f"Error getting check constraints for '{schema}.{table_name}': {str(e)}")
            structure["constraints"] = []

        structure["timestamp"] = serialize_datetime(datetime.now(UTC))

        logger.info(f"Retrieved structure for table '{schema}.{table_name}'")
        return structure

    except Exception as e:
        logger.error(f"Error getting table structure for '{schema}.{table_name}': {str(e)}")
        return {"error": str(e), "table_name": table_name, "schema": schema}


async def get_table_structure(
    ctx: Context, table_name: str, schema: str = "public"
) -> dict[str, Any]:
    """Get detailed table structure."""
    return _get_table_structure_impl(table_name, schema)
