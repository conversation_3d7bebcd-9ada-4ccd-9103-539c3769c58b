"""Storage and cost optimization tools."""

import logging
from datetime import datetime
from typing import Any

from database import get_db_connection, serialize_datetime
from fastmcp import Context
from sqlalchemy import text

logger = logging.getLogger(__name__)


async def storage_optimization_report(ctx: Context) -> dict[str, Any]:
    """Identify storage savings opportunities."""
    try:
        engine = get_db_connection()

        # Find largest tables
        large_tables_query = text("""
            SELECT
                schemaname,
                tablename,
                pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
                pg_total_relation_size(schemaname||'.'||tablename) as total_size_bytes,
                pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
                pg_relation_size(schemaname||'.'||tablename) as table_size_bytes,
                pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size,
                (pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size_bytes
            FROM pg_tables
            WHERE schemaname NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
            LIMIT 20
        """)

        # Find tables with high dead tuple ratio
        bloated_tables_query = text("""
            SELECT
                schemaname,
                relname,
                n_live_tup,
                n_dead_tup,
                CASE
                    WHEN n_live_tup + n_dead_tup > 0
                    THEN round((n_dead_tup::numeric / (n_live_tup + n_dead_tup) * 100), 2)
                    ELSE 0
                END as dead_tuple_percent,
                pg_size_pretty(pg_total_relation_size(schemaname||'.'||relname)) as total_size
            FROM pg_stat_user_tables
            WHERE n_live_tup + n_dead_tup > 100
            ORDER BY (n_dead_tup::numeric / (n_live_tup + n_dead_tup)) DESC
            LIMIT 10
        """)

        with engine.connect() as conn:
            large_tables = []
            large_tables_result = conn.execute(large_tables_query).fetchall()
            total_database_size = 0

            for row in large_tables_result:
                table_info = {
                    "schema": row[0],
                    "table": row[1],
                    "total_size": row[2],
                    "total_size_bytes": row[3],
                    "table_size": row[4],
                    "table_size_bytes": row[5],
                    "index_size": row[6],
                    "index_size_bytes": row[7],
                    "index_ratio": round((float(row[7]) / float(row[3])) * 100, 2)
                    if row[3] > 0
                    else 0,
                }
                large_tables.append(table_info)
                total_database_size += row[3]

            bloated_tables = []
            bloated_tables_result = conn.execute(bloated_tables_query).fetchall()

            for row in bloated_tables_result:
                if float(row[4]) > 10:  # Only include tables with >10% dead tuples
                    bloated_tables.append(
                        {
                            "schema": row[0],
                            "table": row[1],
                            "live_tuples": row[2],
                            "dead_tuples": row[3],
                            "dead_tuple_percent": float(row[4]),
                            "total_size": row[5],
                        }
                    )

            # Generate optimization recommendations
            recommendations = []
            potential_savings = 0

            # Check for over-indexed tables
            for table in large_tables:
                if table["index_ratio"] > 50:
                    recommendations.append(
                        f"Table {table['schema']}.{table['table']} has high index overhead ({table['index_ratio']}%)"
                    )
                    potential_savings += (
                        float(table["index_size_bytes"]) * 0.3
                    )  # Estimate 30% savings

            # Check for bloated tables
            for table in bloated_tables:
                recommendations.append(
                    f"Table {table['schema']}.{table['table']} needs VACUUM ({table['dead_tuple_percent']}% dead tuples)"
                )
                # Estimate potential savings from VACUUM
                estimated_bloat_size = (
                    float(total_database_size) * (float(table["dead_tuple_percent"]) / 100) * 0.1
                )
                potential_savings += estimated_bloat_size

            return {
                "total_database_size_bytes": total_database_size,
                "total_database_size": f"{total_database_size / (1024**3):.2f} GB",
                "largest_tables": large_tables,
                "bloated_tables": bloated_tables,
                "recommendations": recommendations,
                "estimated_potential_savings_bytes": int(potential_savings),
                "estimated_potential_savings": f"{potential_savings / (1024**3):.2f} GB",
                "savings_percentage": round((potential_savings / total_database_size) * 100, 2)
                if total_database_size > 0
                else 0,
                "timestamp": serialize_datetime(datetime.now()),
            }

    except Exception as e:
        logger.error(f"Error generating storage optimization report: {str(e)}")
        return {"error": str(e), "timestamp": serialize_datetime(datetime.now())}
