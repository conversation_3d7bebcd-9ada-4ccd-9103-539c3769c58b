"""Database overview and comprehensive analysis tools."""

import logging
from datetime import datetime
from typing import Any

from database import get_db_connection, serialize_datetime
from fastmcp import Context
from sqlalchemy import inspect, text
from tools.statistics import _get_table_stats_impl

logger = logging.getLogger(__name__)


async def get_database_overview(ctx: Context) -> dict[str, Any]:
    """Get comprehensive database overview with performance and security analysis."""
    try:
        engine = get_db_connection()
        inspector = inspect(engine)

        db_info = {
            "timestamp": serialize_datetime(datetime.now()),
            "schemas": {},
            "database_summary": {
                "total_schemas": 0,
                "total_tables": 0,
                "total_size_bytes": 0,
                "total_rows": 0,
            },
            "performance_overview": {},
            "security_overview": {},
            "relationships": {"foreign_keys": [], "relationship_summary": {}},
        }

        # Get database-wide performance metrics first
        with engine.connect() as conn:
            # Database-wide statistics
            db_stats_query = text("""
                SELECT
                    pg_database_size(current_database()) as database_size_bytes,
                    (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
                    (SELECT count(*) FROM pg_stat_activity) as total_connections,
                    current_setting('max_connections')::int as max_connections
            """)

            db_stats = conn.execute(db_stats_query).fetchone()

            if db_stats:
                db_info["performance_overview"] = {
                    "active_connections": db_stats[1],
                    "total_connections": db_stats[2],
                    "max_connections": db_stats[3],
                    "connection_usage_percent": round((db_stats[2] / db_stats[3]) * 100, 2)
                    if db_stats[3] > 0
                    else 0,
                }

        schemas = inspector.get_schema_names()
        user_schemas = [
            s for s in schemas if s not in ["information_schema", "pg_catalog", "pg_toast"]
        ]
        db_info["database_summary"]["total_schemas"] = len(user_schemas)

        # Track top performers for summary
        all_tables_with_stats = []
        # Track relationships across all schemas
        all_relationships = []
        table_connections = {}  # Track how many connections each table has

        for schema in user_schemas:
            logger.info(f"Processing schema: {schema}")

            tables = inspector.get_table_names(schema=schema)
            db_info["database_summary"]["total_tables"] += len(tables)

            schema_info = {
                "table_count": len(tables),
                "total_size_bytes": 0,
                "total_rows": 0,
                "tables": {},
            }

            for table in tables:
                # Get only essential stats, skip detailed structure
                table_stats = _get_table_stats_impl(table, schema)

                # Get foreign key relationships for this table
                try:
                    fks = inspector.get_foreign_keys(table, schema=schema)
                    for fk in fks:
                        relationship = {
                            "from_schema": schema,
                            "from_table": table,
                            "from_columns": fk["constrained_columns"],
                            "to_schema": fk.get("referred_schema") or schema,
                            "to_table": fk["referred_table"],
                            "to_columns": fk["referred_columns"],
                            "constraint_name": fk.get("name"),
                        }
                        all_relationships.append(relationship)

                        # Track connections for both tables
                        from_key = f"{schema}.{table}"
                        to_key = f"{relationship['to_schema']}.{relationship['to_table']}"

                        table_connections[from_key] = table_connections.get(from_key, 0) + 1
                        table_connections[to_key] = table_connections.get(to_key, 0) + 1
                except Exception as e:
                    logger.warning(f"Could not get foreign keys for {schema}.{table}: {e}")

                if "error" not in table_stats:
                    # Extract only key metrics for agent consumption
                    essential_info = {
                        "row_count": table_stats.get("row_count", 0),
                        "size_bytes": table_stats.get("size", {}).get("total_size_bytes", 0),
                        "size_readable": table_stats.get("size", {}).get("total_size", "0 B"),
                    }

                    # Add performance flags for agent decision making
                    perf_metrics = table_stats.get("performance_metrics", {})
                    if perf_metrics:
                        live_tuples = perf_metrics.get("live_tuples", 0)
                        dead_tuples = perf_metrics.get("dead_tuples", 0)
                        seq_scans = perf_metrics.get("sequential_scans", 0)
                        idx_scans = perf_metrics.get("index_scans", 0)
                        total_modifications = (
                            perf_metrics.get("tuples_inserted", 0)
                            + perf_metrics.get("tuples_updated", 0)
                            + perf_metrics.get("tuples_deleted", 0)
                        )

                        # Calculate key ratios for agent insights
                        total_tuples = live_tuples + dead_tuples
                        total_scans = seq_scans + idx_scans

                        essential_info["needs_attention"] = []
                        if total_tuples > 0 and (dead_tuples / total_tuples) > 0.2:
                            essential_info["needs_attention"].append("high_dead_tuples")
                        if total_scans > 0 and (seq_scans / total_scans) > 0.5:
                            essential_info["needs_attention"].append("frequent_seq_scans")
                        if essential_info["row_count"] == 0:
                            essential_info["needs_attention"].append("empty_table")

                        # Store for top tables analysis
                        all_tables_with_stats.append(
                            {
                                "schema": schema,
                                "table": table,
                                "size_bytes": essential_info["size_bytes"],
                                "total_scans": total_scans,
                                "total_modifications": total_modifications,
                            }
                        )

                    schema_info["tables"][table] = essential_info
                    schema_info["total_size_bytes"] += essential_info["size_bytes"]
                    schema_info["total_rows"] += essential_info["row_count"]

                    # Update database totals
                    db_info["database_summary"]["total_size_bytes"] += essential_info["size_bytes"]
                    db_info["database_summary"]["total_rows"] += essential_info["row_count"]
                else:
                    # Minimal error info for agent
                    schema_info["tables"][table] = {"error": "stats_unavailable"}

            db_info["schemas"][schema] = schema_info

        # Add human-readable database size
        total_size_gb = db_info["database_summary"]["total_size_bytes"] / (1024**3)
        db_info["database_summary"]["total_size_readable"] = f"{total_size_gb:.2f} GB"

        # Add top tables summary for agent insights
        if all_tables_with_stats:
            # Top 5 tables by size
            top_by_size = sorted(
                all_tables_with_stats, key=lambda x: x["size_bytes"], reverse=True
            )[:5]
            # Top 5 most active tables
            top_by_activity = sorted(
                all_tables_with_stats, key=lambda x: x["total_scans"], reverse=True
            )[:5]

            db_info["performance_overview"]["top_tables"] = {
                "largest": [
                    {
                        "schema": t["schema"],
                        "table": t["table"],
                        "size_bytes": t["size_bytes"],
                    }
                    for t in top_by_size
                ],
                "most_active": [
                    {
                        "schema": t["schema"],
                        "table": t["table"],
                        "total_scans": t["total_scans"],
                    }
                    for t in top_by_activity
                ],
            }

        # Add security overview
        with engine.connect() as conn:
            security_issues = []
            security_score = 100

            # Check security settings
            security_settings = ["ssl", "log_connections", "password_encryption"]
            settings = {}

            for setting in security_settings:
                try:
                    result = conn.execute(text(f"SHOW {setting}")).fetchone()
                    settings[setting] = result[0] if result else "unknown"
                except:
                    settings[setting] = "not available"

            # Check for pg_stat_statements extension
            ext_query = text(
                "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements')"
            )
            pg_stat_statements_installed = conn.execute(ext_query).fetchone()[0]
            settings["pg_stat_statements_installed"] = pg_stat_statements_installed

            # Security issue detection
            if settings.get("ssl") != "on":
                security_issues.append("ssl_disabled")
                security_score -= 20

            if not pg_stat_statements_installed:
                security_issues.append("no_query_monitoring")
                security_score -= 10

            # Get user security summary
            users_query = text("""
                SELECT
                    COUNT(*) as total_users,
                    COUNT(*) FILTER (WHERE rolsuper = true) as superusers,
                    COUNT(*) FILTER (WHERE rolconnlimit = -1) as unlimited_connections
                FROM pg_roles
                WHERE rolcanlogin = true
            """)

            user_stats = conn.execute(users_query).fetchone()

            if user_stats:
                total_users = user_stats[0]
                superusers = user_stats[1]
                unlimited_conn = user_stats[2]

                if superusers > 1:  # More than just postgres user
                    security_issues.append("multiple_superusers")
                    security_score -= 15

                if unlimited_conn > 0:
                    security_issues.append("unlimited_connections")
                    security_score -= 10

                db_info["security_overview"] = {
                    "security_score": max(0, security_score),
                    "total_users": total_users,
                    "superusers": superusers,
                    "unlimited_connections": unlimited_conn,
                    "security_settings": settings,
                    "security_issues": security_issues,
                    "recommendations": [
                        "Enable SSL encryption" if "ssl_disabled" in security_issues else None,
                        "Install pg_stat_statements for query monitoring"
                        if "no_query_monitoring" in security_issues
                        else None,
                        "Review superuser privileges"
                        if "multiple_superusers" in security_issues
                        else None,
                        "Set connection limits for users"
                        if "unlimited_connections" in security_issues
                        else None,
                    ],
                }

                # Remove None values from recommendations
                db_info["security_overview"]["recommendations"] = [
                    r for r in db_info["security_overview"]["recommendations"] if r is not None
                ]

        # Build relationship summary
        db_info["relationships"]["foreign_keys"] = all_relationships

        if all_relationships:
            # Find most connected tables (hub tables)
            most_connected = sorted(table_connections.items(), key=lambda x: x[1], reverse=True)[:5]

            # Find isolated tables (no relationships)
            all_table_keys = set()
            for schema in user_schemas:
                for table in inspector.get_table_names(schema=schema):
                    all_table_keys.add(f"{schema}.{table}")

            connected_tables = set(table_connections.keys())
            isolated_tables = all_table_keys - connected_tables

            # Relationship patterns analysis
            relationship_patterns = {}
            for rel in all_relationships:
                from_table = f"{rel['from_schema']}.{rel['from_table']}"
                to_table = f"{rel['to_schema']}.{rel['to_table']}"

                # Count references to each table (how many tables reference it)
                if to_table not in relationship_patterns:
                    relationship_patterns[to_table] = {
                        "referenced_by": 0,
                        "references": 0,
                    }
                relationship_patterns[to_table]["referenced_by"] += 1

                # Count references from each table (how many tables it references)
                if from_table not in relationship_patterns:
                    relationship_patterns[from_table] = {
                        "referenced_by": 0,
                        "references": 0,
                    }
                relationship_patterns[from_table]["references"] += 1

            # Find potential hub/lookup tables (highly referenced)
            hub_tables = [
                (table, stats["referenced_by"])
                for table, stats in relationship_patterns.items()
                if stats["referenced_by"] >= 2
            ]
            hub_tables.sort(key=lambda x: x[1], reverse=True)

            db_info["relationships"]["relationship_summary"] = {
                "total_relationships": len(all_relationships),
                "connected_tables": len(connected_tables),
                "isolated_tables": len(isolated_tables),
                "most_connected_tables": [
                    {"table": table, "connections": count} for table, count in most_connected[:5]
                ],
                "hub_tables": [
                    {"table": table, "referenced_by": count} for table, count in hub_tables[:5]
                ],
                "relationship_insights": [],
            }

            # Add insights for agents
            if len(isolated_tables) > 0:
                db_info["relationships"]["relationship_summary"]["relationship_insights"].append(
                    f"{len(isolated_tables)} tables have no foreign key relationships"
                )

            if hub_tables:
                top_hub = hub_tables[0]
                db_info["relationships"]["relationship_summary"]["relationship_insights"].append(
                    f"{top_hub[0]} is the most referenced table ({top_hub[1]} references)"
                )

            if len(all_relationships) == 0:
                db_info["relationships"]["relationship_summary"]["relationship_insights"].append(
                    "No foreign key relationships found - consider adding referential integrity"
                )
        else:
            db_info["relationships"]["relationship_summary"] = {
                "total_relationships": 0,
                "connected_tables": 0,
                "isolated_tables": db_info["database_summary"]["total_tables"],
                "relationship_insights": ["No foreign key relationships found in the database"],
            }

        logger.info(
            f"Database overview complete: {db_info['database_summary']['total_tables']} tables across {len(user_schemas)} schemas, {len(all_relationships)} relationships"
        )
        return db_info

    except Exception as e:
        logger.error(f"Error generating database overview: {str(e)}")
        return {"error": str(e), "timestamp": serialize_datetime(datetime.now())}
