"""Performance monitoring and analysis tools."""

import logging
from datetime import datetime
from typing import Any

from database import get_db_connection, serialize_datetime
from fastmcp import Context
from security import sanitize_limit, validate_identifier
from sqlalchemy import text

logger = logging.getLogger(__name__)


async def get_slow_queries(ctx: Context, limit: int = 10) -> dict[str, Any]:
    """Identify slow-running queries from pg_stat_statements."""
    try:
        # Sanitize the limit parameter
        limit = sanitize_limit(limit, default=10, max_limit=50)

        engine = get_db_connection()

        # First check if pg_stat_statements extension is installed
        with engine.connect() as conn:
            ext_check = text(
                "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements')"
            )
            extension_installed = conn.execute(ext_check).fetchone()[0]

            if not extension_installed:
                return {
                    "error": "pg_stat_statements extension is not installed",
                    "note": "To install: CREATE EXTENSION pg_stat_statements; (requires restart and shared_preload_libraries setting)",
                    "slow_queries": [],
                    "total_found": 0,
                    "timestamp": serialize_datetime(datetime.now()),
                }

            query = text("""
                SELECT
                    query,
                    calls,
                    total_exec_time,
                    mean_exec_time,
                    stddev_exec_time,
                    rows,
                    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
                FROM pg_stat_statements
                WHERE query NOT LIKE '%pg_stat_statements%'
                ORDER BY total_exec_time DESC
                LIMIT :limit
            """)

            result = conn.execute(query, {"limit": limit})

            slow_queries = []
            for row in result:
                slow_queries.append(
                    {
                        "query": row[0],
                        "calls": row[1],
                        "total_exec_time_ms": float(row[2]) if row[2] else 0,
                        "mean_exec_time_ms": float(row[3]) if row[3] else 0,
                        "stddev_exec_time_ms": float(row[4]) if row[4] else 0,
                        "rows_returned": row[5],
                        "cache_hit_percent": float(row[6]) if row[6] else 0,
                    }
                )

            return {
                "slow_queries": slow_queries,
                "total_found": len(slow_queries),
                "timestamp": serialize_datetime(datetime.now()),
            }

    except Exception as e:
        logger.error(f"Error getting slow queries: {str(e)}")
        return {
            "error": str(e),
            "note": "pg_stat_statements extension may not be installed or accessible",
            "slow_queries": [],
            "total_found": 0,
            "timestamp": serialize_datetime(datetime.now()),
        }


async def get_blocking_queries(ctx: Context) -> dict[str, Any]:
    """Find queries causing locks/blocks."""
    try:
        engine = get_db_connection()

        query = text("""
            SELECT
                blocked_locks.pid AS blocked_pid,
                blocked_activity.usename AS blocked_user,
                blocking_locks.pid AS blocking_pid,
                blocking_activity.usename AS blocking_user,
                blocked_activity.query AS blocked_statement,
                blocking_activity.query AS current_statement_in_blocking_process,
                blocked_activity.application_name AS blocked_application,
                blocking_activity.application_name AS blocking_application
            FROM pg_catalog.pg_locks blocked_locks
            JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
            JOIN pg_catalog.pg_locks blocking_locks
                ON blocking_locks.locktype = blocked_locks.locktype
                AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
                AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
                AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
                AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
                AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
                AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
                AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
                AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
                AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
                AND blocking_locks.pid != blocked_locks.pid
            JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
            WHERE NOT blocked_locks.granted;
        """)

        with engine.connect() as conn:
            result = conn.execute(query)

            blocking_queries = []
            for row in result:
                blocking_queries.append(
                    {
                        "blocked_pid": row[0],
                        "blocked_user": row[1],
                        "blocking_pid": row[2],
                        "blocking_user": row[3],
                        "blocked_query": row[4],
                        "blocking_query": row[5],
                        "blocked_application": row[6],
                        "blocking_application": row[7],
                    }
                )

            return {
                "blocking_queries": blocking_queries,
                "total_blocks": len(blocking_queries),
                "timestamp": serialize_datetime(datetime.now()),
            }

    except Exception as e:
        logger.error(f"Error getting blocking queries: {str(e)}")
        return {"error": str(e), "timestamp": serialize_datetime(datetime.now())}


async def analyze_table_usage(
    ctx: Context, table_name: str, schema: str = "public"
) -> dict[str, Any]:
    """Detailed table usage patterns and optimization suggestions."""
    try:
        if not validate_identifier(table_name) or not validate_identifier(schema):
            return {"error": "Invalid table or schema name"}

        engine = get_db_connection()

        # Get detailed table statistics
        usage_query = text("""
            SELECT
                schemaname,
                relname,
                seq_scan,
                seq_tup_read,
                idx_scan,
                idx_tup_fetch,
                n_tup_ins,
                n_tup_upd,
                n_tup_del,
                n_tup_hot_upd,
                n_live_tup,
                n_dead_tup,
                vacuum_count,
                autovacuum_count,
                analyze_count,
                autoanalyze_count,
                last_vacuum,
                last_autovacuum,
                last_analyze,
                last_autoanalyze
            FROM pg_stat_user_tables
            WHERE relname = :table_name AND schemaname = :schema_name
        """)

        # Get index usage for this table
        index_query = text("""
            SELECT
                indexrelname,
                idx_scan,
                idx_tup_read,
                idx_tup_fetch
            FROM pg_stat_user_indexes
            WHERE relname = :table_name AND schemaname = :schema_name
            ORDER BY idx_scan DESC
        """)

        with engine.connect() as conn:
            # Table usage stats
            usage_result = conn.execute(
                usage_query, {"table_name": table_name, "schema_name": schema}
            ).fetchone()

            # Index usage stats
            index_result = conn.execute(
                index_query, {"table_name": table_name, "schema_name": schema}
            ).fetchall()

            if not usage_result:
                return {
                    "error": f"Table {schema}.{table_name} not found or no statistics available"
                }

            # Calculate ratios and recommendations
            seq_scan = usage_result[2] or 0
            idx_scan = usage_result[4] or 0
            total_scans = seq_scan + idx_scan

            recommendations = []

            # Analyze scan patterns
            if total_scans > 0:
                seq_scan_ratio = (seq_scan / total_scans) * 100
                if seq_scan_ratio > 50:
                    recommendations.append("High sequential scan ratio - consider adding indexes")

            # Analyze dead tuples
            live_tup = usage_result[10] or 0
            dead_tup = usage_result[11] or 0
            if live_tup > 0:
                dead_ratio = (dead_tup / (live_tup + dead_tup)) * 100
                if dead_ratio > 20:
                    recommendations.append("High dead tuple ratio - consider running VACUUM")

            # Analyze HOT updates
            total_updates = usage_result[7] or 0
            hot_updates = usage_result[9] or 0
            if total_updates > 0:
                hot_ratio = (hot_updates / total_updates) * 100
                if hot_ratio < 50:
                    recommendations.append(
                        "Low HOT update ratio - consider reducing page fillfactor"
                    )

            # Index usage analysis
            unused_indexes = []
            for idx_row in index_result:
                if (idx_row[1] or 0) == 0:  # idx_scan = 0
                    unused_indexes.append(idx_row[0])

            if unused_indexes:
                recommendations.append(f"Unused indexes detected: {', '.join(unused_indexes)}")

            return {
                "table_usage": {
                    "schema": usage_result[0],
                    "table": usage_result[1],
                    "sequential_scans": seq_scan,
                    "sequential_tuples_read": usage_result[3],
                    "index_scans": idx_scan,
                    "index_tuples_fetched": usage_result[5],
                    "tuples_inserted": usage_result[6],
                    "tuples_updated": usage_result[7],
                    "tuples_deleted": usage_result[8],
                    "hot_updates": usage_result[9],
                    "live_tuples": live_tup,
                    "dead_tuples": dead_tup,
                    "vacuum_count": usage_result[12],
                    "autovacuum_count": usage_result[13],
                    "analyze_count": usage_result[14],
                    "autoanalyze_count": usage_result[15],
                    "last_vacuum": usage_result[16].isoformat() if usage_result[16] else None,
                    "last_autovacuum": usage_result[17].isoformat() if usage_result[17] else None,
                    "last_analyze": usage_result[18].isoformat() if usage_result[18] else None,
                    "last_autoanalyze": usage_result[19].isoformat() if usage_result[19] else None,
                },
                "index_usage": [
                    {
                        "index_name": row[0],
                        "scans": row[1],
                        "tuples_read": row[2],
                        "tuples_fetched": row[3],
                    }
                    for row in index_result
                ],
                "analysis": {
                    "sequential_scan_ratio": round((seq_scan / total_scans) * 100, 2)
                    if total_scans > 0
                    else 0,
                    "dead_tuple_ratio": round((dead_tup / (live_tup + dead_tup)) * 100, 2)
                    if (live_tup + dead_tup) > 0
                    else 0,
                    "hot_update_ratio": round((hot_updates / total_updates) * 100, 2)
                    if total_updates > 0
                    else 0,
                    "total_scans": total_scans,
                },
                "recommendations": recommendations,
                "timestamp": serialize_datetime(datetime.now()),
            }

    except Exception as e:
        logger.error(f"Error analyzing table usage: {str(e)}")
        return {"error": str(e), "timestamp": serialize_datetime(datetime.now())}
