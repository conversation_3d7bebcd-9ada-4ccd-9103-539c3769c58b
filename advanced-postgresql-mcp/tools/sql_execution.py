"""SQL execution and query analysis tools."""

import logging
from datetime import datetime
from typing import Any

from database import get_db_connection, serialize_datetime
from fastmcp import Context
from security import is_safe_query
from sqlalchemy import text

logger = logging.getLogger(__name__)


async def execute_sql(ctx: Context, query: str, read_only: bool = True) -> dict[str, Any]:
    """Execute SQL queries safely with read-only protection."""
    try:
        if read_only and not is_safe_query(query):
            return {
                "error": "Query not allowed. Only SELECT, EXPLAIN, SHOW, DESCRIBE queries are permitted in read-only mode.",
                "query": query[:100] + "..." if len(query) > 100 else query,
            }

        engine = get_db_connection()
        with engine.connect() as conn:
            result = conn.execute(text(query))

            if result.returns_rows:
                columns = result.keys()
                rows = []
                row_count = 0

                for row in result:
                    if row_count >= 1000:  # Limit to 1000 rows for safety
                        break
                    row_dict = {}
                    for i, col in enumerate(columns):
                        value = row[i]
                        # Use the centralized serialize_datetime function
                        if isinstance(value, datetime):
                            value = serialize_datetime(value)
                        row_dict[col] = value
                    rows.append(row_dict)
                    row_count += 1

                return {
                    "success": True,
                    "columns": list(columns),
                    "rows": rows,
                    "row_count": row_count,
                    "query": query,
                    "timestamp": serialize_datetime(datetime.now()),
                }
            else:
                return {
                    "success": True,
                    "message": "Query executed successfully (no rows returned)",
                    "query": query,
                    "timestamp": serialize_datetime(datetime.now()),
                }

    except Exception as e:
        logger.error(f"Error executing SQL query: {str(e)}")
        return {
            "error": str(e),
            "query": query[:100] + "..." if len(query) > 100 else query,
            "timestamp": serialize_datetime(datetime.now()),
        }


async def explain_query(ctx: Context, query: str, analyze: bool = False) -> dict[str, Any]:
    """Get query execution plan for performance analysis."""
    try:
        if not is_safe_query(query):
            return {
                "error": "Only SELECT queries can be explained",
                "query": query[:100] + "..." if len(query) > 100 else query,
            }

        engine = get_db_connection()
        explain_cmd = (
            "EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)" if analyze else "EXPLAIN (FORMAT JSON)"
        )

        with engine.connect() as conn:
            result = conn.execute(text(f"{explain_cmd} {query}"))
            plan = result.fetchone()[0]

            return {
                "success": True,
                "execution_plan": plan,
                "query": query,
                "analyzed": analyze,
                "timestamp": serialize_datetime(datetime.now()),
            }

    except Exception as e:
        logger.error(f"Error explaining query: {str(e)}")
        return {
            "error": str(e),
            "query": query[:100] + "..." if len(query) > 100 else query,
            "timestamp": serialize_datetime(datetime.now()),
        }


async def query_cost_analysis(ctx: Context, query: str) -> dict[str, Any]:
    """Analyze query resource costs."""
    try:
        if not is_safe_query(query):
            return {"error": "Only SELECT queries can be analyzed for cost"}

        engine = get_db_connection()

        with engine.connect() as conn:
            # Get detailed execution plan with costs
            explain_result = conn.execute(
                text(f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}")
            ).fetchone()
            plan = explain_result[0][0]

            # Extract cost information
            execution_time = plan.get("Execution Time", 0)
            planning_time = plan.get("Planning Time", 0)

            def extract_node_costs(node):
                costs = {
                    "startup_cost": node.get("Startup Cost", 0),
                    "total_cost": node.get("Total Cost", 0),
                    "actual_time": node.get("Actual Total Time", 0),
                    "rows": node.get("Actual Rows", 0),
                    "buffers_hit": node.get("Shared Hit Blocks", 0),
                    "buffers_read": node.get("Shared Read Blocks", 0),
                    "buffers_written": node.get("Shared Written Blocks", 0),
                }

                # Recursively process child plans
                if "Plans" in node:
                    costs["child_plans"] = [extract_node_costs(child) for child in node["Plans"]]

                return costs

            root_costs = extract_node_costs(plan["Plan"])

            # Calculate resource usage
            total_buffers = root_costs["buffers_hit"] + root_costs["buffers_read"]
            cache_hit_ratio = (
                (root_costs["buffers_hit"] / total_buffers * 100) if total_buffers > 0 else 0
            )

            # Cost analysis and recommendations
            recommendations = []
            cost_score = "low"

            if execution_time > 1000:  # > 1 second
                cost_score = "high"
                recommendations.append("Query execution time is high - consider optimization")
            elif execution_time > 100:  # > 100ms
                cost_score = "medium"
                recommendations.append("Query execution time could be improved")

            if cache_hit_ratio < 95:
                recommendations.append(
                    "Low buffer cache hit ratio - query may benefit from indexing"
                )

            if root_costs["total_cost"] > 10000:
                recommendations.append(
                    "High estimated cost - review query plan for expensive operations"
                )

            return {
                "query": query[:200] + "..." if len(query) > 200 else query,
                "execution_time_ms": execution_time,
                "planning_time_ms": planning_time,
                "cost_analysis": {
                    "startup_cost": root_costs["startup_cost"],
                    "total_cost": root_costs["total_cost"],
                    "actual_rows": root_costs["rows"],
                    "buffers_hit": root_costs["buffers_hit"],
                    "buffers_read": root_costs["buffers_read"],
                    "cache_hit_ratio": round(cache_hit_ratio, 2),
                },
                "cost_score": cost_score,
                "recommendations": recommendations,
                "full_execution_plan": plan,
                "timestamp": serialize_datetime(datetime.now()),
            }

    except Exception as e:
        logger.error(f"Error analyzing query cost: {str(e)}")
        return {"error": str(e), "timestamp": serialize_datetime(datetime.now())}
