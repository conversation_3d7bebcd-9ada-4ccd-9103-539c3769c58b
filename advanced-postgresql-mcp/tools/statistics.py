"""Database statistics and performance tools."""

import logging
from datetime import datetime
from typing import Any

from database import get_db_connection, serialize_datetime
from fastmcp import Context
from security import validate_identifier
from sqlalchemy import text

logger = logging.getLogger(__name__)


def _get_table_stats_impl(table_name: str, schema: str = "public") -> dict[str, Any]:
    """Internal implementation for table stats retrieval."""
    try:
        # Validate identifiers
        if not validate_identifier(table_name):
            return {
                "error": f"Invalid table name: {table_name}",
                "table_name": table_name,
                "schema": schema,
            }

        if not validate_identifier(schema):
            return {
                "error": f"Invalid schema name: {schema}",
                "table_name": table_name,
                "schema": schema,
            }

        engine = get_db_connection()

        with engine.connect() as conn:
            # Use parameterized queries for safety
            # Row count - using quoted identifiers to prevent SQL injection
            count_query = text(f'SELECT COUNT(*) FROM "{schema}"."{table_name}"')
            row_count = conn.execute(count_query).scalar()

            # Table size and performance metrics (PostgreSQL specific)
            stats_query = text("""
                SELECT
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes,
                    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
                    pg_relation_size(schemaname||'.'||tablename) as table_size_bytes,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size,
                    (pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size_bytes
                FROM pg_tables
                WHERE tablename = :table_name
                AND schemaname = :schema_name
            """)

            schema_name = schema or "public"
            size_result = conn.execute(
                stats_query, {"table_name": table_name, "schema_name": schema_name}
            ).fetchone()

            # Additional performance metrics
            perf_query = text("""
                SELECT
                    seq_scan,
                    seq_tup_read,
                    idx_scan,
                    idx_tup_fetch,
                    n_tup_ins,
                    n_tup_upd,
                    n_tup_del,
                    n_tup_hot_upd,
                    n_live_tup,
                    n_dead_tup,
                    vacuum_count,
                    autovacuum_count,
                    analyze_count,
                    autoanalyze_count
                FROM pg_stat_user_tables
                WHERE relname = :table_name
                AND schemaname = :schema_name
            """)

            perf_result = conn.execute(
                perf_query, {"table_name": table_name, "schema_name": schema_name}
            ).fetchone()

            stats = {
                "table_name": table_name,
                "schema": schema,
                "row_count": row_count,
                "size": {
                    "total_size": size_result[0] if size_result else "Unknown",
                    "total_size_bytes": size_result[1] if size_result else 0,
                    "table_size": size_result[2] if size_result else "Unknown",
                    "table_size_bytes": size_result[3] if size_result else 0,
                    "index_size": size_result[4] if size_result else "Unknown",
                    "index_size_bytes": size_result[5] if size_result else 0,
                },
                "performance_metrics": {
                    "sequential_scans": perf_result[0] if perf_result else 0,
                    "sequential_tuples_read": perf_result[1] if perf_result else 0,
                    "index_scans": perf_result[2] if perf_result else 0,
                    "index_tuples_fetched": perf_result[3] if perf_result else 0,
                    "tuples_inserted": perf_result[4] if perf_result else 0,
                    "tuples_updated": perf_result[5] if perf_result else 0,
                    "tuples_deleted": perf_result[6] if perf_result else 0,
                    "hot_updates": perf_result[7] if perf_result else 0,
                    "live_tuples": perf_result[8] if perf_result else 0,
                    "dead_tuples": perf_result[9] if perf_result else 0,
                    "vacuum_count": perf_result[10] if perf_result else 0,
                    "autovacuum_count": perf_result[11] if perf_result else 0,
                    "analyze_count": perf_result[12] if perf_result else 0,
                    "autoanalyze_count": perf_result[13] if perf_result else 0,
                }
                if perf_result
                else {},
                "timestamp": serialize_datetime(datetime.now()),
            }

            logger.info(f"Retrieved statistics for table '{schema}.{table_name}'")
            return stats

    except Exception as e:
        logger.error(f"Error getting table stats for '{schema}.{table_name}': {str(e)}")
        return {"error": str(e), "table_name": table_name, "schema": schema}


async def get_table_stats(ctx: Context, table_name: str, schema: str = "public") -> dict[str, Any]:
    """Get table statistics and performance metrics."""
    return _get_table_stats_impl(table_name, schema)
