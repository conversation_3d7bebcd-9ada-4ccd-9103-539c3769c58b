# CloudThinker Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                      CloudThinker Platform                                      │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘
                                                │
                                                ▼
┌─────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                        User Interface Layer                                     │
│                                                                                                 │
│  ┌───────────────┐   ┌───────────────────────────────────────────────────────────────────────┐  │
│  │               │   │                           Frontend (Next.js)                          │  │
│  │ Slack         │   │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │  │
│  │ Integration   │◄──►│  │ Dashboard   │  │ Auth        │  │ Chat        │  │ Shared      │  │  │
│  │ Service       │   │  │ Components  │  │ Components  │  │ Components  │  │ Components  │  │  │
│  │               │   │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │  │
│  └───────────────┘   └───────────────────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘
                                                │
                                                ▼
┌─────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                       Application Layer                                          │
│                                                                                                 │
│  ┌───────────────┐   ┌───────────────┐   ┌───────────────┐   ┌───────────────┐   ┌───────────┐  │
│  │ Backend API   │   │ Executor      │   │ Payment       │   │ Scheduler     │   │ Worker    │  │
│  │ Service       │   │ Service       │   │ Service       │   │ Service       │   │ Service   │  │
│  │ (FastAPI)     │   │ (AI Agents)   │   │ (Stripe)      │   │ (Celery Beat) │   │ (Celery)  │  │
│  └───────┬───────┘   └───────┬───────┘   └───────┬───────┘   └───────┬───────┘   └─────┬─────┘  │
│          │                   │                   │                   │                 │        │
│          └───────────────────┴───────────────────┴───────────────────┴─────────────────┘        │
│                                                │                                                │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘
                                                │
                                                ▼
┌─────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                       Data Storage Layer                                         │
│                                                                                                 │
│  ┌───────────────┐   ┌───────────────┐   ┌───────────────┐   ┌───────────────┐   ┌───────────┐  │
│  │ PostgreSQL    │   │ Redis         │   │ MinIO         │   │ Qdrant        │   │ Flower    │  │
│  │ Database      │   │ Cache         │   │ Object        │   │ Vector        │   │ Celery    │  │
│  │               │   │ & Message     │   │ Storage       │   │ Database      │   │ Monitor   │  │
│  │               │   │ Broker        │   │               │   │               │   │           │  │
│  └───────────────┘   └───────────────┘   └───────────────┘   └───────────────┘   └───────────┘  │
│                                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘
                                                │
                                                ▼
┌─────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                      Monitoring & DevOps                                         │
│                                                                                                 │
│  ┌───────────────┐   ┌───────────────┐                                                          │
│  │ Dozzle        │   │ Adminer       │                                                          │
│  │ Log Viewer    │   │ DB Admin      │                                                          │
│  │               │   │               │                                                          │
│  └───────────────┘   └───────────────┘                                                          │
│                                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘
```

## Architecture Components

### User Interface Layer
- **Frontend**: Next.js-based web application with dashboard, authentication, and chat components
- **Slack Integration**: Service that connects CloudThinker with Slack workspaces

### Application Layer
- **Backend API Service**: FastAPI-based REST API handling core business logic
- **Executor Service**: Manages AI-powered agents for cloud infrastructure optimization
- **Payment Service**: Handles subscription and payment processing via Stripe
- **Scheduler Service**: Celery Beat-based service for scheduled tasks
- **Worker Service**: Celery workers for asynchronous task processing

### Data Storage Layer
- **PostgreSQL**: Primary relational database
- **Redis**: Cache and message broker for Celery
- **MinIO**: S3-compatible object storage
- **Qdrant**: Vector database for AI embeddings and similarity search
- **Flower**: Celery monitoring and management tool

### Monitoring & DevOps
- **Dozzle**: Docker container log viewer
- **Adminer**: Database management interface

## Core Features

1. **Operation Agent**: AI-powered agents for infrastructure management
2. **Cloud FinOps**: Data-driven financial visibility and optimization
3. **Cost Optimization**: Continuous analysis and optimization of cloud resources
4. **Automation Workflow**: Seamless, repeatable automation workflows

## Deployment

The entire application is containerized using Docker and orchestrated with Docker Compose, making it easy to deploy in various environments.
