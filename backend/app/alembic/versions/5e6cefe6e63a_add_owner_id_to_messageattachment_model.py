"""Add owner_id to MessageAttachment model

Revision ID: 5e6cefe6e63a
Revises: d9bfb00f4b1f
Create Date: 2025-07-04 10:35:08.439761

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5e6cefe6e63a'
down_revision = 'd9bfb00f4b1f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('message_attachments', sa.Column('owner_id', sa.Uuid(), nullable=False))
    op.alter_column('message_attachments', 'message_id',
               existing_type=sa.UUID(),
               nullable=True)
    op.create_index(op.f('ix_message_attachments_owner_id'), 'message_attachments', ['owner_id'], unique=False)
    op.create_foreign_key(None, 'message_attachments', 'user', ['owner_id'], ['id'])
    op.drop_column('message_attachments', 'upload_status')
    op.drop_column('message_attachments', 'security_scan_status')
    op.drop_column('message_attachments', 'bucket_name')
    op.drop_column('message_attachments', 'attachment_metadata')
    op.drop_column('message_attachments', 'file_hash')
    op.drop_column('message_attachments', 'processing_status')
    op.drop_column('message_attachments', 'ai_analysis')
    sa.Enum('PENDING', 'SCANNING', 'CLEAN', 'QUARANTINED', 'BLOCKED', name='securityscanstatus').drop(op.get_bind())
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('PENDING', 'SCANNING', 'CLEAN', 'QUARANTINED', 'BLOCKED', name='securityscanstatus').create(op.get_bind())
    op.add_column('message_attachments', sa.Column('ai_analysis', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('message_attachments', sa.Column('processing_status', postgresql.ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', name='asynctaskstatus', create_type=False), autoincrement=False, nullable=False))
    op.add_column('message_attachments', sa.Column('file_hash', sa.VARCHAR(length=128), autoincrement=False, nullable=False))
    op.add_column('message_attachments', sa.Column('attachment_metadata', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('message_attachments', sa.Column('bucket_name', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.add_column('message_attachments', sa.Column('security_scan_status', postgresql.ENUM('PENDING', 'SCANNING', 'CLEAN', 'QUARANTINED', 'BLOCKED', name='securityscanstatus', create_type=False), autoincrement=False, nullable=False))
    op.add_column('message_attachments', sa.Column('upload_status', postgresql.ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', name='uploadstatus', create_type=False), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'message_attachments', type_='foreignkey')
    op.drop_index(op.f('ix_message_attachments_owner_id'), table_name='message_attachments')
    op.alter_column('message_attachments', 'message_id',
               existing_type=sa.UUID(),
               nullable=False)
    op.drop_column('message_attachments', 'owner_id')
    # ### end Alembic commands ###
