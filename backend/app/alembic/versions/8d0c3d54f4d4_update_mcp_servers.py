"""update_mcp_servers

Revision ID: 8d0c3d54f4d4
Revises: a726acf48b33
Create Date: 2025-06-19 15:07:50.913606

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '8d0c3d54f4d4'
down_revision = 'a726acf48b33'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.<PERSON>um('CONNECTED', 'ERROR', name='mcpserverstatus').create(op.get_bind())
    sa.Enum('STREAMABLE_HTTP', 'SSE', name='mcpservertransport').create(op.get_bind())
    op.create_table('mcpserver',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('prefix', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('type', postgresql.ENUM('STREAMABLE_HTTP', 'SSE', name='mcpservertransport', create_type=False), nullable=False),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_builtin', sa.Boolean(), nullable=False),
    sa.Column('tool_list', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('tool_permissions', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('tool_enabled', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('status', postgresql.ENUM('CONNECTED', 'ERROR', name='mcpserverstatus', create_type=False), nullable=False),
    sa.Column('status_message', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('status_updated_at', sa.DateTime(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_mcpserver_is_active'), 'mcpserver', ['is_active'], unique=False)
    op.create_index(op.f('ix_mcpserver_is_builtin'), 'mcpserver', ['is_builtin'], unique=False)
    op.create_index(op.f('ix_mcpserver_workspace_id'), 'mcpserver', ['workspace_id'], unique=False)
    op.drop_table('mcpconfig')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('mcpconfig',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('workspace_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('config', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], name='mcpconfig_workspace_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='mcpconfig_pkey'),
    sa.UniqueConstraint('workspace_id', name='mcpconfig_workspace_id_key')
    )
    op.drop_index(op.f('ix_mcpserver_workspace_id'), table_name='mcpserver')
    op.drop_index(op.f('ix_mcpserver_is_builtin'), table_name='mcpserver')
    op.drop_index(op.f('ix_mcpserver_is_active'), table_name='mcpserver')
    op.drop_table('mcpserver')
    sa.Enum('STREAMABLE_HTTP', 'SSE', name='mcpservertransport').drop(op.get_bind())
    sa.Enum('CONNECTED', 'ERROR', name='mcpserverstatus').drop(op.get_bind())
    # ### end Alembic commands ###
