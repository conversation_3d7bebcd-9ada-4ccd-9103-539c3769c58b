"""add_message_indexing

Revision ID: a726acf48b33
Revises: db745f7c4ad4
Create Date: 2025-06-13 09:00:28.062402

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'a726acf48b33'
down_revision = 'db745f7c4ad4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_message_action_type'), 'message', ['action_type'], unique=False)
    op.create_index(op.f('ix_message_conversation_id'), 'message', ['conversation_id'], unique=False)
    op.create_index(op.f('ix_message_created_at'), 'message', ['created_at'], unique=False)
    op.create_index(op.f('ix_message_is_deleted'), 'message', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_message_is_interrupt'), 'message', ['is_interrupt'], unique=False)
    op.create_index(op.f('ix_message_role'), 'message', ['role'], unique=False)
    op.create_index(op.f('ix_message_updated_at'), 'message', ['updated_at'], unique=False)
    op.create_index(op.f('ix_messageagentthought_message_id'), 'messageagentthought', ['message_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_messageagentthought_message_id'), table_name='messageagentthought')
    op.drop_index(op.f('ix_message_updated_at'), table_name='message')
    op.drop_index(op.f('ix_message_role'), table_name='message')
    op.drop_index(op.f('ix_message_is_interrupt'), table_name='message')
    op.drop_index(op.f('ix_message_is_deleted'), table_name='message')
    op.drop_index(op.f('ix_message_created_at'), table_name='message')
    op.drop_index(op.f('ix_message_conversation_id'), table_name='message')
    op.drop_index(op.f('ix_message_action_type'), table_name='message')
    # ### end Alembic commands ###
