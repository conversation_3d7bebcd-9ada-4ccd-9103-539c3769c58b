"""add new builtin connector scripts cli

Revision ID: d9bfb00f4b1f
Revises: fe6d21358ee4
Create Date: 2025-07-02 10:14:18.506662

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlmodel import Session, select
from datetime import datetime
import uuid


# revision identifiers, used by Alembic.
revision = 'd9bfb00f4b1f'
down_revision = 'fe6d21358ee4'
branch_labels = None
depends_on = None

# New builtin connector definition
NEW_CONNECTOR = {
    "name": "cli_script_execution",
    "display_name": "CLI Script Execution",
    "description": "Built-in connector for executing CLI scripts",
    "default_required_permission": True,
}


def upgrade():
    """Add the new cli_script_execution builtin connector and associate it with existing workspaces."""

    # Get database connection
    connection = op.get_bind()
    session = Session(connection)

    try:
        # Check if the connector already exists
        existing_connector = connection.execute(
            sa.text("SELECT id FROM builtinconnector WHERE name = :name"),
            {"name": NEW_CONNECTOR["name"]}
        ).fetchone()

        if existing_connector:
            print(f"Connector '{NEW_CONNECTOR['name']}' already exists, skipping creation")
            connector_id = existing_connector[0]
        else:
            # Insert new builtin connector
            connector_id = str(uuid.uuid4())
            connection.execute(
                sa.text("""
                    INSERT INTO builtinconnector (id, name, display_name, description, default_required_permission, created_at, updated_at)
                    VALUES (:id, :name, :display_name, :description, :default_required_permission, :created_at, :updated_at)
                """),
                {
                    "id": connector_id,
                    "name": NEW_CONNECTOR["name"],
                    "display_name": NEW_CONNECTOR["display_name"],
                    "description": NEW_CONNECTOR["description"],
                    "default_required_permission": NEW_CONNECTOR["default_required_permission"],
                    "created_at": datetime.now(),
                    "updated_at": datetime.now(),
                }
            )
            print(f"Created new builtin connector: {NEW_CONNECTOR['name']}")

        # Get all existing workspaces
        workspaces = connection.execute(
            sa.text("SELECT id FROM workspace WHERE is_deleted = false")
        ).fetchall()

        # Associate the connector with each workspace
        for workspace in workspaces:
            workspace_id = workspace[0]

            # Check if association already exists
            existing_association = connection.execute(
                sa.text("""
                    SELECT id FROM workspacebuiltinconnector
                    WHERE workspace_id = :workspace_id AND connector_id = :connector_id
                """),
                {"workspace_id": workspace_id, "connector_id": connector_id}
            ).fetchone()

            if not existing_association:
                # Create workspace-connector association
                association_id = str(uuid.uuid4())
                connection.execute(
                    sa.text("""
                        INSERT INTO workspacebuiltinconnector (id, workspace_id, connector_id, is_active, required_permission)
                        VALUES (:id, :workspace_id, :connector_id, :is_active, :required_permission)
                    """),
                    {
                        "id": association_id,
                        "workspace_id": workspace_id,
                        "connector_id": connector_id,
                        "is_active": True,
                        "required_permission": NEW_CONNECTOR["default_required_permission"],
                    }
                )
                print(f"Associated connector with workspace: {workspace_id}")

        # Commit the transaction
        connection.commit()
        print("Successfully added cli_script_execution builtin connector")

    except Exception as e:
        connection.rollback()
        print(f"Error adding builtin connector: {str(e)}")
        raise
    finally:
        session.close()


def downgrade():
    """Remove the cli_script_execution builtin connector and its associations."""

    connection = op.get_bind()

    try:
        # Get the connector ID
        connector_result = connection.execute(
            sa.text("SELECT id FROM builtinconnector WHERE name = :name"),
            {"name": NEW_CONNECTOR["name"]}
        ).fetchone()

        if not connector_result:
            print(f"Connector '{NEW_CONNECTOR['name']}' not found, nothing to remove")
            return

        connector_id = connector_result[0]

        # Remove workspace-connector associations
        connection.execute(
            sa.text("DELETE FROM workspacebuiltinconnector WHERE connector_id = :connector_id"),
            {"connector_id": connector_id}
        )
        print("Removed workspace associations")

        # Remove agent-connector associations (if any)
        connection.execute(
            sa.text("""
                DELETE FROM agentconnectorbuiltinconnector
                WHERE workspace_builtin_connector_id IN (
                    SELECT id FROM workspacebuiltinconnector WHERE connector_id = :connector_id
                )
            """),
            {"connector_id": connector_id}
        )

        # Remove the builtin connector
        connection.execute(
            sa.text("DELETE FROM builtinconnector WHERE id = :connector_id"),
            {"connector_id": connector_id}
        )
        print(f"Removed builtin connector: {NEW_CONNECTOR['name']}")

        # Commit the transaction
        connection.commit()
        print("Successfully removed cli_script_execution builtin connector")

    except Exception as e:
        connection.rollback()
        print(f"Error removing builtin connector: {str(e)}")
        raise
