"""refactor agent connector

Revision ID: e1165976a953
Revises: 8d0c3d54f4d4
Create Date: 2025-06-20 09:30:33.170312

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'e1165976a953'
down_revision = '8d0c3d54f4d4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Clear all mcp_servers values in AgentConnector table
    connection = op.get_bind()

    # Update all rows to set mcp_servers to empty array
    connection.execute(
        sa.text("UPDATE agentconnector SET mcp_servers = '{}' WHERE mcp_servers IS NOT NULL")
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Note: This downgrade cannot restore the original mcp_servers values
    # as they were not backed up. This is a data-destructive migration.
    # If needed, restore from backup or manual intervention is required.
    pass
    # ### end Alembic commands ###
