from uuid import UUID

from celery.result import As<PERSON><PERSON><PERSON><PERSON>
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import CurrentUser, get_async_session
from app.logger import logger
from app.schemas.message_attachment import (
    AttachmentConfirmRequest,
    AttachmentDownloadResponse,
    AttachmentPresignedUrlRequest,
    AttachmentPresignedUrlResponse,
    TaskStatusResponse,
)
from app.services.attachment_service import AttachmentService
from app.tasks.attachment_tasks import validate_attachment_task

router = APIRouter()


@router.post("/attachments/presigned-urls", response_model=AttachmentPresignedUrlResponse)
async def generate_attachment_presigned_urls(
    request: AttachmentPresignedUrlRequest,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
):
    """
    Generate presigned URLs for file uploads.
    Clients can use these URLs to upload files directly to the object storage.
    """
    try:
        service = AttachmentService(session)
        response, error = await service.generate_presigned_urls(
            files=request.files,
            user_id=current_user.id,
            workspace_id=current_user.current_workspace_id,
        )
        if error:
            raise HTTPException(status_code=400, detail=error)
        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating presigned URLs for attachments: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/attachments/confirm-uploads", response_model=TaskStatusResponse)
async def confirm_attachment_uploads(
    request: AttachmentConfirmRequest,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
):
    """
    Confirm that files have been uploaded and trigger the validation task.
    """
    try:
        service = AttachmentService(session)

        # Verify files exist and create attachment records
        verified_files = await service.verify_uploads(request.uploaded_files)
        if not verified_files:
            raise HTTPException(status_code=400, detail="No uploaded files found in storage.")

        # Trigger validation task
        task = validate_attachment_task.delay(
            uploaded_files=[f.model_dump() for f in verified_files],
            user_id=str(current_user.id),
            workspace_id=str(current_user.current_workspace_id),
        )

        return TaskStatusResponse(
            task_id=task.id,
            status="PROGRESS",
            status_message=f"Validation started for {len(verified_files)} files.",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error confirming attachment uploads: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/attachments/tasks/{task_id}", response_model=TaskStatusResponse)
async def get_attachment_task_status(task_id: UUID):
    """
    Get the status of an asynchronous attachment validation task.
    """
    try:
        task_result = AsyncResult(str(task_id))
        response = {
            "task_id": str(task_id),
            "status": task_result.state,
            "progress": 0,
        }

        if task_result.state == "PENDING":
            response["status_message"] = "Task is pending."
        elif task_result.state == "PROGRESS":
            response["status"] = "PROGRESS"
            if task_result.info and isinstance(task_result.info, dict):
                response["progress"] = task_result.info.get("progress", 0)
                response["status_message"] = task_result.info.get("message", "Processing...")
        elif task_result.state == "SUCCESS":
            response["status"] = "SUCCESS"
            response["progress"] = 100
            response["status_message"] = "Task completed successfully."
            if task_result.result and isinstance(task_result.result, dict):
                # Extract attachment_id from the first successful attachment for backward compatibility
                attachments = task_result.result.get("attachments", [])
                successful_attachments = [a for a in attachments if a.get("status") == "success"]
                if successful_attachments:
                    response["result"] = {"attachment_id": successful_attachments[0]["attachment_id"]}
                else:
                    response["result"] = task_result.result
        elif task_result.state == "FAILURE":
            response["status"] = "FAILURE"
            response["status_message"] = "Task failed."
            if task_result.info and isinstance(task_result.info, dict):
                response["error"] = task_result.info.get("error", str(task_result.result))
            else:
                response["error"] = str(task_result.result)

        return TaskStatusResponse(**response)
    except Exception as e:
        logger.error(f"Error getting task status for task {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error retrieving task status.")


@router.get("/attachments/{attachment_id}/download-url", response_model=AttachmentDownloadResponse)
async def get_attachment_download_url(
    attachment_id: UUID,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
):
    """
    Generate a presigned GET URL to download an attachment.
    """
    try:
        service = AttachmentService(session)
        download_url, error = await service.get_download_url(
            attachment_id=attachment_id,
            user_id=current_user.id,
        )
        if error:
            raise HTTPException(status_code=404, detail=error)

        return AttachmentDownloadResponse(
            attachment_id=attachment_id,
            download_url=download_url
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating download URL for attachment {attachment_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
