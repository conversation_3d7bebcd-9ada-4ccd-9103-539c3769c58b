"""
Console WebSocket Proxy Router
Proxies WebSocket connections from frontend to executor following the same pattern as other routes
"""

import asyncio
import json
import logging
import uuid

import websockets
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState
from sqlalchemy.orm import selectinload
from sqlmodel import Session, select
from websockets.exceptions import ConnectionClosedError, ConnectionClosedOK

from app.api.deps import verify_access_token
from app.core.config import settings
from app.core.db import engine
from app.models import AWSAccount, Workspace
from app.repositories.workspaces import WorkspaceRepository

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/console", tags=["console-proxy"])

# Active WebSocket connections mapping: workspace_id -> user_id -> proxy instance
active_connections: dict[str, dict[str, "ConsoleWebSocketProxy"]] = {}


def get_workspace_aws_credentials(workspace_id: uuid.UUID) -> dict[str, str | None]:
    """
    Fetch AWS credentials for a workspace.

    Args:
        workspace_id: UUID of the workspace

    Returns:
        Dictionary containing AWS credentials or None values if not found
    """
    try:
        with Session(engine) as session:
            workspace_repo = WorkspaceRepository(session)

            # Get workspace with AWS account
            statement = (
                select(Workspace)
                .where(Workspace.id == workspace_id, ~Workspace.is_deleted)
                .options(
                    selectinload(Workspace.aws_account).selectinload(
                        AWSAccount.credential
                    ),
                    selectinload(Workspace.settings),
                )
            )
            workspace = session.exec(statement).first()

            if not workspace or not workspace.aws_account:
                logger.warning(f"No AWS account found for workspace {workspace_id}")
                return {
                    "aws_access_key_id": None,
                    "aws_secret_access_key": None,
                    "aws_default_region": "us-east-1",
                }

            # Get AWS credentials
            credential = workspace_repo.get_aws_credentials(workspace.aws_account.id)
            if not credential:
                logger.warning(f"No AWS credentials found for workspace {workspace_id}")
                return {
                    "aws_access_key_id": None,
                    "aws_secret_access_key": None,
                    "aws_default_region": "us-east-1",
                }

            # Get default region from workspace settings
            default_region = "us-east-1"
            if workspace.settings and workspace.settings.regions:
                default_region = workspace.settings.regions[0]

            logger.info(f"Retrieved AWS credentials for workspace {workspace_id}")
            return {
                "aws_access_key_id": credential.access_key_id,
                "aws_secret_access_key": credential.secret_access_key,
                "aws_default_region": default_region,
            }

    except Exception as e:
        logger.error(
            f"Error fetching AWS credentials for workspace {workspace_id}: {e}"
        )
        return {
            "aws_access_key_id": None,
            "aws_secret_access_key": None,
            "aws_default_region": "us-east-1",
        }


class ConsoleWebSocketProxy:
    """WebSocket proxy between frontend and executor following the executor pattern"""

    def __init__(self, workspace_id: str, user_id: str):
        self.workspace_id = workspace_id
        self.user_id = user_id
        self.frontend_ws: WebSocket | None = None
        self.executor_ws: websockets.WebSocketServerProtocol | None = None
        self.running = False
        self.logger = logger
        self.aws_credentials: dict[str, str | None] = {}

    async def connect_to_executor(self) -> bool:
        """Establish WebSocket connection to executor following the executor pattern"""
        try:
            # Fetch AWS credentials for the workspace
            self.aws_credentials = get_workspace_aws_credentials(
                uuid.UUID(self.workspace_id)
            )

            # Build executor URL following the same pattern as call_executor
            executor_base = settings.EXECUTOR_HOST.rstrip("/")
            if not executor_base.startswith(("http://", "https://")):
                executor_base = f"http://{executor_base}"

            # Replace http with ws for WebSocket
            ws_base = executor_base.replace("http://", "ws://").replace(
                "https://", "wss://"
            )
            executor_url = f"{ws_base}/api/v1/console/ws/{self.workspace_id}"

            self.logger.info(
                f"[{self.workspace_id}:{self.user_id}] Connecting to executor at: {executor_url}"
            )

            # Connect to executor WebSocket with timeout using asyncio.wait_for
            self.executor_ws = await asyncio.wait_for(
                websockets.connect(executor_url), timeout=30.0
            )

            # Send AWS credentials as initial configuration message if available
            if self.aws_credentials.get("aws_access_key_id"):
                config_message = {
                    "type": "config",
                    "aws_credentials": {
                        "aws_access_key_id": self.aws_credentials["aws_access_key_id"],
                        "aws_secret_access_key": self.aws_credentials[
                            "aws_secret_access_key"
                        ],
                        "aws_default_region": self.aws_credentials[
                            "aws_default_region"
                        ],
                    },
                }
                await self.executor_ws.send(json.dumps(config_message))
                self.logger.info(
                    f"[{self.workspace_id}:{self.user_id}] Sent AWS credentials configuration to executor"
                )

            self.logger.info(
                f"[{self.workspace_id}:{self.user_id}] Successfully connected to executor with AWS credentials"
            )
            return True

        except asyncio.TimeoutError:
            self.logger.error(
                f"[{self.workspace_id}:{self.user_id}] Timeout connecting to executor after 30 seconds"
            )
            return False
        except Exception as e:
            self.logger.error(
                f"[{self.workspace_id}:{self.user_id}] Failed to connect to executor: {e}"
            )
            return False

    async def proxy_frontend_to_executor(self):
        """Forward messages from frontend to executor"""
        try:
            while self.running and self.frontend_ws and self.executor_ws:
                try:
                    # Receive from frontend
                    message = await self.frontend_ws.receive_text()

                    # Log message type for debugging
                    try:
                        msg_data = json.loads(message)
                        msg_type = msg_data.get("type", "unknown")
                        self.logger.debug(
                            f"[{self.workspace_id}:{self.user_id}] Frontend -> Executor: {msg_type}"
                        )
                    except Exception as e:
                        self.logger.error(
                            f"[{self.workspace_id}:{self.user_id}] Error parsing message: {e}"
                        )
                        self.logger.debug(
                            f"[{self.workspace_id}:{self.user_id}] Frontend -> Executor: raw message"
                        )

                    # Forward to executor
                    await self.executor_ws.send(message)

                except WebSocketDisconnect:
                    self.logger.info(
                        f"[{self.workspace_id}:{self.user_id}] Frontend WebSocket disconnected"
                    )
                    break
                except ConnectionClosedError:
                    self.logger.info(
                        f"[{self.workspace_id}:{self.user_id}] Executor WebSocket connection closed"
                    )
                    break
                except Exception as e:
                    self.logger.error(
                        f"[{self.workspace_id}:{self.user_id}] Error in frontend->executor proxy: {e}"
                    )
                    break

        except Exception as e:
            self.logger.error(
                f"[{self.workspace_id}:{self.user_id}] Error in frontend->executor proxy loop: {e}"
            )

    async def proxy_executor_to_frontend(self):
        """Forward messages from executor to frontend"""
        try:
            while self.running and self.frontend_ws and self.executor_ws:
                try:
                    # Receive from executor
                    message = await self.executor_ws.recv()

                    # Log message type for debugging
                    try:
                        msg_data = json.loads(message)
                        msg_type = msg_data.get("type", "unknown")
                        self.logger.debug(
                            f"[{self.workspace_id}:{self.user_id}] Executor -> Frontend: {msg_type}"
                        )
                    except Exception as e:
                        self.logger.error(
                            f"[{self.workspace_id}:{self.user_id}] Error parsing message: {e}"
                        )
                        self.logger.debug(
                            f"[{self.workspace_id}:{self.user_id}] Executor -> Frontend: raw message"
                        )

                    # Forward to frontend
                    await self.frontend_ws.send_text(message)

                except (ConnectionClosedError, ConnectionClosedOK):
                    self.logger.info(
                        f"[{self.workspace_id}:{self.user_id}] Executor WebSocket connection closed"
                    )
                    break
                except WebSocketDisconnect:
                    self.logger.info(
                        f"[{self.workspace_id}:{self.user_id}] Frontend WebSocket disconnected"
                    )
                    break
                except Exception as e:
                    self.logger.error(
                        f"[{self.workspace_id}:{self.user_id}] Error in executor->frontend proxy: {e}"
                    )
                    break

        except Exception as e:
            self.logger.error(
                f"[{self.workspace_id}:{self.user_id}] Error in executor->frontend proxy loop: {e}"
            )

    async def start_proxy(self, frontend_ws: WebSocket):
        """Start the bidirectional proxy following the async pattern"""
        self.frontend_ws = frontend_ws
        self.running = True

        try:
            # Connect to executor
            if not await self.connect_to_executor():
                await frontend_ws.close(
                    code=1011, reason="Failed to connect to executor"
                )
                return

            self.logger.info(
                f"[{self.workspace_id}:{self.user_id}] Starting bidirectional proxy with AWS credentials"
            )

            # Start bidirectional proxying
            await asyncio.gather(
                self.proxy_frontend_to_executor(),
                self.proxy_executor_to_frontend(),
                return_exceptions=True,
            )

        except Exception as e:
            self.logger.error(
                f"[{self.workspace_id}:{self.user_id}] Error in proxy: {e}"
            )
        finally:
            await self.cleanup()

    async def cleanup(self):
        """Cleanup connections"""
        self.running = False
        self.logger.info(
            f"[{self.workspace_id}:{self.user_id}] Cleaning up proxy connections"
        )

        # Close executor WebSocket
        if self.executor_ws:
            try:
                await self.executor_ws.close()
            except Exception as e:
                self.logger.warning(
                    f"[{self.workspace_id}:{self.user_id}] Error closing executor WebSocket: {e}"
                )

        # Close frontend WebSocket
        if self.frontend_ws:
            try:
                # Only close if not already closed
                if self.frontend_ws.client_state == WebSocketState.CONNECTED:
                    await self.frontend_ws.close(code=1000, reason="Proxy cleanup")
            except Exception as e:
                self.logger.warning(
                    f"[{self.workspace_id}:{self.user_id}] Error closing frontend WebSocket: {e}"
                )

        # Remove from active connections
        if self.workspace_id in active_connections:
            active_connections[self.workspace_id].pop(self.user_id, None)
            if not active_connections[self.workspace_id]:
                del active_connections[self.workspace_id]


@router.websocket("/ws")
async def websocket_proxy_endpoint(websocket: WebSocket):
    """
    WebSocket proxy endpoint with authentication following the existing route patterns
    Gets workspace ID from user's current workspace like other routes
    """
    logger.info("New console WebSocket connection attempt")

    # For WebSocket connections, we need to handle authentication manually
    # Get token from query parameters
    query_params = dict(websocket.query_params)
    token = query_params.get("token")

    if not token:
        await websocket.close(code=1008, reason="Authentication token required")
        return

    try:
        # Verify token and get user
        payload = verify_access_token(token)
        if not payload:
            await websocket.close(code=1008, reason="Invalid token")
            return

        user_id = payload.sub
        workspace_id = payload.workspace_id

        # Accept WebSocket connection
        await websocket.accept()
        logger.info(
            f"Console WebSocket connection accepted for workspace: {workspace_id}, user: {user_id}"
        )

        # Create and start proxy
        proxy = ConsoleWebSocketProxy(str(workspace_id), str(user_id))

        # Track active connection
        if str(workspace_id) not in active_connections:
            active_connections[str(workspace_id)] = {}
        active_connections[str(workspace_id)][str(user_id)] = proxy

        # Start proxy
        await proxy.start_proxy(websocket)

    except Exception as e:
        logger.error(f"Error in WebSocket proxy endpoint: {e}")
        try:
            if websocket.client_state == WebSocketState.CONNECTED:
                await websocket.close(code=1011, reason="Internal server error")
        except Exception as e:
            logger.error(f"Error closing WebSocket: {e}")
            pass
