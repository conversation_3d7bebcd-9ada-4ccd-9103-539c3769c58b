import uuid
from typing import Any

from fastapi import APIRouter, HTTPException

from app.api.deps import CurrentUser, SessionAsyncDep
from app.logger import logger
from app.models import Message
from app.schemas.mcp_server import (
    MCPServerCreateSchema,
    MCPServerListResponseSchema,
    MCPServerResponseSchema,
    MCPServerUpdateSchema,
)
from app.services.mcp_server_service import MCPServerService

router = APIRouter()


@router.post("/", response_model=MCPServerResponseSchema)
async def create_mcp_server(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    mcp_server_in: MCPServerCreateSchema,
) -> Any:
    """
    Create a new MCP server for a workspace.
    """
    try:
        mcp_server_service = MCPServerService(session)
        mcp_server = await mcp_server_service.create_mcp_server(
            workspace_id=current_user.current_workspace_id, data=mcp_server_in
        )
        return mcp_server
    except Value<PERSON>rror as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating MCP server: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Integration service temporarily unavailable"
        )


@router.get("/", response_model=MCPServerListResponseSchema)
async def get_mcp_servers(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
) -> Any:
    """
    Get all MCP servers for a workspace.

    Returns information about all configured MCP servers for the workspace,
    including their connection status, available tools, and configuration details.
    """
    try:
        mcp_server_service = MCPServerService(session)
        mcp_servers = await mcp_server_service.get_mcp_servers(
            current_user.current_workspace_id
        )
        return mcp_servers
    except Exception as e:
        logger.error(f"Error retrieving MCP servers: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Integration service temporarily unavailable"
        )


@router.get("/{server_id}", response_model=MCPServerResponseSchema)
async def get_mcp_server(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    server_id: uuid.UUID,
) -> Any:
    """
    Get a specific MCP server by ID.
    """
    try:
        mcp_server_service = MCPServerService(session)
        mcp_server = await mcp_server_service.get_mcp_server(
            current_user.current_workspace_id, server_id
        )
        if not mcp_server:
            raise HTTPException(status_code=404, detail="MCP server not found")
        return mcp_server
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving MCP server: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Integration service temporarily unavailable"
        )


@router.put("/{server_id}", response_model=MCPServerResponseSchema)
async def update_mcp_server(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    server_id: uuid.UUID,
    mcp_server_update: MCPServerUpdateSchema,
) -> Any:
    """
    Update an existing MCP server.
    """
    try:
        mcp_server_service = MCPServerService(session)
        updated_server = await mcp_server_service.update_mcp_server(
            workspace_id=current_user.current_workspace_id,
            server_id=server_id,
            data=mcp_server_update,
        )
        if not updated_server:
            raise HTTPException(status_code=404, detail="MCP server not found")
        return updated_server
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating MCP server: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Integration service temporarily unavailable"
        )


@router.delete("/{server_id}", response_model=Message)
async def delete_mcp_server(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    server_id: uuid.UUID,
) -> Any:
    """
    Delete an MCP server.
    """
    try:
        mcp_server_service = MCPServerService(session)
        success = await mcp_server_service.delete_mcp_server(
            current_user.current_workspace_id, server_id
        )
        if not success:
            raise HTTPException(status_code=404, detail="MCP server not found")
        return Message(message="MCP server successfully deleted")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting MCP server: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Integration service temporarily unavailable"
        )


@router.post("/{server_id}/refresh", response_model=MCPServerResponseSchema)
async def refresh_mcp_server(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    server_id: uuid.UUID,
) -> Any:
    """
    Refresh an MCP server.
    """
    try:
        mcp_server_service = MCPServerService(session)
        updated_server = await mcp_server_service.refresh_mcp_server(
            workspace_id=current_user.current_workspace_id,
            server_id=server_id,
        )
        if not updated_server:
            raise HTTPException(status_code=404, detail="MCP server not found")
        return updated_server
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error refreshing MCP server: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Integration service temporarily unavailable"
        )
