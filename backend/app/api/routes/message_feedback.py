import logging
from typing import Any
from uuid import UUID

from fastapi import APIRouter, HTTPException

from app.api.deps import CurrentUser, SessionAsyncDep
from app.models import (
    MessageFeedbackCreate,
    MessageFeedbackPublic,
    MessageFeedbackUpdate,
)
from app.repositories.message_feedback import MessageFeedbackRepository

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/message/{message_id}", response_model=MessageFeedbackPublic | None)
async def get_message_feedback(
    message_id: UUID,
    async_session: SessionAsyncDep,
) -> Any:
    """Get feedback for a specific message."""
    try:
        repo = MessageFeedbackRepository(async_session)
        return await repo.get_by_message_id(message_id)
    except Exception as e:
        logger.error(f"Error getting message feedback for message {message_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while getting message feedback.",
        )


@router.post("/", response_model=MessageFeedbackPublic)
async def create_message_feedback(
    feedback_in: MessageFeedbackCreate,
    async_session: SessionAsyncDep,
    current_user: CurrentUser,
) -> Any:
    """Create feedback for a message."""
    try:
        repo = MessageFeedbackRepository(async_session)
        return await repo.create(feedback_in, current_user)
    except Exception as e:
        logger.error(f"Error creating message feedback: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while creating message feedback.",
        )


@router.put("/message/{message_id}", response_model=MessageFeedbackPublic)
async def update_message_feedback(
    message_id: UUID,
    feedback_in: MessageFeedbackUpdate,
    async_session: SessionAsyncDep,
    current_user: CurrentUser,
) -> Any:
    """Update feedback for a message."""
    try:
        repo = MessageFeedbackRepository(async_session)
        return await repo.update(message_id, feedback_in, current_user)
    except Exception as e:
        logger.error(f"Error updating message feedback for message {message_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while updating message feedback.",
        )


@router.delete("/message/{message_id}")
async def delete_message_feedback(
    message_id: UUID,
    async_session: SessionAsyncDep,
    current_user: CurrentUser,
) -> Any:
    """Delete feedback for a message."""
    try:
        repo = MessageFeedbackRepository(async_session)
        await repo.delete(message_id, current_user)
        return {"message": "Feedback deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting message feedback for message {message_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while deleting message feedback.",
        )
