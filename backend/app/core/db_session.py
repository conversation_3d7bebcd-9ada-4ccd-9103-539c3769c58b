from contextlib import asynccontextmanager

from sqlalchemy.sql import text
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.db import async_engine


@asynccontextmanager
async def get_task_session(timeout: int = 600):
    """
    Provide a transactional scope around a series of operations for background tasks.
    This context manager handles session creation, timeout settings, commits, rollbacks, and closing.
    """
    session = AsyncSession(async_engine)
    try:
        # Set statement timeout for the session
        await session.exec(text(f"SET statement_timeout = {timeout * 1000}"))
        yield session
        # Commit the transaction if no exceptions occurred
        await session.commit()
    except Exception:
        # Rollback the transaction in case of any error
        await session.rollback()
        raise
    finally:
        # Ensure the session is closed
        await session.close()
