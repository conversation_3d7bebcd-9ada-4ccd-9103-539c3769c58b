"""
Crawler Configuration Module

This module provides configuration functions for web crawling strategies.
"""

from crawl4ai import (
    CacheMode,
    CrawlerRunConfig,
)
from crawl4ai.content_filter_strategy import PruningContentFilter
from crawl4ai.content_scraping_strategy import LXMLWebScrapingStrategy
from crawl4ai.deep_crawling import BestFirstCrawlingStrategy
from crawl4ai.markdown_generation_strategy import <PERSON><PERSON><PERSON><PERSON><PERSON>downGenerator

from app.core.config import settings


def get_crawling_config(deep_crawl: bool = False) -> CrawlerRunConfig:
    """
    Returns a base crawler configuration with common settings.

    Args:
        deep_crawl: Whether to enable deep crawling (follows internal links)

    Returns:
        CrawlerRunConfig: Basic configuration for web crawling
    """
    config = CrawlerRunConfig(
        markdown_generator=DefaultMarkdownGenerator(
            content_filter=PruningContentFilter()
        ),
        scraping_strategy=LXMLWebScrapingStrategy(),
        cache_mode=CacheMode.BYPASS,
        remove_overlay_elements=True,
        exclude_internal_links=False,
        exclude_external_links=True,
        exclude_social_media_links=True,
        exclude_external_images=True,
        remove_forms=True,
        # wait_for_images=False,
        # scan_full_page=True,
        # scroll_delay=0.5,
        # verbose=False,
        # check_robots_txt=False,
        # mean_delay=0.1,
        # max_range=0.1,
        # magic=True,
        page_timeout=60000,
        # screenshot=False,
        # pdf=False,
        # excluded_tags=["form", "header", "img"],
        # stream=False,
    )

    # Only add deep crawling strategy if enabled
    if deep_crawl:
        config.deep_crawl_strategy = BestFirstCrawlingStrategy(
            max_depth=settings.MAX_DEPTH,
            include_external=False,
            max_pages=settings.MAX_PAGES,
        )

    return config
