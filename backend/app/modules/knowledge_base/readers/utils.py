import uuid
from urllib.parse import urlparse, urlunparse


def generate_short_uuid(length: int = 8) -> str:
    return str(uuid.uuid4()).replace("-", "")[:length]


def clean_and_deduplicate_urls(links: list[dict]) -> tuple[list[dict], set[str]]:
    """
    Clean URLs by removing query parameters and fragments, then deduplicate.

    Args:
        links: List of link dictionaries with 'href' key

    Returns:
        List of cleaned and deduplicated link dictionaries
    """
    seen_urls = set()
    cleaned_links = []
    cleaned_urls = set()
    skip_urls = [
        "changelog",
        "license",
        "contributing",
        "security",
        "privacy",
        "terms",
        "imprint",
        "contact",
        "about",
        "faq",
        "support",
        "help",
        "events",
        "careers",
        "partners",
        "investors",
    ]

    for link in links:
        url = link["href"]

        # Parse the URL
        parsed = urlparse(url)

        # Remove query parameters and fragments
        cleaned_url = urlunparse(
            (
                parsed.scheme,
                parsed.netloc,
                parsed.path.rstrip("/"),  # Remove trailing slash for consistency
                "",  # params
                "",  # query
                "",  # fragment
            )
        )

        # Add trailing slash back for root paths to maintain consistency
        if not cleaned_url.endswith("/") and parsed.path in ["", "/"]:
            cleaned_url += "/"

        # Skip if we've already seen this cleaned URL
        if cleaned_url in seen_urls:
            continue

        # Skip URLs with fragments (like #section)
        if parsed.fragment:
            continue

        # Skip URLs that contain any of the skip_urls
        if any(skip_url in cleaned_url for skip_url in skip_urls):
            continue

        seen_urls.add(cleaned_url)
        cleaned_urls.add(cleaned_url)

        # Create new link dict with cleaned URL
        cleaned_link = link.copy()
        cleaned_link["href"] = cleaned_url
        cleaned_links.append(cleaned_link)

    return cleaned_links, cleaned_urls
