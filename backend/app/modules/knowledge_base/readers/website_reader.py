import asyncio
from typing import Callable
from urllib.parse import urlparse, urlunparse

from crawl4ai import (
    AsyncWebCrawler,
    BrowserConfig,
    CrawlResult,
    RateLimiter,
    SemaphoreDispatcher,
)
from llama_index.core.schema import Document
from slugify import slugify

from app.core.config import settings
from app.logger import logger
from app.models import AsyncTaskStatus, DocumentKB, DocumentKBCreate, DocumentType

from ..crawlers.crawler_config import get_crawling_config
from .utils import clean_and_deduplicate_urls

_browser_config = BrowserConfig(
    headless=True,
    browser_type="chromium",
    channel="chrome",
    viewport_width=1920,
    viewport_height=1080,
    user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
)
_dispatcher = SemaphoreDispatcher(
    semaphore_count=16,
    max_session_permit=20,
    rate_limiter=RateLimiter(base_delay=(0.1, 0.3), max_delay=10.0),
)


def _generate_file_name(result: CrawlResult) -> str:
    """Generate a file name from crawl result metadata."""
    url = result.url
    title = result.metadata.get("title", "")
    description = result.metadata.get("description", "")
    if not description and not title:
        return url.split("/")[-1]
    if not description:
        return title
    return f"{title} - {description}"


def _generate_object_name(kb_id: str, file_name: str) -> str:
    """Generate object name for storage."""
    return f"{kb_id}/{slugify(file_name)}.md"


def _extract_text_content(result: CrawlResult) -> str:
    """Extract text content from crawl result."""
    if not result.markdown:
        return ""
    return result.markdown.fit_markdown or result.markdown.raw_markdown


def _create_document(
    result: CrawlResult, doc_db: DocumentKB, object_name: str
) -> Document:
    """Create a Document object from crawl result."""
    return Document(
        doc_id=str(doc_db.id),
        text=_extract_text_content(result),
        metadata={
            "url": result.url,
            "deep_crawl": doc_db.deep_crawl,
            "title": result.metadata.get("title", ""),
            "description": result.metadata.get("description", ""),
            "object_name": object_name,
            "kb_id": str(doc_db.kb_id),
            "document_id": str(doc_db.id),
        },
    )


def _create_child_document_kb(
    result: CrawlResult, doc_db: DocumentKB, file_name: str, object_name: str
) -> DocumentKBCreate:
    """Create a DocumentKBCreate object for child documents."""
    return DocumentKBCreate(
        name=file_name[:255],
        file_name=file_name,
        object_name=object_name,
        url=result.url,
        deep_crawl=doc_db.deep_crawl,
        kb_id=doc_db.kb_id,
        parent_id=doc_db.id,
        embed_status=AsyncTaskStatus.COMPLETED,
        type=DocumentType.URL,
    )


def _normalize_url_for_comparison(url: str) -> str:
    """Normalize URL for comparison by removing trailing slashes and fragments."""
    parsed = urlparse(url)
    normalized = urlunparse(
        (
            parsed.scheme,
            parsed.netloc,
            parsed.path.rstrip("/"),
            "",  # params
            "",  # query
            "",  # fragment
        )
    )
    return normalized


def _process_deep_crawl_results(
    doc_db: DocumentKB, results: list[CrawlResult], success: bool
) -> tuple[list[Document], list[DocumentKBCreate]]:
    """Process results for deep crawl documents."""
    documents = []
    children_docs = []

    if not success or not results:
        return documents, children_docs

    # Find the result that matches the original URL to update the parent document
    parent_result = None
    child_results = []

    logger.info(f"Processing deep crawl results for original URL: {doc_db.url}")
    logger.info(f"Found {len(results)} crawl results")

    for result in results:
        if not result or not result.markdown:
            continue

        # Check if this result matches the original URL (normalize URLs for comparison)
        result_url = _normalize_url_for_comparison(result.url)
        original_url = _normalize_url_for_comparison(doc_db.url)

        if result_url == original_url:
            parent_result = result
        else:
            child_results.append(result)

    logger.info(
        f"Parent result found: {parent_result is not None}, Child results: {len(child_results)}"
    )

    # Update parent document if we found content for the original URL
    if parent_result:
        file_name = _generate_file_name(parent_result)
        object_name = _generate_object_name(doc_db.kb_id, file_name)

        # Update the parent document with actual content
        doc_db.embed_status = AsyncTaskStatus.COMPLETED
        doc_db.name = file_name[:255]
        doc_db.file_name = file_name
        doc_db.object_name = object_name

        # Create document for the parent
        documents.append(_create_document(parent_result, doc_db, object_name))
    else:
        # If we couldn't find content for the original URL, mark it as failed
        doc_db.embed_status = AsyncTaskStatus.FAILED
        logger.warning(f"No matching result found for original URL: {doc_db.url}")

    # Create child documents for all other URLs
    for result in child_results:
        file_name = _generate_file_name(result)
        object_name = _generate_object_name(doc_db.kb_id, file_name)

        documents.append(_create_document(result, doc_db, object_name))
        children_docs.append(
            _create_child_document_kb(result, doc_db, file_name, object_name)
        )

    return documents, children_docs


def _process_shallow_crawl_results(
    doc_db: DocumentKB, results: list[CrawlResult], success: bool
) -> tuple[list[Document], None]:
    """Process results for shallow crawl documents."""
    if not success or not results or not results[0] or not results[0].markdown:
        doc_db.embed_status = AsyncTaskStatus.FAILED
        return [], None

    result = results[0]
    file_name = _generate_file_name(result)
    object_name = _generate_object_name(doc_db.kb_id, file_name)

    # Update the document database object
    doc_db.embed_status = AsyncTaskStatus.COMPLETED
    doc_db.name = file_name
    doc_db.file_name = file_name
    doc_db.object_name = object_name

    document = _create_document(result, doc_db, object_name)
    return [document], None


async def _process_single_url(
    crawler: AsyncWebCrawler,
    url: str,
) -> tuple[list[CrawlResult] | None, bool]:
    """Process a single URL with the crawler."""
    try:
        results: list[CrawlResult] = await crawler.arun(
            url=url,
            config=get_crawling_config(),
            dispatcher=_dispatcher,
        )
        return results, True

    except Exception as e:
        logger.error(f"Error crawling website: {e}")
        return None, False


async def _process_single_url_with_crawler(
    crawler: AsyncWebCrawler,
    url: str,
) -> tuple[list[CrawlResult] | None, bool]:
    logger.info(f"Deep crawling {url}")

    # Step 1. Get all internal links
    res: list[CrawlResult] = await crawler.arun(url=url)
    if not res.success:
        return None, False

    # Store the original page result
    original_result = res

    internal_links = res.links["internal"]
    _, cleaned_urls_set = clean_and_deduplicate_urls(internal_links)

    # Ensure the original URL is included in the crawl list
    original_url_normalized = _normalize_url_for_comparison(url)
    cleaned_urls_list = list(cleaned_urls_set)

    # Add original URL if it's not already in the list
    if not any(
        _normalize_url_for_comparison(u) == original_url_normalized
        for u in cleaned_urls_list
    ):
        cleaned_urls_list.insert(0, url)

    # Limit to max pages
    cleaned_urls = cleaned_urls_list[: settings.MAX_PAGES]

    logger.info(
        f"Found {len(internal_links)} URLs, {len(cleaned_urls)} cleaned URLs (including original), limiting to {settings.MAX_PAGES}"
    )

    if len(cleaned_urls) == 0:
        return None, False

    # Step 2. Crawl all internal links (including original URL)
    crawl_results: list[CrawlResult] = await crawler.arun_many(
        urls=cleaned_urls,
        config=get_crawling_config(),
        dispatcher=_dispatcher,
    )

    # If original URL wasn't successfully crawled in the batch, use the original result
    original_found = any(
        _normalize_url_for_comparison(result.url) == original_url_normalized
        for result in crawl_results
        if result and result.success
    )

    logger.info(f"Original URL found in batch crawl: {original_found}")
    logger.info(f"Original result success: {original_result.success}")

    if not original_found and original_result.success:
        crawl_results.insert(0, original_result)
        logger.info("Inserted original result at the beginning of crawl results")

    # Filter successful results
    successful_results = [
        result for result in crawl_results if result and result.success
    ]

    logger.info(f"Total successful results: {len(successful_results)}")
    for i, result in enumerate(successful_results):
        logger.debug(f"Result {i}: {result.url} (success: {result.success})")

    if len(successful_results) == 0:
        return None, False

    return successful_results, True


async def read(
    docs_to_ingest: list[DocumentKB],
    call_back: Callable,
) -> tuple[list[Document], list[DocumentKB], list[DocumentKBCreate]]:
    """Read and process website documents."""
    try:
        crawler = AsyncWebCrawler(config=_browser_config)
        await crawler.start()

        # Crawl all URLs concurrently
        normal_tasks = [
            _process_single_url(crawler, doc.url)
            for doc in docs_to_ingest
            if not doc.deep_crawl
        ]
        crawler_tasks = [
            _process_single_url_with_crawler(crawler, doc.url)
            for doc in docs_to_ingest
            if doc.deep_crawl
        ]
        all_tasks = normal_tasks + crawler_tasks
        all_results = await asyncio.gather(*all_tasks, return_exceptions=True)

        # Process results
        documents = []
        children_docs = []

        call_back(
            state=AsyncTaskStatus.IN_PROGRESS,
            meta={
                "progress": 50,
                "message": "Processing documents",
            },
        )
        for doc_db, crawl_result in zip(docs_to_ingest, all_results, strict=True):
            if isinstance(crawl_result, Exception):
                logger.error(f"Error processing URL {doc_db.url}: {crawl_result}")
                doc_db.embed_status = AsyncTaskStatus.FAILED
                continue

            results, success = crawl_result
            if not success or not results:
                continue

            if doc_db.deep_crawl:
                docs, children = _process_deep_crawl_results(doc_db, results, success)
                documents.extend(docs)
                children_docs.extend(children)
            else:
                docs, _ = _process_shallow_crawl_results(doc_db, results, success)
                documents.extend(docs)

        await crawler.close()
        return documents, docs_to_ingest, children_docs

    except Exception as e:
        logger.error(f"Error ingesting website: {e}")
        raise e
