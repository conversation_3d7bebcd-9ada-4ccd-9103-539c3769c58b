"""Networking agent."""

import random

from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.store.memory import BaseStore
from langgraph.types import Command
from pydantic import BaseModel, Field

from app.core.config import settings
from app.logger import logger
from app.services.memory.memory_service import MemoryService

from ...agents.conversational.agent import ConversationalAgent
from ...common import (
    BaseAgentToolNode,
    BaseConversationalAgentNode,
)
from ...core import (
    BaseAgent,
    Configuration,
    GlobalState,
    InputState,
)
from ...core.utils import load_chat_model
from ...prompts import (
    CUSTOMER_ROLE_DESCRIPTION,
    GROUP_CHAT_PROMPT,
    ROLE_PLAYING_PROMPT,
)
from ...tools.builtin import GroupChat


class RolePlayingOutput(BaseModel):
    """Output for role playing game.

    This class represents the output format for the role playing game where agents select roles
    from available participants based on the conversation context and last customer message.
    If the last customer message is satisfied, returns "END" as the role.
    """

    thought: str = Field(
        ...,
        description="The reasoning behind selecting this role based on conversation context",
    )
    role: str = Field(..., description="The selected role or 'END' if customer request is satisfied")


class NetworkingAgent(BaseAgent, BaseAgentToolNode, BaseConversationalAgentNode):
    """Networking agent."""

    async def set_up_state(self, state: GlobalState, config: RunnableConfig):
        """Set up state."""
        configuration = Configuration.from_runnable_config(config)

        if not state.init_state:
            # Set up the state for the first time
            messages = state.messages
            state = GlobalState.from_config(configuration.agents_config)
            state.messages = messages
            state.init_state = True

        # Set up the name
        state.name = None

        # Set up the group chat
        if state.group_chat is None:
            state.group_chat = GroupChat()
        state.group_chat.last_message = state.messages
        state.group_chat.messages.append(("Customer", state.messages))

        # Set up the MCP client
        conversation_id = config.get("configurable", {}).get("conversation_id", None)
        if conversation_id and conversation_id not in self.mcp_clients:
            await self.set_up_mcp_client(conversation_id, configuration)

        return Command(goto="coordination_agent", update=state)

    async def append_group_chat_prompt(self, state, config):
        """Append a HumanMessage with the group chat prompt to the selected agent's messages."""
        # Get the raw user prompt from config or state
        user_prompt = config.get("configurable", {}).get("user_prompt")
        attachment_content = config.get("configurable", {}).get("attachment_content")

        if not user_prompt and hasattr(state, "group_chat") and state.group_chat.messages:
            # Fallback: try to get the last customer message
            user_prompt = state.group_chat.last_message if hasattr(state.group_chat, "last_message") else None

        # Use search_memory to get available memories
        memory_service = MemoryService()
        available_memories_prompt = ""
        if user_prompt:
            available_memories_prompt = await memory_service.search_memory(
                user_prompt,
                state.name,
                config.get("configurable", {}).get("workspace_id"),
            )
        else:
            available_memories_prompt = "No user prompt provided."

        logger.info(f"Available memories prompt: {available_memories_prompt}")

        payload = [
            {
                "type": "text",
                "text": GROUP_CHAT_PROMPT.format(
                    group_chat=state.group_chat.get_messages(),
                    last_message=state.group_chat.last_message,
                    name=state.name,
                    kb_prompt=config.get("configurable", {}).get("kb_prompt"),
                    available_memories_prompt=available_memories_prompt,
                ),
            },
        ]
        if attachment_content:
            payload.extend(attachment_content)

        state.instance_states[state.name].messages.append(HumanMessage(payload))

    async def handle_role_playing(
        self,
        state: GlobalState,
        config: RunnableConfig,
        available_agents_list: list[str],
        active_agents: list[str],
    ) -> str:
        """Handle role playing."""
        success = False
        retry_count = 0
        role_playing_prompt = ROLE_PLAYING_PROMPT.format(
            name=state.name,
            role_descriptions="\n".join(available_agents_list),
            customer_role_description=CUSTOMER_ROLE_DESCRIPTION,
            group_chat=state.group_chat.get_messages(),
            last_group_chat_message=f"{state.group_chat.messages[-1][0]}: {state.group_chat.messages[-1][1]}",
            last_message=state.group_chat.last_message,
            participants=",".join(active_agents),
        )
        messages = [
            {
                "role": "user",
                "content": role_playing_prompt,
            },
        ]

        while retry_count < settings.NETWORKING_AGENT_RETRY_COUNT and not success:
            retry_count += 1

            # Use model to select next role/agent
            model = load_chat_model(settings.NETWORKING_MODEL).with_structured_output(RolePlayingOutput, strict=True)
            config["run_name"] = "Networking Agent"
            response = await model.ainvoke(messages, config)
            messages.append(response)
            role = response.role.strip()

            if role in self.state.instance_states.keys():
                success = True
                state.name = role
            else:
                success = False
                messages.append(
                    AIMessage(
                        content=f"The role {role} is not available. Please select another role in the list: {available_agents_list}"
                    )
                )

        if not success:
            # If no success, randomly select an agent
            state.name = random.choice(self.state.instance_states.keys())

        return state.name

    async def coordination_agent(self, state: GlobalState, config: RunnableConfig, store: BaseStore):
        """Coordinating agent (refactored for clarity and maintainability)."""
        conversation_id = config.get("configurable", {}).get("conversation_id", None)
        # Prepare agent lists
        (
            active_agents,
            available_agents_list,
            _,
        ) = await self.prepare_agent_list(config)

        # Handle direct target group chat member
        if state.target_group_chat_member:
            if state.target_group_chat_member == "Customer":
                state.target_group_chat_member = None
                if conversation_id in self.mcp_clients:
                    self.mcp_clients.pop(conversation_id)
                return Command(goto=END, update=state)

            # Target is another agent
            state.name = state.target_group_chat_member
            state.target_group_chat_member = None
            await self.append_group_chat_prompt(state, config)
            return Command(goto=state.name, update=state)

        # Decide next agent to handle the message
        if len(active_agents) > 1:
            state.name = await self.handle_role_playing(state, config, available_agents_list, active_agents)
        else:
            # Only one agent left, assign or end
            state.name = active_agents[0] if not state.name else "Customer"

        # If customer request is satisfied, end
        if state.name == "Customer":
            if conversation_id in self.mcp_clients:
                self.mcp_clients.pop(conversation_id)
            return Command(goto=END, update=state)

        # Otherwise, pass to the selected agent
        await self.append_group_chat_prompt(state, config)
        return Command(goto=state.name, update=state)

    def build_graph(self) -> None:
        """Build the graph."""
        self.state = GlobalState.from_config(self.config)
        self.graph = StateGraph(GlobalState, input=InputState, config_schema=Configuration)

        self.graph.add_node("set_up_state", self.set_up_state)
        self.graph.add_node("coordination_agent", self.coordination_agent)

        # Add worker nodes and edges
        for agent_id, _ in self.state.instance_states.items():
            # Hard code the supervisor agent has name "supervisor"
            self.graph.add_node(
                agent_id,
                ConversationalAgent(config=self.config, mcp_clients=self.mcp_clients).compile(),
            )
            self.graph.add_edge(agent_id, "coordination_agent")
        self.graph.add_edge("__start__", "set_up_state")

    def get_graph(self) -> StateGraph:
        """Get the graph."""
        return self.graph

    def compile(self, **kwargs):
        """Compile the graph."""
        return self.graph.compile(**kwargs)
