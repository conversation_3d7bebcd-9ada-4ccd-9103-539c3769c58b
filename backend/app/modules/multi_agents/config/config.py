"""Multi-agent system configuration."""

import uuid
from dataclasses import dataclass
from functools import cached_property
from pathlib import Path
from typing import Any

from pydantic import BaseModel, Field
from sqlmodel import SQLModel

from app.core.config import settings
from app.models import MCPServerBase

from .agent import AgentConfig
from .loaders import get_config_loader


class WorkspaceConfigSchema(SQLModel):
    workspace_id: uuid.UUID
    aws_access_key_id: str
    aws_secret_access_key: str
    aws_default_region: str
    environment: str


class MultiAgentConfig(BaseModel):
    """Multi-agent configuration."""

    start_agent: str = Field(
        default="supervisor", description="The agent to start the conversation with"
    )
    agents: dict[str, AgentConfig] = Field(
        ..., description="Map of agent configurations"
    )
    mcp_servers: list[MCPServerBase] | None = Field(
        default=None, description="MCP configuration"
    )
    active_agents: list[str] | None = Field(
        default=None, description="List of active agents"
    )

    @classmethod
    def load_config(cls, config: dict | Path | str) -> "MultiAgentConfig":
        """Load configuration from dictionary or file and validate it.

        Args:
            config: Either a dictionary containing the configuration,
                   or a Path/string pointing to a YAML/JSON file

        Returns:
            MultiAgentConfig: Loaded and validated configuration

        Raises:
            ValueError: If configuration source is invalid, file not found,
                      or validation fails
        """
        if isinstance(config, dict):
            instance = cls.model_validate(config)
        else:
            # Convert string path to Path object
            if isinstance(config, str):
                config = Path(config)

            if not isinstance(config, Path):
                raise ValueError(
                    f"Config must be dict, Path, or str, got {type(config).__name__}"
                )

            if not config.exists():
                raise ValueError(f"Configuration file not found: {config}")

            # Load configuration using appropriate loader
            loader = get_config_loader(config)
            data = loader.load(config)
            instance = cls.model_validate(data)

        # Validate the loaded configuration
        instance.validate()
        return instance

    def validate(self) -> None:
        """Run all validation checks on the configuration.

        This method runs all validation checks to ensure the configuration
        is valid and consistent. Currently includes:
        - Agent delegation dependencies validation
        - (Add more validations here as needed)

        Raises:
            ValueError: If any validation check fails
        """
        self.validate_agent_dependencies()

    def get_agent_configs(self) -> dict[str, AgentConfig]:
        """Get all agent configurations.

        Returns:
            Dict[str, AgentConfig]: Map of agent configurations
        """
        return self.agents

    def get_agent_config(self, agent_id: str) -> AgentConfig | None:
        """Get configuration for a specific agent.

        Args:
            agent_id: ID of the agent

        Returns:
            Optional[AgentConfig]: Agent configuration if found, None otherwise
        """
        return self.agents.get(agent_id)

    def validate_agent_dependencies(self) -> None:
        """Validate agent delegation dependencies.

        Raises:
            ValueError: If there are invalid delegation configurations
        """
        agent_ids = list(self.agents.keys())
        for _, agent_config in self.agents.items():
            delegate_config = agent_config.delegate_config
            # Check if the agent can delegate
            if delegate_config and delegate_config.delegate:
                # Check if the delegate_to is a list
                if isinstance(delegate_config.delegate_to, list):
                    # Check if the delegate_to is not "all" and is in the list of agents
                    for delegate_to in delegate_config.delegate_to:
                        if delegate_to not in agent_ids:
                            raise ValueError(
                                f"Invalid delegation target: {delegate_to}"
                            )
                # Check if the delegate_to is not "all" and is in the list of agents
                elif (
                    delegate_config.delegate_to != "all"
                    and delegate_config.delegate_to not in self.agents.keys()
                ):
                    raise ValueError(
                        f"Invalid delegation target: {delegate_config.delegate_to}"
                    )

    @cached_property
    def delegate_tool_config(
        self,
    ) -> dict[str, dict[str, Any]]:
        """Prepare configuration for delegate tools.

        Args:
            instance_states: Dictionary of agent states

        Returns:
            Dictionary mapping agent IDs to their delegate tool configurations
        """
        # Get all agent information
        all_agents = {
            agent_id: {
                "call_name": agent_id,
                "role": agent_config.role,
                "goal": agent_config.goal,
            }
            for agent_id, agent_config in self.agents.items()
        }

        # For each agent that can delegate, create their specific agent list
        agent_specific_lists = {}
        for agent_id, agent_config in self.agents.items():
            if agent_config.delegate_config is None:
                continue

            # Get list of agents this agent can delegate to
            if agent_config.delegate_config.delegate_to == "all":
                available_delegates = [
                    aid for aid in all_agents.keys() if aid != agent_id
                ]
            else:
                available_delegates = agent_config.delegate_config.delegate_to

            # Create agent-specific list string
            delegate_info = [all_agents[aid] for aid in available_delegates]
            agent_list = "\n".join(
                [
                    f"- {agent['call_name']}: {agent['role']} - {agent['goal']}"
                    for agent in delegate_info
                ]
            )
            agents_name = [agent["call_name"] for agent in delegate_info]

            agent_specific_lists[agent_id] = {
                "agents_info": delegate_info,
                "agents_list": agent_list,
                "agents_name": agents_name,
            }

        return agent_specific_lists


@dataclass
class ConversationConfigSchema:
    """Schema for conversation configuration"""

    user_id: uuid.UUID | None = None
    checkpoint_id: uuid.UUID | None = None
    thread_id: uuid.UUID | None = None
    conversation_id: uuid.UUID | None = None
    workspace_id: uuid.UUID | None = None
    resource_id: uuid.UUID | None = None
    workspace_config: WorkspaceConfigSchema | None = None
    agents_config: MultiAgentConfig | None = None
    recursion_limit: int = 200
    available_kbs_for_search: list[uuid.UUID] | None = None
    kb_prompt: str | None = None
    attachment_content: list[dict] | None = None

    def to_dict(self):
        result = {
            "configurable": {
                "user_id": str(self.user_id) if self.user_id else None,
                "recursion_limit": self.recursion_limit,
                "checkpoint_id": str(self.checkpoint_id)
                if self.checkpoint_id
                else None,
                "thread_id": str(self.thread_id) if self.thread_id else None,
                "conversation_id": str(self.conversation_id)
                if self.conversation_id
                else None,
                "workspace_id": str(self.workspace_id) if self.workspace_id else None,
                "workspace_config": self.workspace_config,
                "agents_config": self.agents_config,
                "available_kbs_for_search": self.available_kbs_for_search,
                "kb_prompt": self.kb_prompt,
                "resource_id": str(self.resource_id) if self.resource_id else None,
                "environment": settings.ENVIRONMENT,
                "attachment_content": self.attachment_content,
            },
            "recursion_limit": self.recursion_limit,
            "metadata": {
                "langfuse_user_id": str(self.user_id) if self.user_id else None,
                "langfuse_session_id": str(self.conversation_id)
                if self.conversation_id
                else None,
            },
        }

        return result
