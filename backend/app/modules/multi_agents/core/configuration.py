"""Define the configurable parameters for the agent."""

from dataclasses import dataclass, field, fields
from typing import Annotated

from langchain_core.language_models import BaseChatModel
from langchain_core.runnables import RunnableConfig, ensure_config

from app.core.config import settings
from app.modules.multi_agents.config.config import WorkspaceConfigSchema

from ..config import MultiAgentConfig
from .utils import load_chat_model


@dataclass(kw_only=True)
class Configuration:
    """The base class for agent configurations."""

    model_name: Annotated[str, {"__template_metadata__": {"kind": "llm"}}] = field(
        default=settings.MAIN_MODEL,
        metadata={
            "description": "The name of the language model to use for the agent's main interactions. "
            "Should be in the form: provider/model-name."
        },
    )

    max_conversation_rounds: int = field(
        default=2,
        metadata={
            "description": "The maximum number of conversation rounds before summarizing the conversation and restarting."
        },
    )

    agents_config: MultiAgentConfig = field(
        default_factory=MultiAgentConfig,
        metadata={"description": "The configuration for the agents."},
    )

    workspace_config: WorkspaceConfigSchema = field(
        default_factory=WorkspaceConfigSchema,
        metadata={"description": "The configuration for the workspace."},
    )

    agent_role: str = field(
        default="assistant",
        metadata={"description": "The role of the current agent."},
    )

    @property
    def model(self) -> BaseChatModel:
        """Initialize and return the chat model"""
        return load_chat_model(self.model_name)

    @classmethod
    def from_runnable_config(cls, config: RunnableConfig | None = None):
        """Create a Configuration instance from a RunnableConfig object."""
        config = ensure_config(config)
        configurable = config.get("configurable") or {}
        _fields = {f.name for f in fields(cls) if f.init}
        return cls(**{k: v for k, v in configurable.items() if k in _fields})
