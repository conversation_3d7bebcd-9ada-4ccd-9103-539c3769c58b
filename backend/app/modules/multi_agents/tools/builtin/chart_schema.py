from enum import Enum

from pydantic import BaseModel, validator


class ChartType(str, Enum):
    BAR = "bar"
    PIE = "pie"
    LINE = "line"
    AREA = "area"
    STEP_AREA = "step_area"
    RADAR = "radar"


class ChartDataset(BaseModel):
    data: list[float]
    label: str | None = None

    @validator("data", pre=True)
    def validate_data(cls, v):
        """Validate and clean numeric data."""
        if not isinstance(v, list):
            return []

        cleaned_data = []
        for item in v:
            try:
                if isinstance(item, str):
                    # Clean string numbers
                    cleaned_str = item.strip()
                    # Remove leading zeros
                    cleaned_str = cleaned_str.lstrip("0") or "0"
                    # Handle decimal points
                    if cleaned_str.startswith("."):
                        cleaned_str = "0" + cleaned_str
                    if cleaned_str.endswith("."):
                        cleaned_str = cleaned_str[:-1]

                    cleaned_data.append(float(cleaned_str))
                else:
                    cleaned_data.append(float(item))
            except (ValueError, TypeError):
                # If conversion fails, use 0 as default
                cleaned_data.append(0.0)

        return cleaned_data


class ChartAxis(BaseModel):
    title: str | None = None
    type: str = "category"


class ChartStructuredOutput(BaseModel):
    """This schema is the same as the #chart tool but without description, when create this schema, please ensure the correct structure output"""

    title: str
    description: str | None
    chart_type: ChartType
    categories: list[str]
    datasets: list[ChartDataset]
    x_axis: ChartAxis
    y_axis: ChartAxis
    show_legend: bool
    show_grid: bool
    currency_format: bool
    percentage_format: bool
    position: int | None = 0
