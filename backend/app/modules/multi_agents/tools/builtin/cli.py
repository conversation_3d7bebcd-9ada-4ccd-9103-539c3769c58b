from typing import Annotated, Any

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

from .executor import call_executor


@tool
async def cli_script_execution(
    script: str, *, config: Annotated[RunnableConfig, InjectedToolArg], reasoning: str
) -> dict[str, Any]:
    """CLI Script Execution Tool.
    - OS: ubuntu:22.04

    Tips:
    - Consolidate multiple scripts to single large bash script as possible to reduce the # of call

    Supported CLI Tools:
    - aws-cli
    - gcloud

    Args:
        script: string
        - Script execution to the bash environment. This method is mainly purpose to execute cli tools.
        reasoning: string
        - Reasoning for the permission request
    """
    result = await call_executor(script, config)
    return result
