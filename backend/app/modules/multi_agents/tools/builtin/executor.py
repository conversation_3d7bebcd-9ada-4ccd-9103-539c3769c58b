import asyncio

import aiohttp
from langchain_core.runnables import RunnableConfig

from app.core.config import settings

from ...core import Configuration


async def call_executor(script: str, config: RunnableConfig | None = None):
    api_url = settings.EXECUTOR_HOST + "/api/v1/tools/run"
    params = {}
    if config:
        configuration = Configuration.from_runnable_config(config)
        workspace = configuration.workspace_config
        params = {
            "workspace_id": str(workspace.workspace_id),
            "aws_access_key_id": str(workspace.aws_access_key_id),
            "aws_secret_access_key": str(workspace.aws_secret_access_key),
            "aws_default_region": str(workspace.aws_default_region),
        }

    headers = {"accept": "application/json", "Content-Type": "text/plain"}

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                api_url, headers=headers, params=params, data=script, timeout=30
            ) as response:
                response.raise_for_status()
                result = await response.json()
                status = result.get("status", "unknown")
                output = result.get("result", "")

        except asyncio.TimeoutError:
            status = "timeout"
            output = "API request timed out after 30 seconds"
        except aiohttp.ClientError as e:
            status = "error"
            output = f"Error calling API: {str(e)}"
        except ValueError as e:
            status = "error"
            output = f"Error parsing API response: {str(e)}"
        except KeyError as e:
            status = "error"
            output = f"Unexpected API response format: {str(e)}"
        except Exception:
            status = "error"
            output = "Unexpected API Error"

    return {
        "status": status,
        "output": output,
    }
