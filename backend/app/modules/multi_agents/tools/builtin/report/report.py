import json
from typing import Annotated, Literal
from uuid import UUID

from langchain_core.runnables.base import RunnableConfig
from langchain_core.tools import BaseTool, InjectedToolArg
from langchain_core.tools.base import ArgsSchema

from app.api.deps import get_async_session
from app.logger import logger
from app.modules.multi_agents.core.states.global_state import ReportManager
from app.repositories.report import ReportRepository

from .schema import ExecutiveSummary, ReportInput, ReportSection


class ReportTool(BaseTool):
    name: str = "report"
    description: str = "A tool for managing report in a conversation."
    args_schema: ArgsSchema | None = ReportInput
    report_manager: ReportManager | None = None
    workspace_id: UUID | None = None
    conversation_id: UUID | None = None

    def __init__(self, report_manager: ReportManager | None = None, **kwargs):
        super().__init__(**kwargs)
        self.report_manager = report_manager
        self.workspace_id = None
        self.conversation_id = None

    @staticmethod
    def sanitize_data(data: dict) -> dict:
        """Sanitize data to prevent JSON/Python parsing errors."""
        if isinstance(data, dict):
            sanitized = {}
            for key, value in data.items():
                sanitized[key] = ReportTool.sanitize_data(value)
            return sanitized

    def _run(self):
        pass

    async def _arun(
        self,
        command: Literal[
            "create_outline",
            "update_sections",
            "remove_sections",
            "create_or_update_executive_summary",
            "get_report",
        ]
        | str,
        title: str | None = None,
        description: str | None = None,
        sections: list[ReportSection] | None = None,
        executive_summary: ExecutiveSummary | None = None,
        *,
        config: Annotated[RunnableConfig, InjectedToolArg],
    ):
        try:
            workspace_id = config.get("configurable", {}).get("workspace_id")
            conversation_id = config.get("configurable", {}).get("conversation_id")

            if command not in [
                "create_outline",
                "update_sections",
                "remove_sections",
                "create_or_update_executive_summary",
                "get_report",
            ]:
                return json.dumps(
                    {
                        "status": "error",
                        "message": "Invalid command",
                    }
                )

            # For create_outline, we need to create the structure first, then save
            if command == "create_outline":
                result = await self._create_outline(title, description, sections)
                # Save the report after creating outline
                if "success" in result:
                    current_report = self.report_manager.get_state()
                    try:
                        success = await self._create_or_update_report(
                            current_report,
                            workspace_id,
                            conversation_id,
                        )
                        if not success:
                            return json.dumps(
                                {
                                    "status": "error",
                                    "message": "Failed to save report outline to database",
                                }
                            )
                    except Exception as e:
                        logger.error(f"Failed to save report outline to database: {e}")
                        return json.dumps(
                            {
                                "status": "error",
                                "message": f"Failed to save report outline: {str(e)}",
                            }
                        )
                return result

            # Save current report state to DB first for other commands that modify the report
            if command != "get_report":
                current_report = self.report_manager.get_state()
                try:
                    success = await self._create_or_update_report(
                        current_report,
                        workspace_id,
                        conversation_id,
                    )
                    if not success:
                        return json.dumps(
                            {
                                "status": "error",
                                "message": "Failed to save current report state to database",
                            }
                        )
                except Exception as e:
                    logger.error(
                        f"Failed to save current report state to database: {e}"
                    )
                    return json.dumps(
                        {
                            "status": "error",
                            "message": f"Failed to save current report state: {str(e)}",
                        }
                    )

            if command == "update_sections":
                result = await self._update_sections(sections)
            elif command == "remove_sections":
                result = await self._remove_sections(sections)
            elif command == "create_or_update_executive_summary":
                result = await self._create_or_update_executive_summary(
                    executive_summary
                )
            elif command == "get_report":
                result = await self._get_report(workspace_id, conversation_id)

            # Save updated state to DB after command execution for modify commands
            if command in [
                "update_sections",
                "remove_sections",
                "create_or_update_executive_summary",
            ]:
                current_report = self.report_manager.get_state()
                try:
                    success = await self._create_or_update_report(
                        current_report,
                        workspace_id,
                        conversation_id,
                    )
                    if not success:
                        return json.dumps(
                            {
                                "status": "error",
                                "message": "Failed to save updated report state to database",
                            }
                        )
                except Exception as e:
                    logger.error(
                        f"Failed to save updated report state to database: {e}"
                    )
                    return json.dumps(
                        {
                            "status": "error",
                            "message": f"Failed to save updated report state: {str(e)}",
                        }
                    )

            return result
        except Exception as e:
            logger.error(f"Exception in _run: {str(e)}")
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Failed to run report tool: {str(e)}",
                }
            )

    async def _create_or_update_report(
        self,
        report_in: dict,
        workspace_id: UUID,
        conversation_id: UUID,
    ):
        try:
            async for async_session in get_async_session():
                repo = ReportRepository(async_session)
                result = await repo.create_or_update(
                    report_in, workspace_id, conversation_id
                )
                if result.failure:
                    logger.error(f"Failed to save report to database: {result.error}")
                    return False
                logger.info(f"Report saved successfully to database: {result.value.id}")
                return True
        except Exception as e:
            logger.error(f"Exception in _create_or_update_report: {str(e)}")
            return False

    async def _create_outline(
        self,
        title: str | None = None,
        description: str | None = None,
        sections: list[ReportSection] | None = None,
    ):
        if not title:
            return json.dumps(
                {
                    "status": "error",
                    "message": "Title is required to create report outline",
                }
            )

        current_report = self.report_manager.get_state()
        current_report["title"] = title
        current_report["description"] = description

        # Convert sections to dict format if provided
        if sections:
            current_report["sections"] = [section.model_dump() for section in sections]
        else:
            current_report["sections"] = []

        self.report_manager.set_state(current_report)

        return json.dumps(
            {
                "status": "success",
                "message": "Report outline created successfully",
            }
        )

    async def _update_sections(
        self,
        sections: list[ReportSection] | None = None,
    ):
        if not sections:
            return json.dumps(
                {
                    "status": "error",
                    "message": "No sections provided to update",
                }
            )

        current_report = self.report_manager.get_state()

        # Initialize sections if they don't exist
        if "sections" not in current_report:
            current_report["sections"] = []

        # Convert new sections to dict for easier processing
        new_sections_dict = {section.index: section for section in sections}

        # Update existing sections or add new ones
        existing_sections = {
            section.get("index"): section for section in current_report["sections"]
        }

        for index, new_section in new_sections_dict.items():
            if index in existing_sections:
                # Update existing section with non-None values
                existing_section = existing_sections[index]
                section_dict = new_section.model_dump(exclude_none=True)
                existing_section.update(section_dict)
            else:
                # Add new section
                existing_sections[index] = new_section.model_dump()

        # Convert back to list, sorted by index
        current_report["sections"] = [
            existing_sections[index] for index in sorted(existing_sections.keys())
        ]

        self.report_manager.set_state(current_report)

        return json.dumps(
            {
                "status": "success",
                "message": "Report section updated successfully",
            }
        )

    async def _remove_sections(
        self,
        sections: list[ReportSection] | None = None,
    ):
        if not sections:
            return json.dumps(
                {
                    "status": "error",
                    "message": "No sections provided to remove",
                }
            )

        current_report = self.report_manager.get_state()
        if not current_report.get("sections"):
            return json.dumps(
                {
                    "status": "error",
                    "message": "No sections exist in the report to remove",
                }
            )

        indices = [section.index for section in sections]
        current_report["sections"] = [
            section
            for section in current_report["sections"]
            if section.get("index") not in indices
        ]
        self.report_manager.set_state(current_report)

        return json.dumps(
            {
                "status": "success",
                "message": "Report section removed successfully",
            }
        )

    async def _create_or_update_executive_summary(
        self,
        executive_summary: ExecutiveSummary | None = None,
    ):
        current_report = self.report_manager.get_state()
        current_report["executive_summary"] = (
            executive_summary.model_dump() if executive_summary else None
        )
        self.report_manager.set_state(current_report)

        return json.dumps(
            {
                "status": "success",
                "message": "Report executive summary created successfully",
            }
        )

    async def _get_report(
        self,
        workspace_id: UUID | None = None,
        conversation_id: UUID | None = None,
    ):
        async for async_session in get_async_session():
            repo = ReportRepository(async_session)
            result = await repo.get_by_conversation_id(conversation_id, workspace_id)
            if result.failure:
                return json.dumps(
                    {
                        "status": "error",
                        "message": "Failed to fetch report",
                    }
                )
            if result.value is None:
                return json.dumps(
                    {
                        "status": "error",
                        "message": "Report not found",
                    }
                )
            return json.dumps(
                {
                    "status": "success",
                    "message": "Report fetched successfully",
                }
            )
