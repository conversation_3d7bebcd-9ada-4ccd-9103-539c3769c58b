import re
from datetime import datetime
from enum import Enum
from typing import Literal

from pydantic import BaseModel, Field, ValidationInfo, field_validator


class ChartType(str, Enum):
    BAR = "bar"
    PIE = "pie"
    LINE = "line"
    AREA = "area"
    STEP_AREA = "step_area"
    RADAR = "radar"


class ChartDataset(BaseModel):
    data: list[float]
    label: str | None = None

    @field_validator("data")
    def validate_data(cls, v):
        """Validate and clean numeric data."""
        if not isinstance(v, list):
            return []

        cleaned_data = []
        for item in v:
            try:
                if isinstance(item, str):
                    # Clean string numbers
                    cleaned_str = item.strip()
                    # Remove leading zeros
                    cleaned_str = cleaned_str.lstrip("0") or "0"
                    # Handle decimal points
                    if cleaned_str.startswith("."):
                        cleaned_str = "0" + cleaned_str
                    if cleaned_str.endswith("."):
                        cleaned_str = cleaned_str[:-1]

                    cleaned_data.append(float(cleaned_str))
                else:
                    cleaned_data.append(float(item))
            except (ValueError, TypeError):
                # If conversion fails, use 0 as default
                cleaned_data.append(0.0)

        return cleaned_data


class ChartAxis(BaseModel):
    title: str | None = None
    type: str = "category"


class ChartStructuredOutput(BaseModel):
    """This schema is the same as the #chart tool but without description, when create this schema, please ensure the correct structure output"""

    title: str
    description: str | None
    chart_type: ChartType
    categories: list[str]
    datasets: list[ChartDataset]
    x_axis: ChartAxis
    y_axis: ChartAxis
    show_legend: bool
    show_grid: bool
    currency_format: bool
    percentage_format: bool
    position: int | None = 0


class TableColumnConfig(BaseModel):
    """Configuration for a table column"""

    header: str = Field(description="The header text")
    use_badge: bool = Field(
        default=False, description="Whether to render the cell content as a badge"
    )


class TableStructuredOutput(BaseModel):
    title: str = Field(description="The title of the table")
    description: str | None = Field(
        default=None, description="The description of the table"
    )
    columns: list[TableColumnConfig] | list[dict] = Field(
        description="The columns of the table"
    )
    rows: list[list[str]] = Field(
        description="The rows of the table, each row is a list of strings and seperated by comma"
    )

    @field_validator("rows")
    def validate_rows(cls, v, info: ValidationInfo):
        """Ensure all rows have the same length as headers."""
        if (
            hasattr(info, "data")
            and info.data
            and "columns" in info.data
            and info.data["columns"]
        ):
            header_count = len(info.data["columns"])
            for i, row in enumerate(v):
                if len(row) != header_count:
                    # Pad or truncate row to match header count
                    if len(row) < header_count:
                        v[i] = row + [""] * (header_count - len(row))
                    else:
                        v[i] = row[:header_count]
        return v


class CardStructuredOutput(BaseModel):
    """When using the card, you have to use at least 2 cards to show the data (call CardStructuredOutput 2 times)"""

    title: str = Field(description="The title of the card")
    description: str | None = Field(
        default=None, description="The description of the card"
    )
    lucide_icon: str | None = Field(
        default=None,
        description="The lucide icon of the card with uppercase first letter",
    )
    data: str | None = Field(default=None, description="The data of the card")

    @field_validator("data")
    def clean_data(cls, v):
        """Clean numeric data to prevent invalid literals."""
        if v is not None:
            # Remove leading zeros from numbers
            v = re.sub(r"\b0+(\d+)", r"\1", str(v))
            # Fix decimal formatting
            v = re.sub(r"(?<!\d)\.(\d+)", r"0.\1", v)
            v = re.sub(r"(\d+)\.(?!\d)", r"\1", v)
        return v


class Content(BaseModel):
    index: int = Field(description="The index of the content")
    type: Literal["paragraph", "chart", "table", "card"] = Field(
        description="The type of the content"
    )
    content: (
        str
        | ChartStructuredOutput
        | TableStructuredOutput
        | CardStructuredOutput
        | dict
        | None
    ) = Field(
        default=None,
        description="""
            The data of the content, please use plain text for the paragraph (no markdown)
        """,
    )


class ExecutiveSummary(BaseModel):
    key_findings: list[str] = Field(description="The key findings of the report")
    business_impact: list[str] = Field(description="The business impact of the report")


class ReportSection(BaseModel):
    index: int = Field(description="The index of the section")
    header: str | None = Field(
        default=None,
        description="""
            The header of the section, example: "Monthly Cost Trends Analysis", "Service-Level Cost Analysis", "Resource Utilization Analysis, "Detailed Optimization Recommendations", "Security Operations Metrics", "Threat Landscape & Attack Patterns", "Detailed Security Findings & Risk Analysis", "Compliance Framework Assessment".
        """,
    )
    content: list[Content] | list[dict] | None = Field(
        default=None,
        description="The content of the section, each content has an index",
    )


class ReportInput(BaseModel):
    """
    A tool for managing report in a conversation.

    ###### TOOL USE POLICY #####

    Tool Tag: #report
    Tool Use Policy: #manual
    ##########################

    Commands:
    1. create_outline:
       Required: title, description, sections
       NOTE: YOU DONT NEED TO FILL THE CONTENT, YOU CAN FILL THEM LATER.

    2. update_sections:
       Required: sections
       NOTE: WHEN YOU UPDATE THE SECTIONS, YOU DONT NEED TO FILL ALL FIELDS WITHIN THE SECTION, IF A FIELD IS ALREADY FILLED, KEEP IT NONE. IF TITLE OR THE DESCRIPTION IS ALREADY FILLED, KEEP IT NONE. YOU CAN ALSO ADD NEW SECTIONS TO THE REPORT.

    3. remove_sections:
       Required: sections
       NOTE: ONLY USE THE INDEX OF THE SECTION TO REMOVE.

    4. create_or_update_executive_summary:
       Required: executive_summary
       NOTE: ONLY CALL THIS TOOL IF YOU ARE SURE THAT THE EXECUTIVE SUMMARY IS COMPLETE AND YOU HAVE ALL THE INFORMATION YOU NEED.

    5. get_report:
       Required: None
       NOTE: ONLY CALL THIS TOOL WHEN YOU WANT TO GET THE WHOLE REPORT.
    """

    command: Literal[
        "create_outline",
        "update_sections",
        "remove_sections",
        "create_or_update_executive_summary",
        "get_report",
    ]
    title: str | None = Field(default=None, description="The title of the whole report")
    description: str | None = Field(
        default=None, description="The description of the whole report"
    )
    sections: list[ReportSection] | list[dict] | None = Field(
        default=None,
        description="""
            The sections within the report. Don't add the Executive Summary in this field, you should add it in the `executive_summary` field.
        """,
    )
    executive_summary: ExecutiveSummary | dict | None = Field(
        default=None,
        description="""
            The executive summary of the report, ONLY CALL THIS TOOL WHEN YOU COMPLETE THE REPORT AND YOU ARE SURE THAT THE EXECUTIVE SUMMARY IS COMPLETE AND YOU HAVE ALL THE INFORMATION YOU NEED.
        """,
    )

    class Config:
        extra = "ignore"
        validate_assignment = True
        arbitrary_types_allowed = False
        use_enum_values = True
        validate_default = True
        allow_population_by_field_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
