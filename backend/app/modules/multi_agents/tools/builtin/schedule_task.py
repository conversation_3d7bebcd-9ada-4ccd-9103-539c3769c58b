from typing import Annotated, Any

from langchain_core.runnables.base import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

from app.tasks.shedule_task import create_task


@tool
def schedule_task(
    title: str,
    description: str,
    schedule: str,
    *,
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> dict[str, Any]:
    """
    ###### TOOL USE POLICY #####
    Tool Tag: #schedule
    Tool Use Policy: #manual
    ##########################

    The Schedule Task Tool enables to schedule tasks for team to work on at a later time.

    Args:
        title: Title of the task
        description: Clear description of what the task is
        schedule: Schedule for the task in cron expression format (e.g. "0 0 * * *" for daily at midnight)

    Returns:
        dict: A dictionary with the status of the task
    """
    workspace_id = config.get("configurable", {}).get("workspace_id", None)
    user_id = config.get("configurable", {}).get("user_id", None)

    # Submit task creation task
    create_task.delay(
        title=title,
        description=description,
        schedule=schedule,
        workspace_id=workspace_id,
        user_id=user_id,
    )

    task_data = {"status": "submitted"}

    return task_data
