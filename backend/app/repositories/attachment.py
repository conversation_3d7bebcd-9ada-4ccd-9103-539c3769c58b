from uuid import <PERSON><PERSON><PERSON>
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession
from app.models import MessageAttachment
from app.schemas.message_attachment import UploadedAttachmentInfo
from app.logger import logger

class AttachmentRepository:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def create_attachment(
        self,
        file_info: UploadedAttachmentInfo,
        owner_id: UUID,
    ) -> MessageAttachment:
        """
        Create a new message attachment record in the database.
        """
        try:
            attachment = MessageAttachment(
                owner_id=owner_id,
                filename=file_info.filename,
                original_filename=file_info.filename, # Assuming original is same as sanitized for now
                file_type=file_info.content_type,
                file_size=file_info.file_size,
                storage_key=file_info.storage_key,
            )
            self.session.add(attachment)
            await self.session.commit()
            await self.session.refresh(attachment)
            return attachment
        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error creating attachment record: {e}")
            raise

    async def get_attachment_by_id(self, attachment_id: UUID) -> MessageAttachment | None:
        """
        Retrieve an attachment by its ID.
        """
        statement = select(MessageAttachment).where(MessageAttachment.id == attachment_id)
        result = await self.session.exec(statement)
        return result.one_or_none()

    async def link_attachment_to_message(self, attachment_id: UUID, message_id: UUID) -> MessageAttachment | None:
        """
        Update an attachment to link it to a message.
        """
        attachment = await self.get_attachment_by_id(attachment_id)
        if attachment:
            attachment.message_id = message_id
            await self.session.commit()
            await self.session.refresh(attachment)
        return attachment