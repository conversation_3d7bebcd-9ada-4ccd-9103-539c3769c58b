from datetime import datetime
from uuid import UUID

from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.models import (
    Agent,
    AgentConnector,
    MCPServer,
    MCPServerBase,
    MCPServerStatus,
    MCPServerTransport,
)


class MCPServerRepository:
    """Repository for managing MCP Server entities."""

    def __init__(self, session: AsyncSession):
        """Initialize with an async database session."""
        self.session = session

    def _validate_url_for_transport_type(
        self, config: dict, transport_type: MCPServerTransport
    ) -> dict:
        """
        Validate and fix URL suffix based on transport type.

        Args:
            config: Server configuration dict
            transport_type: MCP server transport type

        Returns:
            Updated config dict with correct URL suffix

        Raises:
            ValueError: If URL format is invalid for the transport type
        """
        if not config or "url" not in config:
            return config

        url = config.get("url", "").strip()
        if not url:
            return config

        # Create a copy of config to avoid modifying the original
        updated_config = config.copy()

        # Remove any existing /sse or /mcp suffixes
        clean_url = url.rstrip("/")
        if clean_url.endswith("/sse") or clean_url.endswith("/mcp"):
            clean_url = clean_url.rsplit("/", 1)[0]

        # Add the correct suffix based on transport type
        if transport_type == MCPServerTransport.SSE:
            if not clean_url.endswith("/sse"):
                updated_config["url"] = f"{clean_url}/sse"
        elif transport_type == MCPServerTransport.STREAMABLE_HTTP:
            if not clean_url.endswith("/mcp"):
                updated_config["url"] = f"{clean_url}/mcp"

        return updated_config

    async def create_mcp_server(
        self, workspace_id: UUID, mcp_server_data: MCPServerBase
    ) -> MCPServer:
        """Create a new MCP server for a workspace."""
        try:
            # Validate and fix URL suffix for transport type
            validated_config = self._validate_url_for_transport_type(
                mcp_server_data.config, mcp_server_data.type
            )

            # Create new MCPServer instance
            db_mcp_server = MCPServer(
                **mcp_server_data.model_dump(exclude={"config"}),
                config=validated_config,
                workspace_id=workspace_id,
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )

            self.session.add(db_mcp_server)
            await self.session.commit()
            await self.session.refresh(db_mcp_server)
            return db_mcp_server
        except Exception as e:
            await self.session.rollback()
            raise e

    async def get_mcp_servers(
        self, workspace_id: UUID, active_only: bool = False
    ) -> list[MCPServer] | None:
        """Get all MCP servers for a workspace."""
        try:
            statement = (
                select(MCPServer)
                .where(MCPServer.workspace_id == workspace_id)
                .order_by(MCPServer.created_at.desc())
            )

            if active_only:
                statement = statement.where(MCPServer.is_active == True)

            result = await self.session.exec(statement)
            mcp_servers = result.all()

            return mcp_servers if mcp_servers else None
        except Exception as e:
            raise e

    async def get_mcp_server(
        self, server_id: UUID, workspace_id: UUID
    ) -> MCPServer | None:
        """Get a specific MCP server by ID for a workspace."""
        try:
            statement = select(MCPServer).where(
                MCPServer.id == server_id, MCPServer.workspace_id == workspace_id
            )
            result = await self.session.exec(statement)
            return result.one_or_none()
        except Exception as e:
            raise e

    async def update_mcp_server(
        self,
        server_id: UUID,
        workspace_id: UUID,
        name: str | None = None,
        prefix: str | None = None,
        type: MCPServerTransport | None = None,
        config: dict | None = None,
        is_active: bool | None = None,
        tool_permissions: list[str] | None = None,
        tool_enabled: list[str] | None = None,
        status: MCPServerStatus | None = None,
        status_message: str | None = None,
        tool_list: list[str] | None = None,
        status_updated_at: datetime | None = None,
    ) -> MCPServer | None:
        """Update an MCP server."""
        try:
            # Get the existing server
            server = await self.get_mcp_server(server_id, workspace_id)
            if not server:
                return None

            # Determine the transport type to use for validation
            # Use the new type if provided, otherwise use the existing type
            transport_type = type if type is not None else server.type

            # Handle config update with URL validation
            if config is not None:
                # Validate and fix URL suffix for transport type
                validated_config = self._validate_url_for_transport_type(
                    config, transport_type
                )
                server.config = validated_config
            elif type is not None and server.config:
                # If only type is being updated, validate existing config URL
                validated_config = self._validate_url_for_transport_type(
                    server.config, transport_type
                )
                server.config = validated_config

            # Update fields only if they are provided (not None)
            if name is not None:
                server.name = name
            if prefix is not None:
                server.prefix = prefix
            if type is not None:
                server.type = type
            if is_active is not None:
                server.is_active = is_active
            if tool_permissions is not None:
                server.tool_permissions = tool_permissions
            if tool_enabled is not None:
                server.tool_enabled = tool_enabled
            if status is not None:
                server.status = status
            if status_message is not None:
                server.status_message = status_message
            if tool_list is not None:
                server.tool_list = tool_list
            if status_updated_at is not None:
                server.status_updated_at = status_updated_at

            # Always update timestamp
            server.updated_at = datetime.now()

            self.session.add(server)
            await self.session.commit()
            await self.session.refresh(server)
            return server
        except Exception as e:
            await self.session.rollback()
            raise e

    async def delete_mcp_server(self, server_id: UUID, workspace_id: UUID) -> bool:
        """Delete an MCP server (hard delete)."""
        try:
            server = await self.get_mcp_server(server_id, workspace_id)
            if not server or server.is_builtin:
                return False

            # Clean up references to this MCP server from AgentConnectors in the workspace
            await self._cleanup_agent_connector_references(server, workspace_id)

            await self.session.delete(server)
            await self.session.commit()
            return True
        except Exception as e:
            await self.session.rollback()
            raise e

    async def _cleanup_agent_connector_references(
        self, server: MCPServer, workspace_id: UUID
    ) -> None:
        """Remove MCP server references from AgentConnectors in the workspace."""
        try:
            # Find all AgentConnectors in the workspace that reference this MCP server
            statement = (
                select(AgentConnector)
                .join(Agent, AgentConnector.agent_id == Agent.id)
                .where(Agent.workspace_id == workspace_id)
            )

            result = await self.session.exec(statement)
            agent_connectors = result.all()

            for agent_connector in agent_connectors:
                if agent_connector.mcp_servers:
                    # Remove any references to this server (by ID, name, or prefix)
                    original_servers = agent_connector.mcp_servers.copy()
                    agent_connector.mcp_servers = [
                        mcp_server
                        for mcp_server in agent_connector.mcp_servers
                        if mcp_server != server.name
                    ]

                    # Only update if there were changes
                    if original_servers != agent_connector.mcp_servers:
                        self.session.add(agent_connector)

            # Commit the AgentConnector updates
            await self.session.commit()

        except Exception as e:
            await self.session.rollback()
            raise e
