from uuid import <PERSON><PERSON><PERSON>

from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.logger import logger
from app.models import (
    Report,
)

from .base import Result


class ReportRepository:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_by_conversation_id(self, conversation_id: UUID, workspace_id: UUID):
        try:
            statement = select(Report).where(
                Report.conversation_id == conversation_id,
                Report.workspace_id == workspace_id,
            )
            result = await self.session.exec(statement)
            result = result.first()
            if result is None:
                return Result.Fail("Report not found for the given conversation")
            return Result.Ok(result)
        except Exception as e:
            return Result.Fail(str(e))

    async def create_or_update(
        self,
        report_in: dict,
        workspace_id: UUID,
        conversation_id: UUID,
    ):
        try:
            current_report = await self.get_by_conversation_id(
                conversation_id, workspace_id
            )

            if current_report.failure:
                report_data = {
                    "conversation_id": conversation_id,
                    "workspace_id": workspace_id,
                    **report_in,
                }
                report = Report(**report_data)
                self.session.add(report)
            else:
                report = current_report.value
                for key, value in report_in.items():
                    if key in report.__dict__:
                        setattr(report, key, value)

                self.session.add(report)

            await self.session.commit()
            await self.session.refresh(report)
            return Result.Ok(report)

        except Exception as e:
            await self.session.rollback()
            logger.error(f"Error in create_or_update: {str(e)}")
            return Result.Fail(str(e))
