from uuid import UUID

from sqlalchemy.orm import selectinload
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.models import User, UserUpdate, UserWorkspace


class UserRepository:
    """
    Simple User repository with basic CRUD operations by user ID.
    """

    def __init__(self, async_session: AsyncSession):
        self.async_session = async_session

    async def create_user(self, user: User) -> User:
        """
        Create a new user.

        Args:
            user: User object to create

        Returns:
            Created User object
        """
        self.async_session.add(user)
        await self.async_session.commit()
        await self.async_session.refresh(user)
        return user

    async def get_user_by_id(self, user_id: UUID) -> User | None:
        """
        Get a user by ID.

        Args:
            user_id: UUID of the user

        Returns:
            User object if found, None otherwise
        """
        return await self.async_session.get(User, user_id)

    async def get_user_with_workspaces(self, user_id: UUID) -> User | None:
        """
        Get a user by ID with workspace relationships eagerly loaded.
        This prevents async lazy loading issues when accessing workspace relationships.

        Args:
            user_id: UUID of the user

        Returns:
            User object with workspaces loaded if found, None otherwise
        """
        user_query = (
            select(User)
            .where(User.id == user_id)
            .options(
                selectinload(User.own_workspaces),
                selectinload(User.workspaces).selectinload(UserWorkspace.workspace),
            )
        )
        result = await self.async_session.execute(user_query)
        return result.scalars().first()

    async def update_user(self, user_id: UUID, user_update: UserUpdate) -> User | None:
        """
        Update a user by ID.

        Args:
            user_id: UUID of the user to update
            user_update: UserUpdate object with fields to update

        Returns:
            Updated User object if found, None otherwise
        """
        db_user = await self.async_session.get(User, user_id)
        if db_user is None:
            return None

        # Update only provided fields
        for field, value in user_update.model_dump(exclude_unset=True).items():
            if value is not None:
                setattr(db_user, field, value)

        self.async_session.add(db_user)
        await self.async_session.commit()
        await self.async_session.refresh(db_user)
        return db_user

    async def delete_user(self, user_id: UUID) -> bool:
        """
        Delete a user by ID.

        Args:
            user_id: UUID of the user to delete

        Returns:
            True if user was found and deleted, False otherwise
        """
        db_user = await self.async_session.get(User, user_id)
        if db_user is None:
            return False

        await self.async_session.delete(db_user)
        await self.async_session.commit()
        return True
