from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, <PERSON>

from app.models import MCPServerStatus, MCPServerTransport


class MCPServerCreateSchema(BaseModel):
    """Schema for creating an MCP server"""

    name: str = Field(
        ..., min_length=1, max_length=255, description="Name of the MCP server"
    )
    prefix: str = Field(
        ..., min_length=1, max_length=255, description="Prefix for the MCP server"
    )
    type: MCPServerTransport = Field(
        default=MCPServerTransport.STREAMABLE_HTTP, description="Transport type"
    )
    config: dict = Field(default_factory=dict, description="Server configuration")
    is_active: bool = Field(default=True, description="Whether the server is active")
    is_builtin: bool = Field(
        default=False, description="Whether this is a builtin server"
    )
    tool_permissions: list[str] = Field(
        default_factory=list, description="Tool permissions"
    )
    tool_enabled: list[str] = Field(default_factory=list, description="Enabled tools")


class MCPServerUpdateSchema(BaseModel):
    """Schema for updating an MCP server"""

    name: str | None = Field(
        None, min_length=1, max_length=255, description="Name of the MCP server"
    )
    prefix: str | None = Field(
        None, min_length=1, max_length=255, description="Prefix for the MCP server"
    )
    type: MCPServerTransport | None = Field(None, description="Transport type")
    config: dict | None = Field(None, description="Server configuration")
    is_active: bool | None = Field(None, description="Whether the server is active")
    tool_permissions: list[str] | None = Field(None, description="Tool permissions")
    tool_enabled: list[str] | None = Field(None, description="Enabled tools")


class MCPServerConnectionResult(BaseModel):
    """Schema for MCP server connection result"""

    status: MCPServerStatus
    status_message: str
    tool_list: list[str]


class MCPServerResponseSchema(BaseModel):
    """Schema for MCP server response"""

    id: UUID
    workspace_id: UUID
    name: str
    prefix: str
    type: MCPServerTransport
    config: dict
    is_active: bool
    is_builtin: bool
    tool_list: list[str]
    tool_permissions: list[str]
    tool_enabled: list[str]
    status: MCPServerStatus
    status_message: str
    status_updated_at: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class MCPServerListResponseSchema(BaseModel):
    """Schema for paginated MCP server list response"""

    data: list[MCPServerResponseSchema]
    count: int


class MCPServerConfigValidationSchema(BaseModel):
    """Schema for validating MCP server configuration"""

    url: str = Field(..., max_length=255, description="Server URL")
    headers: dict = Field(default_factory=dict, description="HTTP headers")
    timeout: float = Field(default=5.0, gt=0, description="Connection timeout")
    sse_read_timeout: float = Field(default=30.0, gt=0, description="SSE read timeout")
