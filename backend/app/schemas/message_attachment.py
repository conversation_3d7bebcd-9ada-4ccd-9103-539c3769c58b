from uuid import UUID
from pydantic import BaseModel, Field
from app.models import AsyncTask<PERSON>tatus

# Constants
MAX_FILE_SIZE = 25 * 1024 * 1024  # 25MB

# Base Schemas
class AttachmentFileInfo(BaseModel):
    file_id: str = Field(..., description="Client-side unique ID for the file.")
    filename: str = Field(..., max_length=255, description="Original name of the file.")
    content_type: str = Field(..., max_length=255, description="MIME type of the file.")
    file_size: int = Field(
        ..., gt=0, le=MAX_FILE_SIZE, description="Size of the file in bytes."
    )

# Presigned URL Generation
class AttachmentPresignedUrlRequest(BaseModel):
    files: list[AttachmentFileInfo] = Field(
        ..., description="List of files to generate presigned URLs for."
    )

class PresignedUrlInfo(BaseModel):
    file_id: str = Field(..., description="Client-side unique ID for the file.")
    filename: str = Field(..., description="Sanitized name of the file.")
    storage_key: str = Field(..., description="The key for the object in storage.")
    presigned_url: str = Field(..., description="The presigned URL for uploading.")

class AttachmentPresignedUrlResponse(BaseModel):
    presigned_urls: list[PresignedUrlInfo] = Field(
        ..., description="List of presigned URLs and associated file info."
    )

# Upload Confirmation
class UploadedAttachmentInfo(BaseModel):
    file_id: str = Field(..., description="Client-side unique ID for the file.")
    filename: str = Field(..., description="Sanitized name of the file.")
    storage_key: str = Field(..., description="The key for the object in storage.")
    content_type: str = Field(..., description="MIME type of the file.")
    file_size: int = Field(..., description="Size of the file in bytes.")

class AttachmentConfirmRequest(BaseModel):
    uploaded_files: list[UploadedAttachmentInfo] = Field(
        ..., description="List of files that have been successfully uploaded."
    )

# Task Status
class TaskStatusResponse(BaseModel):
    task_id: str
    status: AsyncTaskStatus
    status_message: str | None = None
    progress: int = 0
    result: dict | None = None
    error: str | None = None

# Download URL
class AttachmentDownloadResponse(BaseModel):
    attachment_id: UUID = Field(..., description="The ID of the attachment.")
    download_url: str = Field(..., description="The presigned URL for downloading the file.")
