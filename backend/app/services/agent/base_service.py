import contextlib
import copy
import logging
import uuid
from typing import Any

from slugify import slugify
from sqlmodel import Session, func, select, update
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.db import async_engine
from app.models import (
    Agent,
    AgentConnector,
    AgentConnectorBuiltInConnector,
    BuiltInConnector,
    Conversation,
    KBUsageMode,
    Message,
    MessageActionType,
    MessageAttachment,
    WorkspaceBuiltInConnector,
)
from app.modules.multi_agents.config.config import (
    ConversationConfigSchema,
    MultiAgentConfig,
    WorkspaceConfigSchema,
)
from app.modules.multi_agents.core.agents.factory import AgentFactory
from app.modules.multi_agents.tools.builtin.kb import parse_document_mention
from app.repositories.agent_context import AgentContextRepository
from app.repositories.kb import KBRepository
from app.repositories.workspace_builtin_connector import (
    WorkspaceBuiltInConnectorRepository,
)
from app.schemas.mcp_server import MCPServerListResponseSchema, MCPServerResponseSchema
from app.services.attachment_service import AttachmentService
from app.services.mcp_server_service import MCPServerService

from .checkpoint_handler import CheckpointHandler
from .component_handler import ComponentHandler
from .message_handler import MessageHandler
from .stream_handler import StreamHandler
from .thought_handler import ThoughtHandler
from .token_usage_handler import TokenUsageHandler

logger = logging.getLogger(__name__)


class AgentBaseService:
    """Base agent service for managing and executing agents."""

    def __init__(self, session: Session):
        """Initialize the service.

        Args:
            session: SQLModel session for database operations
        """
        self.session = session

        # Initialize handlers
        self.message_handler = MessageHandler(session)
        self.thought_handler = ThoughtHandler(session)
        self.component_handler = ComponentHandler(session)
        self.checkpoint_handler = CheckpointHandler(session)
        self.token_usage_handler = TokenUsageHandler(session)
        self.stream_handler = StreamHandler(self.thought_handler, self.message_handler)
        self.attachment_service = None

    async def _get_attachment_service(self) -> AttachmentService:
        """Get or create an async attachment service."""
        if self.attachment_service is None:
            async_session = AsyncSession(async_engine)
            self.attachment_service = AttachmentService(async_session)
        return self.attachment_service

    # Delegate methods to appropriate handlers
    def _create_message(self, conversation_id: uuid.UUID, content: str, role: str):
        return self.message_handler.create_message(conversation_id, content, role)

    def _create_thought(
        self,
        message_id: uuid.UUID,
        position: int,
        answer: str = "",
        tool: str = "",
        tool_input: dict | None = None,
        observation: str = "",
    ):
        return self.thought_handler.create_thought(
            message_id, position, answer, tool, tool_input, observation
        )

    def _get_last_interrupted_message(self, conversation_id: uuid.UUID):
        return self.message_handler.get_last_interrupted_message(conversation_id)

    def _handle_stream_event(
        self,
        event,
        current_thought,
        response_text,
        response_message,
        thought_idx,
    ):
        return self.stream_handler.handle_stream_event(
            event, current_thought, response_text, response_message, thought_idx
        )

    def _handle_interrupt(
        self, response_message, interrupt_message, accumulated_content=""
    ):
        return self.message_handler.handle_interrupt(
            response_message, interrupt_message, accumulated_content
        )

    def _update_message_checkpoint(
        self, message, start_checkpoint_id, end_checkpoint_id
    ):
        return self.checkpoint_handler.update_message_checkpoint(
            message, start_checkpoint_id, end_checkpoint_id
        )

    def _save_final_response(self, response_message, content, role=None):
        return self.message_handler.save_final_response(response_message, content, role)

    def _cleanup_on_error(self, response_message, user_message):
        return self.message_handler.cleanup_on_error(response_message, user_message)

    def _format_message(self, message):
        return self.message_handler.format_message(message)

    def _format_thought(self, thought, message_id):
        return self.message_handler._format_thought(thought, message_id)

    def _handle_token_usage(self, event, input_token_usage, output_token_usage):
        return self.stream_handler.handle_token_usage(
            event, input_token_usage, output_token_usage
        )

    def _handle_chain_stream_debug(
        self,
        event_data,
        first_checkpoint_id,
        last_checkpoint_id,
        response_message,
        current_namespace,
        resume,
        full_message_text,
    ):
        return self.stream_handler.handle_chain_stream_debug(
            event_data,
            first_checkpoint_id,
            last_checkpoint_id,
            response_message,
            current_namespace,
            resume,
            full_message_text,
        )

    def _process_chart_component(
        self, response_message, chart_data, component_position
    ):
        return self.component_handler.process_chart_component(
            response_message, chart_data, component_position
        )

    def _save_recommendations_as_components(
        self,
        message,
        recommendations_data,
        component_position,
        additional_metadata=None,
    ):
        return self.component_handler.save_recommendations_as_components(
            message, recommendations_data, component_position, additional_metadata
        )

    def get_conversation_history(self, conversation_id, limit=20):
        return self.message_handler.get_conversation_history(conversation_id, limit)

    async def check_out_of_quota(self, workspace_id):
        return await self.token_usage_handler.check_out_of_quota(workspace_id)

    def track_token_usage(
        self,
        message_id,
        workspace_id,
        input_token_count,
        output_token_count,
        model_id="us.anthropic.claude-3-5-sonnet-20240620-v2:0",
        model_provider="bedrock",
    ) -> None:
        return self.token_usage_handler.track_token_usage(
            message_id,
            workspace_id,
            input_token_count,
            output_token_count,
            model_id,
            model_provider,
        )

    def trigger_memory_extraction(self, conversation_id: uuid.UUID) -> None:
        try:
            from app.tasks.memory_tasks import extract_key_learnings_task

            # Queue the task to run in the background
            task = extract_key_learnings_task.delay(str(conversation_id))

            logger.debug(
                f"Triggered memory extraction for conversation {conversation_id} (task ID: {task.id})"
            )
        except ImportError as e:
            logger.error(f"Failed to import memory tasks module: {str(e)}")
        except Exception as e:
            logger.error(
                f"Failed to trigger memory extraction for conversation {conversation_id}: {str(e)}"
            )
            logger.exception("Memory extraction error details:")

    async def _prepare_agent_config(
        self,
        conversation: Conversation,
        graph_name: str = "autonomous_agent",
    ):
        """Prepare agent configuration with workspace and context settings."""
        graph = AgentFactory.get_pre_compiled_graph(graph_name)
        agents_config = copy.deepcopy(
            AgentFactory.get_pre_compiled_graph_config(graph_name)
        )

        # Add context to the config
        if conversation.resource_id:
            resource_context = conversation.resource.get_resource_context()
        else:
            resource_context = ""

        # Add region constraints to the config
        region_constraints = " ,".join(conversation.agent.workspace.settings.regions)

        # Init repo
        repo = AgentContextRepository(session=self.session)

        # We'll get agent UUIDs by querying all workspace agents
        async with self.async_session() as async_session:
            # Query all active agents in the workspace
            agents_stmt = select(Agent).where(
                Agent.workspace_id == conversation.agent.workspace_id,
                Agent.is_active == True,
                Agent.is_deleted == False,
            )
            agents_result = await async_session.exec(agents_stmt)
            workspace_agents = agents_result.all()

            # Create a mapping of agent name (first part of title) to UUID
            agent_name_to_id = {}
            for agent in workspace_agents:
                agent_name = agent.title.split(" ")[0]
                agent_name_to_id[agent_name] = agent.id

        # Add context to the config
        for agent_id in agents_config.agents.keys():
            # Get the corresponding UUID from our mapping
            uuid_agent_id = agent_name_to_id.get(agent_id, None)
            if uuid_agent_id:
                agent_context = repo.get_by_agent_id(uuid_agent_id)

                # Add resource, agent context and agent id to the config
                agents_config.agents[agent_id].context = resource_context
                agents_config.agents[agent_id].agent_context = (
                    agent_context.context if agent_context else ""
                )
                agents_config.agents[agent_id].agent_id = str(uuid_agent_id)

                # Add region constraints to the config
                agents_config.agents[agent_id].region_constraints = region_constraints

        # Add mcp config to the config
        async with self.async_session() as async_session:
            mcp_servers: MCPServerListResponseSchema = await MCPServerService(
                async_session
            ).get_mcp_servers(conversation.agent.workspace_id)
            mcp_servers: list[MCPServerResponseSchema] = mcp_servers.data

            # Query to get all agents in the workspace
            agents_stmt = select(Agent).where(
                Agent.workspace_id == conversation.agent.workspace_id
            )
            agents_result = await async_session.exec(agents_stmt)
            workspace_agents = agents_result.all()

            # Create a dictionary to store agent connectors
            all_agent_builtin_connectors = {}

            # Get the agent instructions
            agent_instructions = {
                agent.title.split(" ")[0]: agent.instructions
                for agent in workspace_agents
            }

            # For each agent, get their built-in connectors
            for agent in workspace_agents:
                # Get the agent connector
                agent_connector_stmt = select(AgentConnector).where(
                    AgentConnector.agent_id == agent.id
                )
                agent_connector_result = await async_session.exec(agent_connector_stmt)
                agent_connector = agent_connector_result.one_or_none()

                if agent_connector:
                    # Query to get builtin connectors for this agent
                    connectors_stmt = (
                        select(BuiltInConnector.name)
                        .join(
                            WorkspaceBuiltInConnector,
                            WorkspaceBuiltInConnector.connector_id
                            == BuiltInConnector.id,
                        )
                        .join(
                            AgentConnectorBuiltInConnector,
                            AgentConnectorBuiltInConnector.workspace_builtin_connector_id
                            == WorkspaceBuiltInConnector.id,
                        )
                        .where(
                            AgentConnectorBuiltInConnector.agent_connector_id
                            == agent_connector.id
                        )
                    )
                    connectors_result = await async_session.exec(connectors_stmt)
                    agent_builtin_connectors = list(connectors_result.all())

                    # Store in the dictionary
                    agent_title = agent.title.split(" ")[0]

                    # Get the agent mcp servers
                    agent_mcp_servers = agent_connector.mcp_servers

                    all_agent_builtin_connectors[agent_title] = {
                        "id": str(agent.id),
                        "type": agent.type,
                        "builtin_connectors": agent_builtin_connectors,
                        "agent_mcp_servers": agent_mcp_servers,
                    }

                    # Update the mcp servers in the agents config
                    if agent_title in agents_config.agents:
                        agents_config.agents[
                            agent_title
                        ].mcp_servers = agent_mcp_servers

        # List all agents mcp available servers
        all_agents_mcp_servers = []
        for agent_config in all_agent_builtin_connectors.values():
            all_agents_mcp_servers.extend(agent_config["agent_mcp_servers"])

        # Filter the mcp servers to only include the ones that are active and in the all_agents_mcp_servers list
        filtered_mcp_servers = []
        for mcp_server in mcp_servers:
            if mcp_server.is_active and mcp_server.name in all_agents_mcp_servers:
                filtered_mcp_servers.append(mcp_server)

        # Update the mcp servers in the mcp config schema
        agents_config.mcp_servers = filtered_mcp_servers

        # Update the tools config for the agents
        agents_config = await self.update_tools_config(
            agents_config, conversation.agent.workspace_id, all_agent_builtin_connectors
        )

        # Update the permissions config for the agents
        mcp_tools_permissions = []
        for mcp_server in agents_config.mcp_servers:
            mcp_tools_permissions.extend(mcp_server.tool_permissions)

        for agent_id, agent_config in agents_config.agents.items():
            agent_config.tool_config.tool_permissions.extend(mcp_tools_permissions)
            agents_config.agents[agent_id] = agent_config

        # Check active agents
        active_agents = self.session.exec(
            select(Agent).where(
                Agent.workspace_id == conversation.agent.workspace_id,
                Agent.type == "conversation_agent",
                Agent.is_active,
            )
        ).all()
        active_agents = [agent.title.split(" ")[0] for agent in active_agents]
        agents_config.active_agents = active_agents

        # Update the instructions for the agents
        for agent_id, agent_config in agents_config.agents.items():
            if agent_id in agent_instructions:
                agent_config.instructions = agent_instructions[agent_id]

        return graph, agents_config

    @contextlib.asynccontextmanager
    async def async_session(self):
        """
        Context manager that provides a Session with a proper async session.
        The session is automatically closed when the context is exited.
        """
        async with AsyncSession(async_engine) as async_session:
            try:
                yield async_session
            finally:
                await async_session.close()

    async def update_tools_config(
        self,
        agents_config: MultiAgentConfig,
        workspace_id: uuid.UUID,
        all_agent_builtin_connectors: dict[str, Any],
    ) -> MultiAgentConfig:
        """Update the tools config for the agents."""
        async with self.async_session() as async_session:
            workspace_builtin_connector_repository = (
                WorkspaceBuiltInConnectorRepository(async_session)
            )
            workspace_builtin_connectors = (
                await workspace_builtin_connector_repository.get_workspace_connectors(
                    workspace_id
                )
            )

            # Tools allowed user to configure
            workspace_tools = {tool.name: tool for tool in workspace_builtin_connectors}

        # Update the tools config for the agents
        for agent_id, agent_config in agents_config.agents.items():
            if agent_id not in all_agent_builtin_connectors:
                continue

            for tool_name, tool in workspace_tools.items():
                # Tools specify in config yaml
                agent_builtin_tools = agent_config.tool_config.builtin

                # Tools user config
                agent_config_tools = all_agent_builtin_connectors[agent_id][
                    "builtin_connectors"
                ]

                # If tool is not active or not in agent config remove it from the agent config
                if tool_name in agent_builtin_tools and (
                    not tool.is_active or tool.name not in agent_config_tools
                ):
                    agent_builtin_tools.remove(tool_name)

                # If tool is active and required permission is true, add it to the agent config
                if tool.required_permission:
                    agent_config.tool_config.tool_permissions.append(tool_name)

                agent_config.tool_config.builtin = agent_builtin_tools

            # Tool permission should be a set
            agent_config.tool_config.tool_permissions = list(
                set(agent_config.tool_config.tool_permissions)
            )

            # Update the agent config in the agents config
            agents_config.agents[agent_id] = agent_config

        return agents_config

    async def get_kb_config(
        self, workspace_id: uuid.UUID, user_id: uuid.UUID, message_content: str
    ):
        """Get the kb config for the conversation."""
        # Get all kb_ids
        async with self.async_session() as async_session:
            kb_repo = KBRepository(async_session)
            all_kbs = await kb_repo.get_kbs(
                workspace_id=workspace_id,
                user_id=user_id,
            )
            if all_kbs.failure:
                raise ValueError(all_kbs.error)
            all_kbs = all_kbs.value.data

            always_kbs = [kb for kb in all_kbs if kb.usage_mode == KBUsageMode.ALWAYS]

            if len(always_kbs) == 0:
                kb_prompt = "User has no always knowledge bases configured, please only call the search_knowledge_base tool if user mention a #kbs/kb-name in their prompt."
            else:
                kb_prompt = "User has always knowledge bases configured, please use the search_knowledge_base tool to get information from the knowledge bases if needed.\n"
                kb_prompt += "\n".join(
                    [
                        f"Title: {kb.title} - Description: {kb.description}"
                        for kb in always_kbs
                    ]
                )

            # Extract KB references from message content more efficiently
            kbs_mentioned = []
            slugified_names = parse_document_mention(message_content)
            if slugified_names:
                kbs_mentioned = [
                    kb.id for kb in all_kbs if slugify(kb.title) in slugified_names
                ]

            # Ensure we only pass KB IDs, not KB objects
            always_kb_ids = [kb.id for kb in always_kbs]
            available_kbs_for_search = always_kb_ids + kbs_mentioned

            return {
                "available_kbs_for_search": available_kbs_for_search,
                "kb_prompt": kb_prompt,
            }

    async def _prepare_attachment_content(
        self, attachment_records: list[MessageAttachment] | None = None
    ) -> list[dict] | None:
        if not attachment_records:
            return None

        # Get async attachment service and process attachments into multimodal content
        attachment_service = await self._get_attachment_service()
        attachment_content = await attachment_service.process_attachments_for_agent(attachment_records)

        return attachment_content

    async def _prepare_conversation_config(
        self,
        conversation: Conversation,
        checkpoint_id: uuid.UUID,
        graph_name: str = "autonomous_agent",
        user_id: uuid.UUID | None = None,
        resource_id: uuid.UUID | None = None,
        message_content: str | None = None,
        attachment_records: list[MessageAttachment] | None = None,
    ) -> ConversationConfigSchema:
        """Prepare configuration for the conversation and check for context resources.

        Args:
            conversation: Conversation object
            checkpoint_id: ID of the checkpoint
            graph_name: Name of the graph to use
            user_id: Optional user ID to use for the conversation

        Returns:
            ConversationConfigSchema
        """
        # Get the aws account
        aws_account = conversation.agent.workspace.aws_account
        if not aws_account or not aws_account.credential:
            raise ValueError(
                "MISSING CREDENTIALS! Please configure workspace credentials!"
            )

        # Get the aws credential
        aws_credential = aws_account.credential

        graph, agents_config = await self._prepare_agent_config(
            conversation, graph_name
        )

        # Get kb and memory config
        kb_config = await self.get_kb_config(
            conversation.agent.workspace_id, user_id, message_content
        )

        # Prepare attachment content
        attachment_content = await self._prepare_attachment_content(attachment_records)

        config = ConversationConfigSchema(
            user_id=user_id,
            checkpoint_id=checkpoint_id,
            thread_id=conversation.id,
            conversation_id=conversation.id,
            workspace_id=conversation.agent.workspace_id,
            resource_id=resource_id,
            workspace_config=WorkspaceConfigSchema(
                workspace_id=conversation.agent.workspace_id,
                aws_access_key_id=aws_credential.access_key_id,
                aws_secret_access_key=aws_credential.secret_access_key,
                aws_default_region=conversation.agent.workspace.settings.regions[-1],
                environment=aws_account.environment,
            ),
            agents_config=agents_config,
            available_kbs_for_search=kb_config["available_kbs_for_search"],
            kb_prompt=kb_config["kb_prompt"],
            attachment_content=attachment_content,
        )
        return graph, config

    async def _prepare_message(
        self,
        conversation_id: uuid.UUID,
        message_content: str,
        message_id: uuid.UUID | None = None,
        resume: bool = False,
        action_type=None,
        restore: bool = False,
        attachment_ids: list[uuid.UUID] | None = None,
    ) -> tuple[Message, bool, str | None]:
        """Prepare user message and handle restore/resume scenarios.

        Args:
            conversation_id: ID of the conversation
            message_content: Content of the user message
            message_id: Optional ID of a message to restore from
            resume: Whether to resume a previous interrupted conversation
            action_type: Action type for the message
            restore: Whether to restore from a specific message

        Returns:
            Tuple containing:
            - The user message
            - Whether title generation is needed
            - Checkpoint ID if applicable
        """
        from sqlmodel import select, update

        from app.models import Message

        checkpoint_id = None
        need_title_generation = False

        # Handle restore case
        if restore and message_id:
            # Get the message to restore from
            restore_message = self.session.get(Message, message_id)
            if not restore_message:
                raise ValueError("Message to restore from not found")

            # For user message, get checkpoint from nearest assistant message below it
            if restore_message.role != "assistant":
                nearest_assistant = self.session.exec(
                    select(Message)
                    .where(not Message.is_deleted)
                    .where(Message.conversation_id == conversation_id)
                    .where(Message.role == "assistant")
                    .where(Message.created_at > restore_message.created_at)
                    .order_by(Message.created_at.asc())
                    .limit(1)
                ).first()

                if nearest_assistant and nearest_assistant.checkpoint:
                    checkpoint_id = nearest_assistant.checkpoint.start_checkpoint_id

            # Soft delete messages after the restore point
            self.session.exec(
                update(Message)
                .where(not Message.is_deleted)
                .where(Message.conversation_id == conversation_id)
                .where(Message.created_at >= restore_message.created_at)
                .values(is_deleted=True)
            )
            self.session.commit()

        # Handle message creation based on resume flag
        if not resume:
            message_db = Message(
                conversation_id=conversation_id,
                content=message_content,
                role="user",
                action_type=action_type or MessageActionType.NONE,
            )
            self.session.add(message_db)
            self.session.commit()
            self.session.refresh(message_db)

            # Handle attachments if provided (link them to the message)
            attachment_records = None
            if attachment_ids:
                attachment_records = self._attach_files_to_message(message_db.id, attachment_ids)

            # Determine if we need to generate a title (only for the first user message)
            user_message_count = self.session.exec(
                select(func.count(Message.id))
                .where(not Message.is_deleted)
                .where(Message.conversation_id == conversation_id)
                .where(Message.role == "user")
            ).one()

            need_title_generation = user_message_count == 1
        else:
            # When resuming, get the last user message
            message_db = self.session.exec(
                select(Message)
                .where(not Message.is_deleted)
                .where(Message.conversation_id == conversation_id)
                .where(Message.role == "user")
                .order_by(Message.created_at.desc())
                .limit(1)
            ).first()

            if not message_db:
                raise ValueError("No previous message found to resume from")

            # Update action type for resumed message if provided
            if action_type:
                message_db.action_type = action_type
                self.session.add(message_db)
                self.session.commit()
                self.session.refresh(message_db)

        return message_db, need_title_generation, checkpoint_id, attachment_records

    def _attach_files_to_message(self, message_id: uuid.UUID, attachment_ids: list[uuid.UUID]) -> list[MessageAttachment]:
        """Attach files to a message by updating the message_id in MessageAttachment records.

        Args:
            message_id: ID of the message to attach files to
            attachment_ids: List of attachment IDs to attach to the message

        Returns:
            list[MessageAttachment]: List of updated attachment records
        """
        # Update the message_id for all specified attachments
        attachment_records = []
        for attachment_id in attachment_ids:
            self.session.exec(
                update(MessageAttachment)
                .where(MessageAttachment.id == attachment_id)
                .values(message_id=message_id)
            )

        self.session.commit()

        # Get the updated records
        attachment_records = self.session.exec(
            select(MessageAttachment)
            .where(MessageAttachment.id.in_(attachment_ids))
        ).all()

        return attachment_records
