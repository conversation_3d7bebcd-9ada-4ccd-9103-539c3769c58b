import json
import logging
import uuid
from datetime import datetime
from typing import Any

from sqlmodel import Session, select

from app.models import Message, MessageActionType
from app.services.notification_service import NotificationService

logger = logging.getLogger(__name__)


class MessageHandler:
    """Handler for message-related operations."""

    def __init__(self, session: Session):
        """Initialize the handler.

        Args:
            session: SQLModel session for database operations
        """
        self.session = session
        self.notification_service = NotificationService(session)

    def create_message(
        self, conversation_id: uuid.UUID, content: str, role: str
    ) -> Message:
        """Create and persist a new message."""
        message = Message(conversation_id=conversation_id, content=content, role=role)
        self.session.add(message)
        self.session.commit()
        self.session.refresh(message)
        return message

    def get_last_interrupted_message(
        self, conversation_id: uuid.UUID
    ) -> Message | None:
        """Get the last interrupted assistant message."""
        return self.session.execute(
            select(Message)
            .where(Message.is_deleted == False)  # noqa: E712
            .where(Message.conversation_id == conversation_id)
            .where(Message.role != "user")
            .where(Message.is_interrupt.is_(True))
            .order_by(Message.created_at.desc())
            .limit(1)
        ).scalar_one_or_none()

    def handle_interrupt(
        self,
        response_message: Message,
        interrupt_message: str,
        accumulated_content: str = "",
    ):
        """Handle interrupted message state and save accumulated content.

        Args:
            response_message: The message to update
            interrupt_message: The interrupt message to store
            accumulated_content: Any accumulated message content before the interrupt
        """
        if accumulated_content and accumulated_content.strip():
            response_message.content = accumulated_content
        response_message.is_interrupt = True
        response_message.interrupt_message = interrupt_message
        self.session.add(response_message)
        self.session.commit()

        # Create a notification for the interrupt message
        conversation = response_message.conversation
        if conversation and conversation.agent and conversation.agent.workspace:
            # Create action URL with agent ID and autonomous flag
            is_autonomous = conversation.agent.type == "autonomous_agent"
            action_url = f"/agents/{conversation.agent.id}?autonomous={str(is_autonomous).lower()}&conversation={conversation.id}"

            # Create notification for workspace owner using background task
            notification_result = (
                self.notification_service.create_interrupt_notification(
                    user_id=conversation.agent.workspace.owner_id,
                    interrupt_message=interrupt_message,
                    conversation_id=conversation.id,
                    requires_action=True,
                    action_url=action_url,
                    message_id=response_message.id,
                )
            )

            logger.info(
                f"Scheduled interrupt notification task: {notification_result['task_id']}"
            )

    def save_final_response(
        self, response_message: Message, content: str, role: str | None = None
    ):
        """Save the final message response."""
        if role:
            response_message.role = role
        response_message.content += content
        response_message.updated_at = datetime.now()
        self.session.add(response_message)
        self.session.commit()

    def cleanup_on_error(
        self, response_message: Message | None, user_message: Message | None
    ):
        """Clean up database entries on error."""
        from app.models import MessageAgentThought, MessageCheckpoint

        if response_message and response_message.id:
            # Delete associated thoughts
            thoughts = (
                self.session.execute(
                    select(MessageAgentThought).where(
                        MessageAgentThought.message_id == response_message.id
                    )
                )
                .scalars()
                .all()
            )

            # Delete associated checkpoint
            checkpoint = self.session.execute(
                select(MessageCheckpoint).where(
                    MessageCheckpoint.message_id == response_message.id
                )
            ).scalar_one_or_none()
            if checkpoint:
                self.session.delete(checkpoint)

            for thought in thoughts:
                self.session.delete(thought)
            self.session.commit()

            # Delete message
            self.session.delete(response_message)
            self.session.commit()

        if user_message:
            self.session.delete(user_message)
            self.session.commit()

    def get_conversation_messages(self, conversation_id: uuid.UUID) -> list[Message]:
        """Retrieve messages for a conversation."""
        return (
            self.session.execute(
                select(Message)
                .where(Message.conversation_id == conversation_id)
                .order_by(Message.created_at.asc())
            )
            .scalars()
            .all()
        )

    def format_message(self, message: Message) -> dict:
        """Format a message for the API response.
        If message content is empty, concatenate all agent thought content.
        """
        content = message.content
        if not content and message.role == "assistant" and message.thoughts:
            # Concatenate all agent thought content
            content = ""
            for thought in sorted(message.thoughts, key=lambda x: x.position):
                if thought.answer:
                    content += thought.answer + "\n"

        # Format display components
        display_components = []
        if message.display_components:
            for component in sorted(
                message.display_components, key=lambda x: x.position
            ):
                display_components.append(
                    {
                        "id": str(component.id),
                        "type": component.type.value,
                        "chart_type": component.chart_type.value
                        if component.chart_type
                        else None,
                        "title": component.title,
                        "description": component.description,
                        "data": component.data,
                        "config": component.config,
                        "position": component.position,
                        "created_at": int(component.created_at.timestamp()),
                    }
                )

        return {
            "id": str(message.id),
            "conversation_id": str(message.conversation_id),
            "content": content,
            "role": message.role,
            "created_at": int(message.created_at.timestamp()),
            "is_interrupt": message.is_interrupt,
            "interrupt_message": message.interrupt_message,
            "action_type": message.action_type.value
            if message.action_type
            else MessageActionType.NONE.value,
            "display_components": display_components,
            "agent_thoughts": [],
        }

    def get_conversation_history(
        self, conversation_id: uuid.UUID, limit: int = 20
    ) -> dict[str, Any]:
        """Get conversation history with messages and their agent thoughts."""
        from app.models import MessageAgentThought, MessageAttachment

        result = self.session.execute(
            select(Message, MessageAgentThought, MessageAttachment)
            .outerjoin(MessageAgentThought)
            .outerjoin(MessageAttachment)
            .where(Message.conversation_id == conversation_id)
            .where(Message.is_deleted == False)  # noqa: E712
            .order_by(Message.created_at.desc())
            .limit(limit + 1)
        ).all()

        messages = {}
        count = 0
        has_more = False

        for message, thought, attachment in reversed(result):
            if count >= limit:
                has_more = True
                break

            if message.id not in messages:
                messages[message.id] = self.format_message(message)
                count += 1

            if thought:
                messages[message.id]["agent_thoughts"].append(
                    self._format_thought(thought, message.id)
                )

            if attachment:
                if "attachments" not in messages[message.id]:
                    messages[message.id]["attachments"] = []
                messages[message.id]["attachments"].append(
                    self._format_attachment(attachment)
                )

        # Sort thoughts by position
        for message in messages.values():
            message["agent_thoughts"].sort(key=lambda x: x["position"])

        return {"limit": limit, "has_more": has_more, "data": list(messages.values())}

    def _format_thought(self, thought: Any, message_id: uuid.UUID) -> dict:
        """Format a thought for the API response."""
        try:
            observation = json.loads(thought.observation)
        except Exception as e:
            logger.error(f"Error formatting thought: {e}")
            observation = thought.observation

        return {
            "id": str(thought.id),
            "message_id": str(message_id),
            "position": thought.position,
            "thought": thought.answer or "",
            "tool": thought.tool or "",
            "tool_input": thought.tool_input or {},
            "observation": observation,
        }

    def _format_attachment(self, attachment: Any) -> dict:
        """Format an attachment for the API response."""
        return {
            "id": str(attachment.id),
            "filename": attachment.filename,
            "original_filename": attachment.original_filename,
            "file_type": attachment.file_type,
            "file_size": attachment.file_size,
            "storage_key": attachment.storage_key,
            "created_at": attachment.created_at.isoformat() if attachment.created_at else None,
        }
