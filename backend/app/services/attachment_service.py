from uuid import UUID, uuid4
from typing import Optional, <PERSON><PERSON>
from datetime import timedelta
import base64
import tempfile
import io

from app.models import MessageAttachment
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.config import settings
from app.logger import logger
from app.repositories.attachment import AttachmentRepository
from app.repositories.object_storage.base import BaseStorageRepository
from app.repositories.object_storage.provider import get_object_storage_repository
from app.schemas.message_attachment import (
    AttachmentFileInfo,
    AttachmentPresignedUrlResponse,
    PresignedUrlInfo,
    UploadedAttachmentInfo,
)
from app.services.security.attachment_validator import validate_attachment_metadata


class AttachmentService:
    def __init__(self, session: AsyncSession):
        self.session = session
        self.attachment_repo = AttachmentRepository(session)
        self.os_repo: BaseStorageRepository = get_object_storage_repository()

    async def generate_presigned_urls(
        self, files: list[AttachmentFileInfo], user_id: UUID, workspace_id: UUID
    ) -> <PERSON><PERSON>[Optional[AttachmentPresignedUrlResponse], Optional[str]]:
        """
        Generate presigned URLs for clients to upload files directly to S3.
        Returns (response, error) where one is None and the other has a value.
        """
        try:
            presigned_urls = []
            for file_info in files:
                # Validate file metadata
                is_valid, error = validate_attachment_metadata(file_info)
                if not is_valid:
                    return None, error

                # Generate a unique storage key
                object_name = f"{workspace_id}/{user_id}/{uuid4()}_{file_info.filename}"

                # Generate presigned URL
                presigned_url = await self.os_repo.get_presigned_put_url(
                    object_name=object_name,
                    bucket_name=settings.ATTACHMENT_BUCKET,
                    expires=3600,  # 1 hour
                    content_type=file_info.content_type,
                )

                presigned_urls.append(
                    PresignedUrlInfo(
                        file_id=file_info.file_id,
                        filename=file_info.filename,
                        storage_key=object_name,
                        presigned_url=presigned_url,
                    )
                )

            return AttachmentPresignedUrlResponse(presigned_urls=presigned_urls), None
        except Exception as e:
            logger.error(f"Error generating presigned URLs: {e}")
            return None, "An unexpected error occurred while generating presigned URLs."

    async def verify_uploads(self, uploaded_files: list[UploadedAttachmentInfo]) -> list[UploadedAttachmentInfo]:
        """
        Verify that files have been uploaded to the object storage.
        """
        verified_files = []
        for file_info in uploaded_files:
            if await self.os_repo.file_exists(file_info.storage_key, settings.ATTACHMENT_BUCKET):
                verified_files.append(file_info)
            else:
                logger.warning(f"File not found in storage after upload confirmation: {file_info.storage_key}")
        return verified_files

    async def get_download_url(self, attachment_id: UUID, user_id: UUID) -> Tuple[Optional[str], Optional[str]]:
        """
        Generate a presigned GET URL for a client to download an attachment.
        Returns (download_url, error) where one is None and the other has a value.
        """
        try:
            attachment = await self.attachment_repo.get_attachment_by_id(attachment_id)
            if not attachment:
                return None, "Attachment not found."

            # Basic permission check (can be expanded)
            if attachment.owner_id != user_id:
                return None, "You do not have permission to access this file."

            download_url = await self.os_repo.get_presigned_url(
                object_name=attachment.storage_key,
                bucket_name=settings.ATTACHMENT_BUCKET,
                expires=timedelta(seconds=3600),  # 1 hour
            )
            return download_url, None
        except Exception as e:
            logger.error(f"Error generating download URL for attachment {attachment_id}: {e}")
            return None, "An unexpected error occurred while generating the download URL."

    async def process_attachments_for_agent(self, attachment_records: list[MessageAttachment]) -> list[dict]:
        """Process attachments into LangGraph-compatible multimodal content.

        Args:
            attachment_ids: List of attachment IDs to process

        Returns:
            List of content dictionaries compatible with LangGraph multimodal messages
        """
        processed_content = []

        for attachment_record in attachment_records:
            try:
                file_category = self._get_file_category(attachment_record.file_type)

                if file_category == 'image':
                    # Keep images as base64 for vision models
                    content_dict = await self._process_image_attachment(attachment_record)
                    if content_dict:
                        processed_content.append(content_dict)

                elif file_category == 'pdf':
                    # Convert PDF to images using pdf2image
                    content_dicts = await self._process_pdf_attachment(attachment_record)
                    processed_content.extend(content_dicts)

                else:
                    # Use LlamaIndex for other file types
                    content_dict = await self._process_document_attachment(attachment_record)
                    if content_dict:
                        processed_content.append(content_dict)

            except Exception as e:
                logger.error(f"Error processing attachment {attachment_record.id}: {e}")
                continue

        return processed_content

    def _get_file_category(self, content_type: str) -> str:
        """Categorize file type for processing."""
        if content_type.startswith('image/'):
            return 'image'
        elif content_type == 'application/pdf':
            return 'pdf'
        else:
            return 'document'

    async def _download_attachment_content(self, attachment) -> bytes:
        """Download attachment content from storage."""
        try:
            file_data, _, _, _ = await self.os_repo.get_file(
                object_name=attachment.storage_key,
                bucket_name=settings.ATTACHMENT_BUCKET
            )
            return file_data.getvalue()
        except Exception as e:
            logger.error(f"Error downloading attachment {attachment.id}: {e}")
            raise

    async def _process_image_attachment(self, attachment) -> Optional[dict]:
        """Process image attachment into multimodal content."""
        try:
            file_data = await self._download_attachment_content(attachment)
            base64_data = base64.b64encode(file_data).decode('utf-8')

            return {
                "type": "image",
                "source_type": "base64",
                "data": base64_data,
                "mime_type": attachment.file_type,
                "metadata": {
                    "filename": attachment.filename,
                    "attachment_id": str(attachment.id)
                }
            }
        except Exception as e:
            logger.error(f"Error processing image attachment {attachment.id}: {e}")
            return None

    async def _process_pdf_attachment(self, attachment) -> list[dict]:
        """Process PDF attachment by converting pages to images."""
        try:
            file_data = await self._download_attachment_content(attachment)
            images = await self._pdf_to_images(file_data)

            content_dicts = []
            for i, img_data in enumerate(images):
                content_dicts.append({
                    "type": "image",
                    "source_type": "base64",
                    "data": img_data,
                    "mime_type": "image/png",
                    "metadata": {
                        "filename": attachment.filename,
                        "attachment_id": str(attachment.id),
                        "source": f"{attachment.filename}_page_{i+1}",
                        "page_number": i + 1
                    }
                })

            return content_dicts
        except Exception as e:
            logger.error(f"Error processing PDF attachment {attachment.id}: {e}")
            return []

    async def _pdf_to_images(self, pdf_data: bytes) -> list[str]:
        """Convert PDF pages to base64 images using pdf2image."""
        try:
            from pdf2image import convert_from_bytes

            images = convert_from_bytes(pdf_data)
            base64_images = []

            for img in images:
                buffer = io.BytesIO()
                img.save(buffer, format='PNG')
                img_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
                base64_images.append(img_data)

            return base64_images
        except ImportError:
            logger.error("pdf2image not installed. Install with: pip install pdf2image")
            raise
        except Exception as e:
            logger.error(f"Error converting PDF to images: {e}")
            raise

    async def _process_document_attachment(self, attachment) -> Optional[dict]:
        """Process document attachment using LlamaIndex for text extraction."""
        try:
            text_content = await self._extract_text_with_llamaindex(attachment)

            return {
                "type": "text",
                "text": f"File: {attachment.filename}\nContent:\n{text_content}"
            }
        except Exception as e:
            logger.error(f"Error processing document attachment {attachment.id}: {e}")
            return None

    async def _extract_text_with_llamaindex(self, attachment) -> str:
        """Extract text using LlamaIndex pattern like file_reader.py."""
        try:
            from llama_index.core import SimpleDirectoryReader

            # Download file content
            file_data = await self._download_attachment_content(attachment)

            # Save temporarily and process
            file_extension = attachment.filename.split('.')[-1] if '.' in attachment.filename else 'txt'
            with tempfile.NamedTemporaryFile(suffix=f".{file_extension}", delete=False) as tmp_file:
                tmp_file.write(file_data)
                tmp_file.flush()

                try:
                    reader = SimpleDirectoryReader(input_files=[tmp_file.name])
                    documents = reader.load_data()

                    return "\n".join([doc.text for doc in documents])
                finally:
                    # Clean up temporary file
                    import os
                    try:
                        os.unlink(tmp_file.name)
                    except:
                        pass

        except ImportError:
            logger.error("LlamaIndex not installed. Install with: pip install llama-index")
            # Fallback to basic text extraction
            return f"Document: {attachment.filename} (text extraction not available)"
        except Exception as e:
            logger.error(f"Error extracting text from {attachment.filename}: {e}")
            return f"Document: {attachment.filename} (error extracting text)"
