from datetime import datetime
from uuid import UUID

from sqlmodel.ext.asyncio.session import AsyncSession

from app.logger import logger
from app.models import (
    MCPServerBase,
    MCPServerStatus,
    MCPServerTransport,
)
from app.modules.connectors.mcp_client.client import MCPClientConnector
from app.repositories.mcp_server import MCPServerRepository
from app.schemas.mcp_server import (
    MCPServerConfigValidationSchema,
    MCPServerConnectionResult,
    MCPServerCreateSchema,
    MCPServerListResponseSchema,
    MCPServerResponseSchema,
    MCPServerUpdateSchema,
)


class MCPServerService:
    def __init__(self, async_session: AsyncSession):
        self.mcp_server_repository = MCPServerRepository(async_session)

    async def connect_mcp_server(
        self, mcp_server: MCPServerBase
    ) -> MCPServerConnectionResult:
        """
        Connect to an MCP server.
        """
        mcp_client = MCPClientConnector(mcp_servers=[mcp_server])
        try:
            tools = await mcp_client.list_tools(mcp_server.name, filter_enabled=False)
        except Exception as e:
            logger.error(f"Error connecting to MCP server {mcp_server.name}: {str(e)}")
            tools = []

        connection_status = mcp_client.get_connection_status()
        return MCPServerConnectionResult(
            status=connection_status[mcp_server.name]["status"],
            status_message=connection_status[mcp_server.name]["status_message"],
            tool_list=[tool.name for tool in tools],
        )

    def validate_mcp_server_config(self, config: dict) -> bool:
        """
        Validate MCP server configuration format.

        Args:
            config: Configuration dictionary to validate

        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            MCPServerConfigValidationSchema(**config)
        except Exception as e:
            logger.error(f"Invalid MCP server configuration: {config} - {str(e)}")
            return False
        return True

    async def _test_server_connection(
        self,
        server_id: UUID,
        workspace_id: UUID,
        updated_config: dict = None,
        updated_name: str = None,
        updated_prefix: str = None,
        updated_type: MCPServerTransport = None,
        updated_tool_permissions: list[str] = None,
        updated_tool_enabled: list[str] = None,
    ) -> tuple[MCPServerStatus | None, str | None, list[str] | None, datetime | None]:
        """
        Test connection to MCP server and return connection info.

        Returns:
            Tuple of (status, status_message, tool_list, status_updated_at)
        """
        try:
            # Get current server info for connection test
            current_server = await self.mcp_server_repository.get_mcp_server(
                server_id=server_id, workspace_id=workspace_id
            )
            if not current_server:
                return None, None, None, None

            # Create test server with updated config for testing
            test_server = MCPServerBase(
                name=updated_name or current_server.name,
                prefix=updated_prefix or current_server.prefix,
                type=updated_type or current_server.type,
                config=updated_config or current_server.config,
                is_active=True,
                is_builtin=current_server.is_builtin,
                tool_permissions=updated_tool_permissions
                or current_server.tool_permissions,
                tool_enabled=updated_tool_enabled or current_server.tool_enabled,
            )

            connection_result = await self.connect_mcp_server(test_server)
            return (
                connection_result.status,
                connection_result.status_message,
                connection_result.tool_list,
                datetime.now(),
            )
        except Exception as e:
            # If connection fails, return error info
            return (
                MCPServerStatus.ERROR,
                f"Connection failed: {str(e)}",
                [],
                datetime.now(),
            )

    async def create_mcp_server(
        self,
        workspace_id: UUID,
        data: MCPServerCreateSchema,
    ) -> MCPServerResponseSchema:
        """
        Create a new MCP server for a workspace.

        Args:
            workspace_id: Workspace UUID
            data: MCP server creation data

        Returns:
            Created MCP server response
        """
        if data.config and not self.validate_mcp_server_config(data.config):
            raise ValueError("Invalid MCP server configuration")

        mcp_server = MCPServerBase(
            name=data.name,
            prefix=data.prefix,
            type=data.type,
            config=data.config,
            is_active=data.is_active,
            is_builtin=data.is_builtin,
            tool_permissions=data.tool_permissions,
            tool_enabled=data.tool_enabled,
        )

        if data.is_active:
            connection_result = await self.connect_mcp_server(mcp_server)
            if connection_result.status == MCPServerStatus.CONNECTED:
                mcp_server.status = MCPServerStatus.CONNECTED
                mcp_server.status_message = connection_result.status_message
                mcp_server.tool_list = connection_result.tool_list
                mcp_server.status_updated_at = datetime.now()
            else:
                mcp_server.status = MCPServerStatus.ERROR
                mcp_server.status_message = connection_result.status_message
                mcp_server.status_updated_at = datetime.now()

        created_server = await self.mcp_server_repository.create_mcp_server(
            workspace_id=workspace_id, mcp_server_data=mcp_server
        )

        return MCPServerResponseSchema.model_validate(created_server)

    async def get_mcp_servers(self, workspace_id: UUID) -> MCPServerListResponseSchema:
        """
        Get MCP servers for a specific workspace.

        Args:
            workspace_id: Workspace UUID

        Returns:
            MCP servers list response
        """
        servers = await self.mcp_server_repository.get_mcp_servers(
            workspace_id=workspace_id
        )
        if servers is None:
            return MCPServerListResponseSchema(data=[], count=0)

        server_responses = [
            MCPServerResponseSchema.model_validate(server) for server in servers
        ]
        return MCPServerListResponseSchema(
            data=server_responses, count=len(server_responses)
        )

    async def get_mcp_server(
        self, workspace_id: UUID, server_id: UUID
    ) -> MCPServerResponseSchema | None:
        """
        Get an MCP server by ID.

        Args:
            workspace_id: Workspace UUID
            server_id: Server UUID

        Returns:
            MCP server response if found, None otherwise
        """
        server = await self.mcp_server_repository.get_mcp_server(
            workspace_id=workspace_id, server_id=server_id
        )
        if server is None:
            return None
        return MCPServerResponseSchema.model_validate(server)

    async def update_mcp_server(
        self,
        workspace_id: UUID,
        server_id: UUID,
        data: MCPServerUpdateSchema,
    ) -> MCPServerResponseSchema | None:
        """
        Update an existing MCP server.

        Args:
            workspace_id: Workspace UUID
            server_id: Server UUID
            data: Update data

        Returns:
            Updated MCP server response if successful, None otherwise
        """
        if data.config and not self.validate_mcp_server_config(data.config):
            raise ValueError("Invalid MCP server configuration")

        # If server is being activated, test connection first
        connection_status = None
        connection_status_message = None
        connection_tool_list = None
        connection_status_updated_at = None

        if data.is_active is True:
            (
                connection_status,
                connection_status_message,
                connection_tool_list,
                connection_status_updated_at,
            ) = await self._test_server_connection(
                server_id=server_id,
                workspace_id=workspace_id,
                updated_config=data.config,
                updated_name=data.name,
                updated_prefix=data.prefix,
                updated_type=data.type,
                updated_tool_permissions=data.tool_permissions,
                updated_tool_enabled=data.tool_enabled,
            )

        # Single database operation to update all fields including connection info
        updated_server = await self.mcp_server_repository.update_mcp_server(
            server_id=server_id,
            workspace_id=workspace_id,
            name=data.name,
            prefix=data.prefix,
            type=data.type,
            config=data.config,
            is_active=data.is_active,
            tool_permissions=data.tool_permissions,
            tool_enabled=data.tool_enabled,
            status=connection_status,
            status_message=connection_status_message,
            tool_list=connection_tool_list,
            status_updated_at=connection_status_updated_at,
        )

        if updated_server is None:
            return None

        return MCPServerResponseSchema.model_validate(updated_server)

    async def delete_mcp_server(self, workspace_id: UUID, server_id: UUID) -> bool:
        """
        Delete an MCP configuration.

        Args:
            workspace_id: Workspace UUID
            server_id: Server UUID

        Returns:
            True if deleted, False if not found
        """
        return await self.mcp_server_repository.delete_mcp_server(
            server_id=server_id, workspace_id=workspace_id
        )

    async def refresh_mcp_server(
        self, workspace_id: UUID, server_id: UUID
    ) -> MCPServerResponseSchema | None:
        """
        Refresh an MCP server.
        """
        mcp_server = await self.mcp_server_repository.get_mcp_server(
            server_id=server_id, workspace_id=workspace_id
        )
        if mcp_server is None:
            return None

        (
            connection_status,
            connection_status_message,
            connection_tool_list,
            connection_status_updated_at,
        ) = await self._test_server_connection(
            server_id=server_id,
            workspace_id=workspace_id,
            updated_config=mcp_server.config,
            updated_name=mcp_server.name,
            updated_prefix=mcp_server.prefix,
            updated_type=mcp_server.type,
        )

        # Single database operation to update all fields including connection info
        updated_server = await self.mcp_server_repository.update_mcp_server(
            server_id=server_id,
            workspace_id=workspace_id,
            name=mcp_server.name,
            prefix=mcp_server.prefix,
            type=mcp_server.type,
            config=mcp_server.config,
            is_active=mcp_server.is_active,
            tool_permissions=mcp_server.tool_permissions,
            tool_enabled=mcp_server.tool_enabled,
            status=connection_status,
            status_message=connection_status_message,
            tool_list=connection_tool_list,
            status_updated_at=connection_status_updated_at,
        )

        if updated_server is None:
            return None

        return MCPServerResponseSchema.model_validate(updated_server)
