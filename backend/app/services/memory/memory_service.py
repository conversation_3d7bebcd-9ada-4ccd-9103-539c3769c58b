import json
import uuid
from typing import Any

from langchain_core.language_models import BaseChatModel
from langchain_core.messages import (
    AnyMessage,
    HumanMessage,
    SystemMessage,
    convert_to_openai_messages,
)
from llama_index.core import Document, VectorStoreIndex
from qdrant_client import models as qdrant_models
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.config import settings
from app.core.langfuse import langfuse_handler
from app.logger import logger
from app.modules.multi_agents import AgentFactory
from app.modules.multi_agents.core.utils import load_chat_model
from app.repositories.conversation import ConversationRepository
from app.services.base_vector_store import BaseVectorStore
from app.services.memory.prompt import EXTRACTION_DECISION_PROMPT, EXTRACTION_PROMPT
from app.services.memory.schema import (
    ExtractionDecision,
    MemoryEvolution,
    MemoryNodeOutput,
)


class MemoryService(BaseVectorStore):
    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, session: AsyncSession | None = None):
        super().__init__()
        self.session = session
        self.conversation_repo = ConversationRepository(async_session=self.session)

    async def _should_extract_learnings(self, user_message: HumanMessage) -> bool:
        # Determine if conversation is worth extracting learnings from
        try:
            model = load_chat_model(settings.MEMORY_EXTRACTION_DECISION_MODEL)
            prompt = [
                SystemMessage(content=EXTRACTION_DECISION_PROMPT),
                HumanMessage(content=user_message.content),
            ]
            response = await model.with_structured_output(ExtractionDecision).ainvoke(
                prompt,
                config={
                    "run_name": "Extraction Decision",
                    "callbacks": [langfuse_handler],
                },
            )
            should_extract = response.should_extract
            logger.debug(f"Extraction decision: {should_extract}")
            return should_extract
        except Exception as e:
            logger.error(f"Error in extraction decision: {str(e)}")
            return False  # Default to not extracting on error

    async def _get_messages_from_checkpoint(
        self, thread_id: str
    ) -> tuple[list[dict], HumanMessage | None]:
        try:
            # Initialize AgentFactory if needed
            if AgentFactory._checkpointer is None:
                logger.debug("AgentFactory not initialized. Initializing now...")
                await AgentFactory.initialize()

            # Get latest assistant message
            latest_assistant_message = (
                await self.conversation_repo.get_last_assistant_message(
                    uuid.UUID(thread_id)
                )
            )

            checkpoint_id = None
            if latest_assistant_message:
                checkpoint = await self.conversation_repo.get_checkpoint_by_message_id(
                    latest_assistant_message.id
                )

                if checkpoint and checkpoint.end_checkpoint_id:
                    checkpoint_id = checkpoint.end_checkpoint_id

            # Configure checkpoint retrieval
            config = {
                "configurable": {
                    "thread_id": thread_id,
                    "checkpoint_id": checkpoint_id,
                }
            }
            checkpoint = await AgentFactory._checkpointer.aget(config)

            # Get agent states from checkpoint
            all_agent_states = checkpoint["channel_values"]["instance_states"]

            # Group messages by agent role
            messages_by_role = {}
            for role, agent_state in all_agent_states.items():
                if hasattr(agent_state, "messages"):
                    messages = agent_state.messages
                    if messages:
                        messages_by_role[role] = messages

            # Get latest user message
            latest_user_message = await self.conversation_repo.get_latest_user_message(
                uuid.UUID(thread_id)
            )

            return messages_by_role, latest_user_message

        except Exception as e:
            logger.error(f"Error in checkpoint retrieval: {e}")
            return [], None

    async def extract_key_learnings_by_role(
        self, conversation_id: uuid.UUID
    ) -> dict[str, Any]:
        """
        Extract key learnings from conversation for each agent role
        """
        try:
            # Get conversation workspace info
            conversation = await self.conversation_repo.async_get_conversation(
                conversation_id
            )
            workspace_id = conversation.agent.workspace_id

            # Ensure collection exists
            collection_name = settings.QDRANT_COLLECTION_NAME
            await self.ensure_collection_exists(collection_name)

            # Get messages from checkpoint
            (
                messages_by_role,
                latest_user_message,
            ) = await self._get_messages_from_checkpoint(str(conversation_id))

            if not messages_by_role:
                logger.error(
                    f"No messages by role found for conversation {conversation_id}"
                )
                return {"success": False, "error": "No messages found"}

            # Decide if conversation is worth extracting from
            should_extract = False
            if latest_user_message is not None:
                should_extract = await self._should_extract_learnings(
                    latest_user_message
                )
            else:
                # Check if any role has substantive content
                for role, messages in messages_by_role.items():
                    if len(messages) > 3:  # Threshold for sufficient message volume
                        should_extract = True
                        logger.debug(
                            f"Found {len(messages)} messages for {role}, proceeding with extraction"
                        )
                        break

            if not should_extract:
                logger.debug("Skipping extraction - not substantive content")
                return {
                    "success": True,
                    "skipped": True,
                    "reason": "Conversation lacks substantive content",
                }

            # Process each agent role
            for role, messages in messages_by_role.items():
                logger.info(f"Processing role: {role} with {len(messages)} messages")

                if (
                    not messages or len(messages) < 2
                ):  # Skip roles with too few messages
                    logger.debug(f"Skipping role {role} - insufficient content")
                    continue

                vector_store = await self.get_vector_store()

                model = load_chat_model(settings.MEMORY_EXTRACTION_MODEL)

                # Step 1: Extract memory node
                current_memory_node = await self._extract_memory_node(model, messages)
                reasoning = current_memory_node.reasoning
                current_memory_node = current_memory_node.memory_node
                logger.info(
                    f"Reasoning: {reasoning}\n"
                    f"Extracted memory node: {current_memory_node.model_dump_json(indent=2)}"
                )

                # Step 2: Prepare for vector store
                current_memory_node_json = current_memory_node.model_dump_json()
                current_vector_store_node = Document(
                    text=current_memory_node_json,
                    metadata={"role": role, "workspace_id": workspace_id},
                )
                current_vector_store_node.embedding = (
                    await self.generate_embedding_with_retry(current_memory_node_json)
                )

                # Step 3: Find related memory nodes
                index = VectorStoreIndex.from_vector_store(
                    vector_store=vector_store,
                    embed_model=self.embed_model,
                )
                query_engine = index.as_retriever(
                    similarity_top_k=settings.MEMORY_MAX_RELATED_MEMORY_NODES,
                    similarity_cutoff=settings.MEMORY_RELATEDNESS_THRESHOLD,
                )
                related_memory_nodes = await query_engine.aretrieve(
                    current_memory_node_json
                )
                logger.info(f"Found {len(related_memory_nodes)} related memory nodes")

                # Step 4: Analyze memory evolution
                nodes_to_add = []
                nodes_to_delete = []

                new_uuid = str(uuid.uuid4())

                if related_memory_nodes and len(related_memory_nodes) > 0:
                    neighbor_count = len(related_memory_nodes)
                    evolution_inputs = [
                        SystemMessage(
                            content=f"""You are analyzing if a memory node should evolve based on its relationship with {neighbor_count} related memory nodes."""
                        ),
                        HumanMessage(
                            content=f"""Current memory node:
                            id: {new_uuid}
                            {current_memory_node_json}\n\nRelated memory nodes: {[f"{node.node_id} - {node.text}" for node in related_memory_nodes]}"""
                        ),
                    ]
                    model_with_structured_output = model.with_structured_output(
                        MemoryEvolution
                    )
                    memory_evolution = await model_with_structured_output.ainvoke(
                        evolution_inputs,
                        config={
                            "run_name": "Memory Evolution",
                            "callbacks": [langfuse_handler],
                        },
                    )

                    logger.info(
                        f"Evolution decision: should_evolve={memory_evolution.should_evolve}, type={memory_evolution.evolution_type}"
                    )

                    if memory_evolution.should_evolve:
                        if memory_evolution.evolution_type == "combine":
                            if memory_evolution.combined_node:
                                combined_node_json = memory_evolution.combined_node.new_node.model_dump_json()
                                combined_vector_store_node = Document(
                                    text=combined_node_json,
                                    metadata={
                                        "role": role,
                                        "workspace_id": workspace_id,
                                    },
                                )
                                combined_vector_store_node.embedding = (
                                    await self.generate_embedding_with_retry(
                                        combined_node_json
                                    )
                                )
                                nodes_to_add.append(combined_vector_store_node)
                                nodes_to_delete.extend(
                                    memory_evolution.combined_node.combined_from
                                )
                                logger.info(
                                    f"Combined {len(memory_evolution.combined_node.combined_from)} nodes into one"
                                )

                        elif memory_evolution.evolution_type == "connect":
                            if memory_evolution.connections:
                                current_memory_node.links = memory_evolution.connections
                                updated_node_json = (
                                    current_memory_node.model_dump_json()
                                )
                                current_vector_store_node = Document(
                                    doc_id=new_uuid,
                                    text=updated_node_json,
                                    metadata={
                                        "role": role,
                                        "workspace_id": workspace_id,
                                        "links": memory_evolution.connections,
                                    },
                                )
                                current_vector_store_node.embedding = (
                                    await self.generate_embedding_with_retry(
                                        updated_node_json
                                    )
                                )
                                nodes_to_add.append(current_vector_store_node)

                        else:  # standalone
                            nodes_to_add.append(current_vector_store_node)
                    else:
                        nodes_to_add.append(current_vector_store_node)
                else:
                    nodes_to_add.append(current_vector_store_node)

                # Step 5: Update vector store
                logger.info(
                    f"Updating vector store: adding {len(nodes_to_add)} nodes, deleting {len(nodes_to_delete)} nodes"
                )
                await vector_store.async_add(nodes_to_add)
                await vector_store.adelete_nodes(node_ids=nodes_to_delete)

            return {"success": True}

        except Exception as e:
            logger.error(f"Error extracting key learnings: {str(e)}")
            logger.exception("Extraction error details:")
            return {"success": False, "error": str(e)}

    async def _extract_memory_node(
        self, model: BaseChatModel, messages: list[AnyMessage]
    ) -> MemoryNodeOutput:
        try:
            # Parse messages into a string
            oai_messages = convert_to_openai_messages(messages)
            messages_str = "\n".join(
                [f"{msg['role']}: {msg['content']}" for msg in oai_messages]
            )

            extraction_messages = [
                {"role": "system", "content": EXTRACTION_PROMPT},
                {"role": "user", "content": messages_str},
            ]

            model_with_structured_output = model.with_structured_output(
                MemoryNodeOutput
            )
            memory_node = await model_with_structured_output.ainvoke(
                extraction_messages,
                config={
                    "run_name": "Memory Node Extraction",
                    "callbacks": [langfuse_handler],
                },
            )
            return memory_node
        except Exception as e:
            logger.error(f"Error extracting learning: {str(e)}")
            raise e

    async def search_memory(self, query: str, role: str, workspace_id: str) -> str:
        collection_name = settings.QDRANT_COLLECTION_NAME

        embedding = await self.generate_embedding_with_retry(query)
        query_response = await self.aqdrant_client.query_points(
            collection_name=collection_name,
            query=embedding,
            query_filter=qdrant_models.Filter(
                must=[
                    qdrant_models.FieldCondition(
                        key="role",
                        match=qdrant_models.MatchValue(value=role),
                    ),
                    qdrant_models.FieldCondition(
                        key="workspace_id",
                        match=qdrant_models.MatchValue(value=workspace_id),
                    ),
                ],
            ),
            limit=settings.MEMORY_MAX_RELATED_MEMORY_NODES,
            score_threshold=settings.MEMORY_RELATEDNESS_THRESHOLD,
            with_payload=True,
            with_vectors=False,
        )
        points = query_response.points
        logger.info(f"Found {len(points)} related memories")
        if len(points) == 0:
            return "No related memories found"

        results = ""
        for index, point in enumerate(points):
            score = point.score
            payload = point.payload
            node_data = json.loads(payload.get("_node_content"))["text_resource"][
                "text"
            ]
            node_data = json.loads(node_data)
            # Task -> Score -> Tags -> Solution
            result = f"""Memory {index + 1}.\nTask: {node_data.get("task", "No task")}\nScore: {score}\nTags: {node_data.get("tags", "No tags")}\nSolution: {node_data.get("solution", "No solution")}\n"""
            results += result
        return results
