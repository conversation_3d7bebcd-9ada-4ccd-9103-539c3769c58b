"""
Service for managing user point limits across all knowledge bases.
Each document chunk becomes a "point" in the vector database.
Users are limited to 10,000 total points across all their KBs.
"""

from uuid import UUID

from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.config import settings
from app.core.qdrant import aclient
from app.logger import logger
from app.models import KB


class PointLimitService:
    """Service for managing and validating user point limits"""

    MAX_POINTS_PER_USER = settings.MAX_POINTS_PER_USER

    def __init__(self, session: AsyncSession):
        self.session = session
        self.qdrant_client = aclient

    async def get_user_kb_ids(self, user_id: UUID, workspace_id: UUID) -> list[str]:
        """Get all KB IDs that the user has access to"""
        try:
            query = select(KB.id).where(
                KB.allowed_users.any(user_id),
                KB.workspace_id == workspace_id,
                KB.is_deleted == False,
            )
            result = await self.session.exec(query)
            kb_ids = result.all()
            return [str(kb_id) for kb_id in kb_ids]
        except Exception as e:
            logger.error(f"Error getting user KB IDs: {str(e)}")
            raise

    async def count_points_for_kb(self, kb_id: str, workspace_id: str) -> int:
        """Count total points for a specific KB in the single collection"""
        try:
            from qdrant_client import models as qdrant_models

            collection_name = settings.QDRANT_COLLECTION_NAME

            # Check if collection exists
            collections = await self.qdrant_client.get_collections()
            collection_names = [col.name for col in collections.collections]

            if collection_name not in collection_names:
                return 0

            # Count points in the collection filtered by kb_id and workspace_id
            count_result = await self.qdrant_client.count(
                collection_name=collection_name,
                count_filter=qdrant_models.Filter(
                    must=[
                        qdrant_models.FieldCondition(
                            key="kb_id",
                            match=qdrant_models.MatchValue(value=kb_id),
                        ),
                        qdrant_models.FieldCondition(
                            key="workspace_id",
                            match=qdrant_models.MatchValue(value=workspace_id),
                        ),
                    ]
                ),
            )
            return count_result.count
        except Exception as e:
            logger.error(f"Error counting points for KB {kb_id}: {str(e)}")
            # Return 0 if there's an error to avoid blocking operations
            return 0

    async def get_user_total_points(self, user_id: UUID, workspace_id: UUID) -> int:
        """Get total points across all user's KBs"""
        try:
            kb_ids = await self.get_user_kb_ids(user_id, workspace_id)
            total_points = 0

            for kb_id in kb_ids:
                points = await self.count_points_for_kb(kb_id, str(workspace_id))
                total_points += points
                logger.debug(f"KB {kb_id}: {points} points")

            logger.info(f"User {user_id} total points: {total_points}")
            return total_points
        except Exception as e:
            logger.error(f"Error getting user total points: {str(e)}")
            raise

    async def check_user_point_limit(
        self, user_id: UUID, workspace_id: UUID
    ) -> tuple[bool, int, int]:
        """
        Check if user has exceeded their point limit.

        Returns:
            (can_add, current_points, limit)
        """
        try:
            current_points = await self.get_user_total_points(user_id, workspace_id)
            can_add = current_points <= self.MAX_POINTS_PER_USER

            logger.info(
                f"Point limit check - User: {user_id}, "
                f"Current: {current_points}, "
                f"Limit: {self.MAX_POINTS_PER_USER}, "
                f"Can add: {can_add}"
            )

            return (
                can_add,
                current_points,
                self.MAX_POINTS_PER_USER,
            )
        except Exception as e:
            logger.error(f"Error checking point limit: {str(e)}")
            # In case of error, allow the operation to proceed to avoid blocking users
            return True, 0, self.MAX_POINTS_PER_USER

    async def get_user_point_usage(self, user_id: UUID, workspace_id: UUID) -> dict:
        """Get detailed point usage information for a user"""
        try:
            kb_ids = await self.get_user_kb_ids(user_id, workspace_id)
            kb_usage = {}
            total_points = 0

            for kb_id in kb_ids:
                points = await self.count_points_for_kb(kb_id, str(workspace_id))
                kb_usage[kb_id] = points
                total_points += points

            return {
                "user_id": str(user_id),
                "total_points": total_points,
                "max_points": self.MAX_POINTS_PER_USER,
                "remaining_points": max(0, self.MAX_POINTS_PER_USER - total_points),
                "usage_percentage": round(
                    (total_points / self.MAX_POINTS_PER_USER) * 100, 2
                ),
                "kb_usage": kb_usage,
                "is_over_limit": total_points > self.MAX_POINTS_PER_USER,
            }
        except Exception as e:
            logger.error(f"Error getting user point usage: {str(e)}")
            raise


class PointLimitError(Exception):
    """Exception raised when user exceeds point limit"""

    def __init__(self, current_points: int, limit: int):
        self.current_points = current_points
        self.limit = limit

        message = f"Point limit exceeded. Current: {current_points}, Limit: {limit}"
        super().__init__(message)
