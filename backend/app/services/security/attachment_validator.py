from app.schemas.message_attachment import AttachmentFileInfo, MAX_FILE_SIZE
from app.schemas.mime_type import (
    ALLOWED_MIME_TYPES_FOR_ATTACHMENT,
    sanitize_filename,
    validate_mime_type,
)

def validate_attachment_metadata(file_info: AttachmentFileInfo) -> tuple[bool, str]:
    """
    Validates the metadata of a file to be uploaded as an attachment.
    """
    # Validate file size
    if file_info.file_size > MAX_FILE_SIZE:
        return False, f"File size exceeds the limit of {MAX_FILE_SIZE // 1024 // 1024}MB."

    # Validate MIME type
    if not validate_mime_type(file_info.content_type, ALLOWED_MIME_TYPES_FOR_ATTACHMENT):
        return False, f"File type '{file_info.content_type}' is not allowed."

    # Check for directory traversal
    if "../" in file_info.filename or "..\\" in file_info.filename:
        return False, "Invalid filename: directory traversal is not allowed."

    # Sanitize filename
    try:
        sanitize_filename(file_info.filename)
    except ValueError as e:
        return False, f"Invalid filename: {e}"

    return True, ""
