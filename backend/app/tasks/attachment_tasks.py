from celery import shared_task
from app.core.config import settings
from app.core.db_session import get_task_session
from app.repositories.attachment import AttachmentRepository
from app.repositories.object_storage.provider import get_object_storage_repository
from app.schemas.message_attachment import UploadedAttachmentInfo
from app.services.security.attachment_validator import validate_attachment_metadata
from app.logger import logger
import asyncio

@shared_task(name="validate_attachment_task")
def validate_attachment_task(uploaded_files: list[dict], user_id: str, workspace_id: str):
    """
    Celery task to validate uploaded attachments, including security scans.
    """
    loop = asyncio.get_event_loop()

    async def main():
        async with get_task_session() as session:
            attachment_repo = AttachmentRepository(session)
            os_repo = get_object_storage_repository()

            results = []

            for file_data in uploaded_files:
                file_info = UploadedAttachmentInfo(**file_data)

                # 1. Download file from S3 for validation
                try:
                    file_data, _, _, _ = await os_repo.get_file(
                        object_name=file_info.storage_key,
                        bucket_name=settings.ATTACHMENT_BUCKET,
                    )
                    # Get the actual file content as bytes
                    file_content = file_data.getvalue()
                except Exception as e:
                    logger.error(f"Failed to download file {file_info.storage_key}: {e}")
                    # Here you might want to update the task status to FAILED
                    continue

                # 2. Size Check
                if len(file_content) > 25 * 1024 * 1024: # 25MB
                    logger.error(f"File {file_info.storage_key} exceeds size limit after download.")
                    # Update task status to FAILED
                    continue

                # 3. Run security validation
                # This is a placeholder for a more comprehensive security scan
                is_valid, error = validate_attachment_metadata(file_info)
                if not is_valid:
                    logger.error(f"Security validation failed for {file_info.storage_key}: {error}")
                    # Update task status to FAILED
                    continue

                # 4. Create MessageAttachment record
                try:
                    attachment = await attachment_repo.create_attachment(
                        file_info=file_info,
                        owner_id=user_id,
                    )
                    logger.info(f"Successfully validated and created attachment {attachment.id} for {file_info.storage_key}")

                    # Add successful result
                    results.append({
                        "file_id": file_info.file_id,
                        "attachment_id": str(attachment.id),
                        "status": "success"
                    })

                except Exception as e:
                    logger.error(f"Failed to create attachment record for {file_info.storage_key}: {e}")
                    # Add failed result
                    results.append({
                        "file_id": file_info.file_id,
                        "status": "failed",
                        "error": str(e)
                    })
                    continue

            return {"attachments": results}

    return loop.run_until_complete(main())
