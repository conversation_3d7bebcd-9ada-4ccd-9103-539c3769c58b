import asyncio
import os
import uuid
from datetime import UTC, datetime
from functools import wraps
from http.client import <PERSON><PERSON>P<PERSON>xception
from logging import getLogger
from typing import Any

from celery.signals import worker_process_init
from pydantic import BaseModel
from sqlmodel import Session

from app.core.celery_app import HIGH_PRIORITY, celery_app
from app.core.db import engine
from app.models import (
    TaskExecutionStatus,
    TaskHistory,
)
from app.services.agent import AutonomousAgentService

logger = getLogger(__name__)


def async_to_sync(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        return loop.run_until_complete(f(*args, **kwargs))

    return wrapper


@worker_process_init.connect
def initialize_worker_agent_factory(**kwargs):
    """Initialize AgentFactory once per worker process"""

    logger.info(f"Worker {os.getpid()} starting - initializing AgentFactory")

    @async_to_sync
    async def initialize_agent_factory():
        from app.modules.multi_agents.core.agents.factory import AgentFactory

        return await AgentFactory.initialize()

    initialize_agent_factory()
    logger.info(f"AgentFactory initialized for worker {os.getpid()}")


def ensure_utc(dt: datetime) -> datetime:
    """Ensure datetime is in UTC"""
    if dt.tzinfo is None:
        return dt.replace(tzinfo=UTC)
    return dt.astimezone(UTC)


class AgentExecutionResult(BaseModel):
    """Result of agent execution"""

    conversation_id: uuid.UUID
    message_result: dict[str, Any]
    status: str = "success"  # success, required_approval, error
    message: str | None = None


async def continue_interrupted_task(
    session: Session, task_history: TaskHistory, message: str, approve: bool
) -> AgentExecutionResult:
    """Continue an interrupted autonomous agent task"""

    try:
        from celery import current_task

        autonomous_agent_service = AutonomousAgentService(session)
        start_time = datetime.now(UTC)

        task_history.status = TaskExecutionStatus.RUNNING
        task_history.celery_task_id = current_task.request.id
        session.add(task_history)
        session.commit()

        try:
            message_result = await autonomous_agent_service.process_message(
                conversation_id=task_history.conversation_id,
                message_content=message,
                message_action_type=None,
                resume=True,
                approve=approve,
                restore=False,
                restore_message_id=None,
                user_id=task_history.task.owner_id,
            )
        except Exception as e:
            task_history.status = TaskExecutionStatus.FAILED
            task_history.message = str(e)
            task_history.end_time = datetime.now(UTC)
            task_history.run_time = task_history.run_time + int(
                (task_history.end_time - ensure_utc(start_time)).total_seconds()
            )
            session.add(task_history)
            session.commit()
            return AgentExecutionResult(
                conversation_id=task_history.conversation_id,
                message_result={},
                status="error",
                message=str(e),
            )

        if message_result.get("type") == "interrupt":
            task_history.status = TaskExecutionStatus.REQUIRED_APPROVAL
            task_history.message = message_result.get("content")
        else:
            task_history.status = TaskExecutionStatus.SUCCEEDED

        task_history.end_time = datetime.now(UTC)
        task_history.run_time = int(
            (task_history.end_time - ensure_utc(start_time)).total_seconds()
        )
        session.add(task_history)
        session.commit()

        if message_result.get("type") == "interrupt":
            return AgentExecutionResult(
                conversation_id=task_history.conversation_id,
                message_result=message_result,
                status="required_approval",
                message=message_result.get("content"),
            )
        else:
            return AgentExecutionResult(
                conversation_id=task_history.conversation_id,
                message_result=message_result,
                status="success",
                message=None,
            )
    except Exception as e:
        logger.error(f"Task {task_history.id} execution failed: {e}")
        return AgentExecutionResult(
            conversation_id=task_history.conversation_id,
            message_result={},
            status="error",
            message=str(e),
        )


@celery_app.task(name="autonomous_agent_tasks.continue_task", priority=HIGH_PRIORITY)
def autonomous_agent_continue_task(task_history_id: str, message: str, approve: bool):
    """Continue an autonomous agent task"""
    logger.info(f"Starting autonomous agent task continue for task {task_history_id}")

    with Session(engine) as session:
        task_history = session.get(TaskHistory, task_history_id)
        if not task_history:
            logger.error(f"Task history {task_history_id} not found")
            raise HTTPException(status_code=404, detail="Task history not found")

        # Use the decorator to properly handle async execution
        @async_to_sync
        async def continue_task() -> AgentExecutionResult:
            return await continue_interrupted_task(
                session, task_history, message, approve
            )

        continue_task()
