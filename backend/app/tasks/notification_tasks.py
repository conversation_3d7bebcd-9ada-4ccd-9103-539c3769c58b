import asyncio
import logging
from functools import wraps
from typing import Any
from uuid import UUID

from app.core.celery_app import NORMAL_PRIORITY, celery_app
from app.core.db_session import get_task_session
from app.models import Notification, NotificationStatus, NotificationType
from app.repositories.notification import NotificationRepository

logger = logging.getLogger(__name__)


def async_to_sync(f):
    """Decorator to convert async functions to sync for Celery tasks."""

    @wraps(f)
    def wrapper(*args, **kwargs):
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        return loop.run_until_complete(f(*args, **kwargs))

    return wrapper


@celery_app.task(
    name="notification_tasks.create_notification",
    bind=True,
    priority=NORMAL_PRIORITY,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    retry_backoff=True,
    retry_backoff_max=300,
    retry_jitter=True,
)
@async_to_sync
async def create_notification(
    self,
    title: str,
    message: str,
    user_id: str,
    type: str = NotificationType.INFO.value,
    status: str = NotificationStatus.UNREAD.value,
    requires_action: bool = False,
    action_url: str | None = None,
    metadata: dict[str, Any] | None = None,
    expires_at: str | None = None,
) -> dict[str, Any]:
    """Create a notification in the background.

    Args:
        title: Notification title
        message: Notification message content
        user_id: ID of the user to notify
        type: Notification type (INFO, WARNING, ERROR, INTERRUPT)
        status: Notification status (UNREAD, READ, ARCHIVED)
        requires_action: Whether the notification requires user action
        action_url: Optional URL for direct navigation
        metadata: Optional metadata dictionary
        expires_at: Optional expiration datetime string

    Returns:
        Dictionary with notification creation details
    """
    try:
        async with get_task_session() as session:
            notification_repo = NotificationRepository(session)

            notification_data = {
                "title": title,
                "message": message,
                "user_id": UUID(user_id),
                "type": NotificationType(type),
                "status": NotificationStatus(status),
                "requires_action": requires_action,
                "action_url": action_url,
                "metadata": metadata or {},
            }

            if expires_at:
                from datetime import datetime

                notification_data["expires_at"] = datetime.fromisoformat(expires_at)

            notification = Notification(**notification_data)
            created_notification = await notification_repo.create(notification)

            logger.info(
                f"Created notification {created_notification.id} for user {user_id}"
            )
            return {
                "notification_id": str(created_notification.id),
                "user_id": user_id,
                "title": title,
                "status": "created",
            }

    except Exception as e:
        logger.error(f"Failed to create notification: {e}")
        raise


@celery_app.task(
    name="notification_tasks.create_interrupt_notification",
    bind=True,
    priority=NORMAL_PRIORITY,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    retry_backoff=True,
    retry_backoff_max=300,
    retry_jitter=True,
)
@async_to_sync
async def create_interrupt_notification(
    self,
    user_id: str,
    interrupt_message: str,
    conversation_id: str | None = None,
    requires_action: bool = True,
    action_url: str | None = None,
    message_id: str | None = None,
) -> dict[str, Any]:
    """Create an interrupt notification in the background.

    Args:
        user_id: ID of the user to notify
        interrupt_message: The interrupt message content
        conversation_id: Optional conversation ID for context
        requires_action: Whether the notification requires user action
        action_url: URL for direct navigation to the agent conversation
        message_id: The message ID that was interrupted

    Returns:
        Dictionary with task execution details
    """
    try:
        async with get_task_session() as session:
            notification_repo = NotificationRepository(session)

            # Format interrupt message to be more concise
            title, extracted_details = _format_interrupt_message(interrupt_message)

            metadata = {
                "interrupt_type": "agent_action_required",
                "extracted_details": extracted_details,
            }

            if conversation_id:
                metadata["conversation_id"] = conversation_id
            if message_id:
                metadata["message_id"] = message_id

            notification = Notification(
                title=title,
                message=interrupt_message,
                user_id=UUID(user_id),
                type=NotificationType.INTERRUPT,
                requires_action=requires_action,
                action_url=action_url,
                metadata=metadata,
            )

            created_notification = await notification_repo.create(notification)

            logger.info(
                f"Created interrupt notification {created_notification.id} for user {user_id}"
            )
            return {
                "notification_id": str(created_notification.id),
                "user_id": user_id,
                "interrupt_type": "agent_action_required",
                "status": "created",
            }

    except Exception as e:
        logger.error(f"Failed to create interrupt notification: {e}")
        raise


@celery_app.task(
    name="notification_tasks.create_resource_status_notification",
    bind=True,
    priority=NORMAL_PRIORITY,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    retry_backoff=True,
    retry_backoff_max=300,
    retry_jitter=True,
)
@async_to_sync
async def create_resource_status_notification(
    self,
    resource_id: str,
    resource_name: str,
    resource_type: str,
    old_status: str,
    new_status: str,
    workspace_id: str,
) -> dict[str, Any]:
    """Create a resource status change notification for all workspace users.

    Args:
        resource_id: ID of the resource that changed
        resource_name: Name of the resource
        resource_type: Type of the resource (e.g., EC2, RDS)
        old_status: Previous status of the resource
        new_status: New status of the resource
        workspace_id: ID of the workspace containing the resource

    Returns:
        Dictionary with task execution details
    """
    try:
        async with get_task_session() as session:
            from sqlmodel import select

            from app.models import UserWorkspace, Workspace

            notification_repo = NotificationRepository(session)

            # Get all users in the workspace (owner + invited users)
            workspace_query = select(Workspace).where(
                Workspace.id == UUID(workspace_id)
            )
            result = await session.execute(workspace_query)
            workspace = result.scalar_one_or_none()

            if not workspace:
                logger.warning(
                    f"Workspace {workspace_id} not found for resource status notification"
                )
                return {"status": "workspace_not_found"}

            user_ids = set()

            # Add workspace owner
            user_ids.add(workspace.owner_id)

            # Add invited users
            user_workspace_query = select(UserWorkspace.user_id).where(
                UserWorkspace.workspace_id == UUID(workspace_id)
            )
            result = await session.execute(user_workspace_query)
            invited_user_ids = result.scalars().all()
            user_ids.update(invited_user_ids)

            # Create notification title and message
            status_icon = _get_status_icon(new_status)
            title = f"{status_icon} {resource_type} Status Changed"

            message = f"Resource '{resource_name}' status changed from {old_status.upper()} to {new_status.upper()}"

            # Determine notification type based on new status
            notification_type = _get_notification_type_for_status(new_status)

            # Create metadata
            metadata = {
                "resource_id": resource_id,
                "resource_name": resource_name,
                "resource_type": resource_type,
                "old_status": old_status,
                "new_status": new_status,
                "workspace_id": workspace_id,
                "change_type": "status_change",
            }

            # Action URL to view the resource
            action_url = f"/dashboard/resources/{resource_id}"

            notifications_created = 0

            # Create notification for each user
            for user_id in user_ids:
                try:
                    notification = Notification(
                        title=title,
                        message=message,
                        user_id=user_id,
                        type=notification_type,
                        requires_action=False,  # Status changes are informational
                        action_url=action_url,
                        metadata=metadata,
                    )

                    await notification_repo.create(notification)
                    notifications_created += 1

                except Exception as e:
                    logger.error(
                        f"Failed to create notification for user {user_id}: {e}"
                    )
                    continue

            logger.info(
                f"Created {notifications_created} resource status notifications for "
                f"{resource_name} ({old_status} -> {new_status})"
            )

            return {
                "resource_id": resource_id,
                "old_status": old_status,
                "new_status": new_status,
                "notifications_created": notifications_created,
                "status": "completed",
            }

    except Exception as e:
        logger.error(f"Failed to create resource status notifications: {e}")
        raise


def _format_interrupt_message(message: str) -> tuple[str, dict]:
    """Format interrupt message to be more concise.

    Returns:
        Tuple of (formatted_title, extracted_details)
    """
    try:
        # Try to extract operation details if it's in the expected format
        if "Operation:" in message:
            # Split by "Operation:" and get the part after it
            parts = message.split("Operation:", 1)
            if len(parts) > 1:
                operation_part = parts[1].strip()
                # Extract the first line as the operation summary
                operation_lines = operation_part.split("\n")
                operation_summary = operation_lines[0].strip()

                title = f"🤖 Agent Action Required: {operation_summary}"

                # Extract additional details
                details = {}
                for line in operation_lines[1:]:
                    if ":" in line:
                        key, value = line.split(":", 1)
                        details[key.strip()] = value.strip()

                return title, details

        # Fallback: use first 100 characters as title
        title = f"🤖 Agent Action Required: {message[:100]}..."
        return title, {"full_message": message}

    except Exception:
        # If parsing fails, return a generic title
        return "🤖 Agent Action Required", {"full_message": message}


def _get_status_icon(status: str) -> str:
    """Get an appropriate icon for the resource status."""
    status = status.lower()

    if status in ["running", "available", "active"]:
        return "🟢"
    elif status in ["starting", "creating", "pending", "updating"]:
        return "🟡"
    elif status in ["stopped", "stopping", "failed", "error"]:
        return "🔴"
    elif status in ["deleted", "deleting"]:
        return "⚫"
    else:
        return "🔵"  # Default for "found" or unknown status


def _get_notification_type_for_status(status: str) -> NotificationType:
    """Determine the appropriate notification type based on resource status."""
    status = status.lower()

    if status in ["failed", "error", "deleted"]:
        return NotificationType.ERROR
    elif status in ["stopped", "stopping", "degraded"]:
        return NotificationType.WARNING
    else:
        return NotificationType.INFO
