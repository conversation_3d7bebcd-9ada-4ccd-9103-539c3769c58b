from uuid import uuid4
from unittest.mock import AsyncMock, MagicMock
from app.services.attachment_service import AttachmentService
from app.schemas.message_attachment import AttachmentFileInfo, UploadedAttachmentInfo
from sqlmodel import Session

async def test_generate_presigned_urls_success(db_session: Session):
    service = AttachmentService(db_session)
    service.os_repo.get_presigned_put_url = AsyncMock(return_value="http://presigned.url")

    files = [AttachmentFileInfo(file_id="1", filename="test.png", content_type="image/png", file_size=1024)]
    user_id = uuid4()
    workspace_id = uuid4()

    response, error = await service.generate_presigned_urls(files, user_id, workspace_id)

    assert error is None
    assert response is not None
    assert len(response.presigned_urls) == 1
    assert response.presigned_urls[0].presigned_url == "http://presigned.url"

async def test_verify_uploads(db_session: Session):
    service = AttachmentService(db_session)
    service.os_repo.file_exists = AsyncMock(side_effect=[True, False])

    uploaded_files = [
        UploadedAttachmentInfo(file_id="1", filename="f1", storage_key="k1", content_type="image/png", file_size=1),
        UploadedAttachmentInfo(file_id="2", filename="f2", storage_key="k2", content_type="image/png", file_size=1),
    ]

    verified = await service.verify_uploads(uploaded_files)
    assert len(verified) == 1
    assert verified[0].storage_key == "k1"

async def test_get_download_url_success(db_session: Session):
    user_id = uuid4()
    attachment_id = uuid4()

    mock_attachment = MagicMock()
    mock_attachment.owner_id = user_id
    mock_attachment.storage_key = "test_key"

    service = AttachmentService(db_session)
    service.attachment_repo.get_attachment_by_id = AsyncMock(return_value=mock_attachment)
    service.os_repo.get_presigned_url = AsyncMock(return_value="http://download.url")

    download_url, error = await service.get_download_url(attachment_id, user_id)

    assert error is None
    assert download_url == "http://download.url"

async def test_get_download_url_permission_denied(db_session: Session):
    user_id = uuid4()
    other_user_id = uuid4()
    attachment_id = uuid4()

    mock_attachment = MagicMock()
    mock_attachment.owner_id = other_user_id

    service = AttachmentService(db_session)
    service.attachment_repo.get_attachment_by_id = AsyncMock(return_value=mock_attachment)

    download_url, error = await service.get_download_url(attachment_id, user_id)

    assert download_url is None
    assert error is not None
    assert "permission" in error.lower()
