[project]
name = "app"
version = "0.1.0"
description = ""
requires-python = ">=3.11,<4.0"
dependencies = [
    "fastapi[standard]<1.0.0,>=0.115.6",
    "python-multipart<1.0.0,>=0.0.7",
    "email-validator<*******,>=2.1.0.post1",
    "passlib[bcrypt]<2.0.0,>=1.7.4",
    "tenacity<9.0.0,>=8.2.3",
    "pydantic>2.0",
    "emails<1.0,>=0.6",
    "jinja2<4.0.0,>=3.1.4",
    "alembic<2.0.0,>=1.12.1",
    "httpx<1.0.0,>=0.25.1",
    "psycopg[binary]>=3.1.13,<4.0.0",
    "sqlmodel<1.0.0,>=0.0.21",
    # Pin bcrypt until passlib supports the latest
    "bcrypt==4.0.1",
    "pydantic-settings<3.0.0,>=2.2.1",
    "sentry-sdk[fastapi]<2.0.0,>=1.40.6",
    "pyjwt<3.0.0,>=2.8.0",
    "cryptography==44.0.0",
    "boto3>=1.37.24",
    "redis==5.2.1",
    "python-redis-lock==4.0.0",
    "rq-scheduler==0.14.0",
    "Faker==33.1.0",
    "alembic-autogenerate-enums==0.1.2",
    "langchain>=0.3.14",
    "langchain-aws==0.2.26",
    "langchain-community>=0.3.14",
    "langgraph==0.2.74",
    "beautifulsoup4>=4.12.3",
    "requests>=2.32.3",
    "fastapi-sso>=0.17.0",
    "authlib>=1.4.0",
    "itsdangerous>=2.2.0",
    "celery[redis]>=5.4.0",
    "flower>=2.0.1",
    "langchain-ollama>=0.2.3",
    "anthropic>=0.38.0",
    "greenlet>=3.1.1",
    "websockets>=12.0",
    "langgraph-checkpoint-postgres>=2.0.14",
    "psycopg-pool>=3.2.4",
    "croniter>=6.0.0",
    "loguru>=0.7.3",
    "nest-asyncio>=1.6.0",
    "langmem==0.0.16",
    "langchain-openai>=0.3.8",
    "mcp==1.9.4",
    "langchain-mcp-adapters>=0.0.5",
    "minio>=7.2.15",
    "pytest-asyncio>=0.23.8",
    "langchain-postgres>=0.0.13",
    "python-slugify>=8.0.4",
    "pdfminer-six>=20250327",
    "llama-cloud-services>=0.6.9",
    "llama-index-core>=0.12.27",
    "llama-index-readers-file>=0.4.9",
    "llama-index-vector-stores-qdrant>=0.6.0",
    "llama-index-embeddings-bedrock<=0.5.0",
    "qdrant-client>=1.12.1",
    "fastembed>=0.7.0",
    "llama-index-postprocessor-bedrock-rerank>=0.3.1",
    "llama-index-llms-langchain>=0.6.1",
    "llama-index-llms-bedrock>=0.3.8",
    "llama-index-retrievers-bm25>=0.5.2",
    "langfuse>=2.60.5",
    "miniopy-async>=1.23.0",
    "pyrefly>=0.17.1",
    "pip>=25.1.1",
    "alembic-postgresql-enum>=1.7.0",
    "langchain-anthropic>=0.3.10",
    "firecrawl>=2.7.1",
    "transformers[torch]>=4.52.4",
    "psutil>=5.9.0",
    "llama-index-readers-docling>=0.3.2",
    "fitz>=0.0.1.dev2",
    "async-lru>=2.0.5",
    "crawl4ai>=0.6.3",
    "python-magic>=0.4.27",
    "slowapi>=0.1.9",
    "pdf2image>=1.17.0",
]

[tool.uv]
dev-dependencies = [
    "pytest<8.0.0,>=7.4.3",
    "mypy<2.0.0,>=1.8.0",
    "ruff<1.0.0,>=0.2.2",
    "pre-commit<4.0.0,>=3.6.2",
    "types-passlib<*******,>=1.7.7.20240106",
    "coverage<8.0.0,>=7.4.3",
]

[tool.uv.sources]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.mypy]
strict = true
exclude = ["venv", ".venv", "alembic"]

[tool.ruff]
target-version = "py310"
exclude = ["alembic"]

[tool.ruff.lint]
select = [
    "E",      # pycodestyle errors
    "W",      # pycodestyle warnings
    "F",      # pyflakes
    "I",      # isort
    "B",      # flake8-bugbear
    "C4",     # flake8-comprehensions
    "UP",     # pyupgrade
    "ARG001", # unused arguments in functions
]
ignore = [
    "E501", # line too long, handled by black
    "B008", # do not perform function calls in argument defaults
    "W191", # indentation contains tabs
    "B904", # Allow raising exceptions without from e, for HTTPException
    "ARG001", # unused arguments in functions
    "UP038", # unused import
    "UP035",
    "E712",
]

[tool.ruff.lint.pyupgrade]
# Preserve types, even if a file imports `from __future__ import annotations`.
keep-runtime-typing = true

[tool.pytest.ini_options]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
]
log_cli = true
log_cli_level = "INFO"
addopts = "-s"
