services:
  adminer:
    image: adminer
    restart: always
    networks:
      - default
    ports:
      - "8080:8080"
    depends_on:
      - db
    environment:
      - ADMINER_DESIGN=pepa-linha-dark

  redis-insight:
    image: redis/redisinsight:latest
    restart: always
    depends_on:
      - redis
    networks:
      - default
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379

  flower:
    image: "${DOCKER_IMAGE_BACKEND?Variable not set}:${TAG-latest}"
    restart: always
    command: celery -A app.worker.celery_app flower --port=5555
    networks:
      - default
    depends_on:
      redis:
        condition: service_started
      worker:
        condition: service_started
    env_file:
      - .env
    environment:
      - FLOWER_BASIC_AUTH=${FLOWER_USER?Variable not set}:${FLOWER_PASSWORD?Variable not set}
      - REDIS_SERVER=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    healthcheck:
      test: ["C<PERSON>", "curl", "-f", "http://localhost:5555"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  dozzle:
    image: amir20/dozzle:latest
    restart: always
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./dozzle/data:/data
    ports:
      - "9999:8080"
    environment:
      - DOZZLE_LEVEL=debug
      - DOZZLE_TAILSIZE=300
      - DOZZLE_AUTH_PROVIDER=simple
    networks:
      - default
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost:8080"]
      interval: 30s
      timeout: 10s
      retries: 3
