# Amazon ECR Setup for CloudThinker

This document explains how to set up Amazon Elastic Container Registry (ECR) for CloudThinker's CI/CD pipeline.

## Overview

CloudThinker uses ECR to store Docker images for all microservices:
- `backend` - FastAPI backend service
- `frontend` - Next.js frontend application
- `executor` - AI executor service
- `payment` - Payment processing service
- `slack-integration` - Slack integration service

## Prerequisites

- AWS CLI installed and configured
- Docker installed
- Appropriate AWS permissions for ECR operations

## Setup Steps

### 1. Create ECR Repositories

Run the setup script to create all required ECR repositories:

```bash
./scripts/aws/setup-ecr.sh [AWS_REGION] [REPOSITORY_PREFIX]
```

Example:
```bash
./scripts/aws/setup-ecr.sh us-east-1 cloudthinker
```

This script will:
- Create ECR repositories for all services
- Configure lifecycle policies for image retention
- Enable vulnerability scanning
- Set up encryption

### 2. Configure GitLab CI/CD Variables

Add the following variables to your GitLab project settings:

| Variable | Description | Example |
|----------|-------------|---------|
| `AWS_ACCOUNT_ID` | Your AWS Account ID | `************` |
| `AWS_DEFAULT_REGION` | AWS region for ECR | `us-east-1` |
| `ECR_REPOSITORY_PREFIX` | Repository prefix | `cloudthinker` |
| `AWS_ACCESS_KEY_ID` | AWS access key for CI/CD | `AKIA...` |
| `AWS_SECRET_ACCESS_KEY` | AWS secret key for CI/CD | `...` |

### 3. Update Environment Configuration

For production deployments, update your `.env` file with ECR image references:

```bash
# ECR Configuration
AWS_ACCOUNT_ID=************
AWS_DEFAULT_REGION=us-east-1
ECR_REPOSITORY_PREFIX=cloudthinker

# Docker Images (ECR)
DOCKER_IMAGE_BACKEND=************.dkr.ecr.us-east-1.amazonaws.com/cloudthinker/backend
DOCKER_IMAGE_FRONTEND=************.dkr.ecr.us-east-1.amazonaws.com/cloudthinker/frontend
DOCKER_IMAGE_EXECUTOR=************.dkr.ecr.us-east-1.amazonaws.com/cloudthinker/executor
DOCKER_IMAGE_PAYMENT=************.dkr.ecr.us-east-1.amazonaws.com/cloudthinker/payment
DOCKER_IMAGE_SLACK_INTEGRATION=************.dkr.ecr.us-east-1.amazonaws.com/cloudthinker/slack-integration
```

## CI/CD Pipeline

The updated GitLab CI pipeline includes:

### Build Stage
- Builds Docker images for all services
- Tags images with commit SHA
- Pushes images to ECR
- Tags `latest` for main branch builds

### Deploy Stages
- Pulls images from ECR
- Deploys using docker-compose with production overrides
- Supports dev, staging, and production environments

## Local Development

### ECR Login
```bash
./scripts/aws/ecr-login.sh [AWS_REGION]
```

### Pull Images Locally
```bash
docker pull ************.dkr.ecr.us-east-1.amazonaws.com/cloudthinker/backend:latest
```

### Build and Push Manually
```bash
# Build
docker build -t ************.dkr.ecr.us-east-1.amazonaws.com/cloudthinker/backend:dev ./backend

# Push
docker push ************.dkr.ecr.us-east-1.amazonaws.com/cloudthinker/backend:dev
```

## Image Lifecycle Management

ECR repositories are configured with lifecycle policies:

- **Production images** (`v*`, `prod*`): Keep last 10 images
- **Latest images**: Keep last 5 images
- **Untagged images**: Delete after 1 day

## Security Features

- **Vulnerability Scanning**: Enabled on push
- **Encryption**: AES256 encryption at rest
- **Access Control**: IAM-based access control
- **Image Signing**: Can be enabled for additional security

## Troubleshooting

### Authentication Issues
```bash
# Check AWS credentials
aws sts get-caller-identity

# Re-login to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com
```

### Repository Not Found
```bash
# List repositories
aws ecr describe-repositories --region us-east-1

# Create missing repository
aws ecr create-repository --repository-name cloudthinker/backend --region us-east-1
```

### Image Pull Issues
```bash
# Check image exists
aws ecr describe-images --repository-name cloudthinker/backend --region us-east-1

# List image tags
aws ecr list-images --repository-name cloudthinker/backend --region us-east-1
```

## Cost Optimization

- Use lifecycle policies to automatically delete old images
- Monitor ECR usage in AWS Cost Explorer
- Consider using ECR Public for open-source components
- Optimize image sizes to reduce storage costs

## Monitoring

Monitor ECR usage through:
- AWS CloudWatch metrics
- ECR console
- AWS Cost and Usage Reports
- Third-party monitoring tools
