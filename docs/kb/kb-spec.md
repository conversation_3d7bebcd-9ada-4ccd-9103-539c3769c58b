# Knowledge Base Feature Analysis

## Overview

The Knowledge Base (KB) feature is a comprehensive document management and semantic search system that allows users to ingest, store, and query documents using advanced AI and vector search capabilities. The system supports both file uploads and website crawling, with intelligent chunking, embedding, and retrieval mechanisms.

## Architecture Overview

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[React/Next.js UI]
        Components[KB Components]
        State[State Management]
    end

    subgraph "API Layer"
        KBRoutes[KB API Routes]
        RuntimeRoutes[KB Runtime Routes]
        Auth[Authentication]
    end

    subgraph "Service Layer"
        KBService[KBService]
        KBRepository[KBRepository]
        PointLimit[Point Limit Service]
    end

    subgraph "Processing Layer"
        Tasks[Celery Tasks]
        FileReader[File Reader]
        WebReader[Website Reader]
        Embedder[Embedding Service]
    end

    subgraph "Storage Layer"
        Postgres[(PostgreSQL)]
        Qdrant[(Qdrant Vector DB)]
        S3[(Object Storage)]
    end

    UI --> KBRoutes
    UI --> RuntimeRoutes
    KBRoutes --> KBService
    RuntimeRoutes --> KBService
    KBService --> KBRepository
    KBService --> Tasks
    Tasks --> FileReader
    Tasks --> WebReader
    Tasks --> Embedder
    KBRepository --> Postgres
    Embedder --> Qdrant
    FileReader --> S3
    WebReader --> S3
```

## Backend Implementation

### Core Models and Database Schema

#### Knowledge Base Model
- **Table**: `knowledge_bases`
- **Key Fields**:
  - `id`: UUID primary key
  - `title`: KB name (1-255 chars, required)
  - `description`: Optional description (max 1000 chars)
  - `access_level`: PRIVATE | SHARED
  - `usage_mode`: MANUAL | AGENT_REQUESTED | ALWAYS
  - `workspace_id`: Foreign key to workspace
  - `owner_id`: Foreign key to user
  - `allowed_users`: Array of user UUIDs for shared access
  - `tags`: Array of strings for categorization
  - `created_at`, `updated_at`, `is_deleted`: Audit fields

#### Document Model
- **Table**: `document_kbs`
- **Key Fields**:
  - `id`: UUID primary key
  - `kb_id`: Foreign key to knowledge base
  - `parent_id`: Self-referencing for hierarchical documents
  - `name`: Document name
  - `type`: URL | FILE
  - `url`: Source URL (for web documents)
  - `file_name`, `file_type`, `object_name`: File metadata
  - `embed_status`: PENDING | IN_PROGRESS | COMPLETED | FAILED
  - `deep_crawl`: Boolean for recursive website crawling

### API Endpoints

#### Core KB Management (`/api/v1/knowledge_base/`)

**Knowledge Base CRUD:**
- `POST /kbs` - Create knowledge base
- `GET /kbs` - List knowledge bases (with filtering)
- `GET /kbs/{kb_id}` - Get specific KB
- `PUT /kbs/{kb_id}` - Update KB
- `DELETE /kbs/{kb_id}` - Delete KB (soft delete + async cleanup)

**Document Management:**
- `POST /kbs/{kb_id}/presigned-urls` - Generate S3 upload URLs
- `POST /kbs/{kb_id}/confirm-uploads` - Confirm uploads & start ingestion
- `POST /kbs/{kb_id}/documents` - Add URLs for crawling
- `GET /kbs/{kb_id}/documents` - List documents in KB
- `DELETE /kbs/{kb_id}/documents/{doc_id}` - Delete document

**Utility Endpoints:**
- `GET /kbs/available-users` - Get workspace users for sharing
- `GET /kbs/point-usage` - Get user's point consumption
- `GET /tasks/{task_id}` - Get async task status

#### Search & Query (`/api/v1/knowledge_base_runtime/`)
- `POST /search` - Semantic search across KBs
- `POST /summarize` - Generate summaries from KB content

### Document Processing Pipeline

#### File Upload Flow
```mermaid
sequenceDiagram
    participant F as Frontend
    participant API as KB API
    participant S3 as Object Storage
    participant T as Celery Task
    participant E as Embedder
    participant Q as Qdrant

    F->>API: Request presigned URLs
    API->>S3: Generate upload URLs
    API->>F: Return presigned URLs
    F->>S3: Upload files directly
    F->>API: Confirm uploads
    API->>T: Start ingestion task
    T->>S3: Read uploaded files
    T->>E: Generate embeddings
    E->>Q: Store vectors
    T->>API: Update status
```

#### Website Crawling Flow
```mermaid
sequenceDiagram
    participant F as Frontend
    participant API as KB API
    participant T as Celery Task
    participant W as Web Crawler
    participant E as Embedder
    participant Q as Qdrant
    participant S3 as Object Storage

    F->>API: Submit URLs for crawling
    API->>T: Start crawl task
    T->>W: Crawl websites
    W->>W: Extract content & links
    W->>S3: Store raw content
    T->>E: Generate embeddings
    E->>Q: Store vectors
    T->>API: Update status
```

### Vector Search Architecture

#### Qdrant Configuration
- **Collection Model**: Single collection (`cloudthinker-collections`) for all workspaces and KBs
- **Workspace Isolation**: Achieved via `workspace_id` metadata field (UUID, indexed)
- **KB Isolation**: Achieved via `kb_id` metadata field (UUID, indexed)
- **Vector Types**:
  - Dense vectors: 1024 dimensions (Cohere Multilingual v3)
  - Sparse vectors: BM25 for keyword matching
- **Distance Metric**: Cosine similarity
- **Indexing**: HNSW with disk storage optimization, plus payload indexes for `workspace_id`, `kb_id`, and `role`
- **Filtering**: All queries and mutations use `workspace_id` and `kb_id` filters for strict isolation
- **Cache Preloading**: The vector store for the default collection is preloaded into the async LRU cache at application startup for optimal first-request performance

#### Search Process
1. **Query Embedding**: Convert search query to 1024-dim vector
2. **Hybrid Search**: Combine dense + sparse vector search
3. **Filtering**: Apply `workspace_id` and `kb_id` filters, plus score thresholds
4. **Ranking**: Score-based result ordering
5. **Synthesis**: LLM-based response generation with citations

> **Note:** This replaces the previous "one collection per workspace" model. All data is now stored in a single Qdrant collection, with strict logical isolation and efficient filtering via indexed metadata fields.

### Background Task Processing

#### Celery Tasks
- **File Ingestion** (`ingest_from_files_task`):
  - Priority: HIGH_PRIORITY
  - Retry: 3 attempts with exponential backoff
  - Progress tracking: 0% → 30% → 90% → 100%

- **Website Ingestion** (`ingest_from_website_task`):
  - Supports deep crawling (max depth: 2, max pages: 20)
  - Hierarchical document storage (parent-child relationships)
  - Content extraction and link discovery

#### Document Readers

**File Reader** (`file_reader.py`):
- Supported formats: PDF, DOCX, TXT, MD, CSV, JSON
- LlamaParse integration for complex documents
- Error handling and file validation

**Website Reader** (`website_reader.py`):
- Recursive crawling with depth limits
- Content extraction using BeautifulSoup
- Link discovery and filtering
- Sitemap.xml parsing
- Rate limiting and politeness delays

### Point Limit System
- Usage tracking per user/workspace
- Pre-ingestion validation
- Automatic task failure for exceeded limits
- Real-time usage reporting

## Frontend Implementation

### Component Architecture

#### Main Pages
- **KB List Page** (`/kb`): Browse and manage knowledge bases
- **KB Detail Page** (`/kb/[kb_id]`): Manage individual KB and documents

#### Key Components

**Core KB Components:**
- `KBPageWrapper`: Main container with filtering and search
- `KBDataTable`: Sortable table with actions
- `KBDetailContent`: Individual KB management interface
- `KBDocumentList`: Document listing with upload/crawl capabilities

**Specialized Components:**
- `KBDocumentUpload`: File upload with drag-drop and progress tracking
- `UserSelector`: Multi-select for sharing permissions
- `AccessLevelSelector`: Privacy level configuration
- `UsageModeSelector`: Agent interaction mode selection
- `KBTaskStatus`: Real-time ingestion progress monitoring

**Utility Components:**
- `FileTypeIcon`: Visual file type indicators
- `KBTypeBadge`: Access level and usage mode badges
- `SimplePDFViewer`: Inline document preview

### State Management

#### Hooks and Data Fetching
- `useKBs`: Knowledge base listing with pagination and filtering
- `useKBById`: Individual KB data retrieval
- `useKBDocuments`: Document listing for specific KB
- `useTaskStatus`: Real-time task progress monitoring

#### Filtering and Search
- **Search**: Text-based search across KB titles and descriptions
- **Access Level Filter**: Private vs Shared KBs
- **Usage Mode Filter**: Manual, Always, Agent-Requested
- **Pagination**: Server-side pagination with configurable limits

### User Interface Features

#### Knowledge Base Management
- **Creation**: Modal-based creation with configuration options
- **Editing**: Inline editing of KB properties
- **Sharing**: User selection for shared access
- **Deletion**: Confirmation dialog with cascading cleanup

#### Document Operations
- **File Upload**:
  - Drag-and-drop interface
  - Multiple file selection
  - Progress tracking with real-time updates
  - File type validation and size limits

- **URL Crawling**:
  - URL validation and formatting
  - Deep crawl option selection
  - Bulk URL submission
  - Crawl progress monitoring

#### Search and Discovery
- **Quick Search**: Instant filtering of KB list
- **Advanced Filters**: Multi-criteria filtering
- **Document Preview**: Inline viewing capabilities
- **Status Indicators**: Visual status representation

## Integration Points

### Agent Integration
- **Usage Modes**:
  - `MANUAL`: Requires explicit user mention
  - `ALWAYS`: Automatically included in all searches
  - `AGENT_REQUESTED`: Used when agents determine relevance

### Authentication & Authorization
- **Workspace Isolation**: KBs scoped to workspaces
- **User Permissions**: Owner and shared user access
- **API Security**: JWT-based authentication on all endpoints

### External Services
- **AWS Bedrock**: Embedding generation and LLM inference
- **Object Storage**: MinIO/S3 for raw document storage
- **Vector Database**: Qdrant for semantic search
- **Task Queue**: Redis-backed Celery for async processing

## Configuration

### Environment Variables
```bash
# Knowledge Base Settings
KB_CHUNK_SIZE=1024
KB_CHUNK_OVERLAP=256
KB_SEARCH_LIMIT=10
KB_SEARCH_SCORE_THRESHOLD=0.5
KB_BUCKET=knowledge-base-docs

# Embedding Configuration
EMBEDDING_MODEL_NAME=cohere.embed-multilingual-v3
EMBEDDING_MODEL_DIMS=1024
EMBEDDING_REGION_NAME=ap-southeast-1

# Crawling Limits
MAX_DEPTH=2
MAX_PAGES=20

# FastEmbed Sparse Models
FASTEMBED_SPARSE_MODEL_NAME=Qdrant/bm25
FASTEMBED_BATCH_SIZE=8

# Vector Database
QDRANT_HOST=qdrant:6333
QDRANT_GRPC_PORT=6334
```

## Performance Considerations

### Optimization Strategies
- **Concurrent Processing**: Parallel document ingestion
- **Hybrid Search**: Dense + sparse vector combination
- **Connection Pooling**: Database and vector store connections
- **Caching**: Embedding model caching with FastEmbed
- **Chunking**: Optimized chunk size and overlap for retrieval

### Scalability Features
- **Workspace Isolation**: Separate vector collections
- **Async Processing**: Non-blocking ingestion pipeline
- **Point Limits**: Resource consumption controls
- **Progressive Loading**: Paginated document lists
- **Direct S3 Upload**: Bypassed backend for large files

## Error Handling & Monitoring

### Error Recovery
- **Automatic Retries**: Exponential backoff for failed tasks
- **Graceful Degradation**: Partial success handling
- **Point Limit Enforcement**: Pre-emptive validation
- **Status Tracking**: Real-time progress and error reporting

### Monitoring Capabilities
- **Task Progress**: Real-time status updates
- **Usage Tracking**: Point consumption monitoring
- **Error Logging**: Structured error reporting
- **Performance Metrics**: Search and ingestion timing

## Security Considerations

### Data Protection
- **Access Control**: Workspace and user-level permissions
- **Soft Deletion**: Recoverable deletion with archive
- **Audit Trail**: Created/updated timestamps and user tracking
- **Input Validation**: URL and file type validation

### Privacy Features
- **Private KBs**: Owner-only access by default
- **Shared Access**: Explicit user permission grants
- **Data Isolation**: Workspace-scoped collections
- **Secure Upload**: Presigned URL-based file uploads

## Future Enhancements

### Planned Improvements
- **Enhanced Reranking**: Cohere rerank v3.5 integration
- **Advanced Chunking**: Semantic splitting strategies
- **Multi-modal Support**: Image and video processing
- **Collaboration Features**: Real-time collaborative editing
- **Analytics Dashboard**: Usage and performance insights
- **API Rate Limiting**: Request throttling and quotas
- **Backup & Recovery**: Automated data backup strategies

This Knowledge Base feature represents a sophisticated document management and search system that combines modern AI capabilities with robust engineering practices to deliver a scalable, user-friendly solution for knowledge management in cloud cost optimization workflows.
