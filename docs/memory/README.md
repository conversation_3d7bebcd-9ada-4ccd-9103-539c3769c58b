# Memory System Overview

The memory subsystem provides persistent, agent-centric long-term memory for storing and retrieving key learnings, execution patterns, and solutions from agent conversations. It is designed to help agents accumulate knowledge over time, optimize future actions, and provide historical context for decision-making.

## What is Stored in Memory?

A **Memory Node** represents a distilled learning or execution pattern extracted from agent conversations. Each node contains:

- **tags**: List of tags categorizing the memory (e.g., topics, cloud services)
- **task**: The specific problem, error, or question addressed
- **solution**: The detailed solution or approach that resolved the task
- **links**: References to related memory nodes (by ID)

See [`backend/app/services/memory/schema.py`](../../backend/app/services/memory/schema.py) for full schema details.

## Memory Extraction & Evolution

- **Extraction**: After agent conversations, the system uses LLMs to decide if substantive learnings should be extracted (see `EXTRACTION_DECISION_PROMPT`).
- **Memory Node Creation**: If extraction is warranted, the system synthesizes a Memory Node from the conversation, focusing on actionable, agent-executable patterns (see `EXTRACTION_PROMPT`).
- **Evolution**: Related memory nodes may be combined, connected, or left standalone based on their relationships and content, using the `MemoryEvolution` model.

## Storage & Retrieval

- **Vector Store**: Memories are stored in a Qdrant vector database, indexed by agent role and workspace for efficient retrieval.
- **Embeddings**: Text embeddings are generated for each memory node to enable semantic search.
- **API Endpoints**: The backend exposes endpoints to:
  - Retrieve memories by agent role and workspace (`POST /api/v1/memory/`)
  - Delete or update memories (`DELETE`/`PUT /api/v1/memory/`)
- **Agent Tool**: Agents can search their long-term memory using the `search_memory` tool, retrieving relevant past learnings for the current context.

## Key Components

- [`MemoryService`](../../backend/app/services/memory/memory_service.py): Core logic for extraction, evolution, storage, and search.
- [`MemoryNode`](../../backend/app/services/memory/schema.py): Data model for memory entries.
- [`memory.py` (API)](../../backend/app/api/routes/memory.py): FastAPI routes for memory operations.
- [`memory.py` (Agent Tool)](../../backend/app/modules/multi_agents/tools/builtin/memory.py): Tool interface for agent memory search.
- [`base_vector_store.py`](../../backend/app/services/base_vector_store.py): Vector store abstraction (Qdrant backend).

## Configuration

Memory system behavior is controlled via environment variables and settings in [`core/config.py`](../../backend/app/core/config.py):

- `QDRANT_HOST`, `QDRANT_PORT`, `QDRANT_COLLECTION_NAME`: Qdrant vector DB settings
- `EMBEDDING_MODEL_NAME`, `EMBEDDING_REGION_NAME`: Embedding model for vectorization
- `MEMORY_EXTRACTION_MODEL`, `MEMORY_MAX_RELATED_MEMORY_NODES`, `MEMORY_RELATEDNESS_THRESHOLD`: Extraction and search parameters

## Example Memory Node

```json
{
  "tags": ["aws", "ec2", "optimization"],
  "task": "EC2 instance optimization analysis",
  "solution": "1. get_ec2_inventory with params {include_metrics: true, period: '7d'}...",
  "links": ["node_id_1", "node_id_2"]
}
```

## Typical Workflow

1. **Conversation occurs** between agent and user or system.
2. **Extraction decision**: LLM determines if learnings should be extracted.
3. **Memory node creation**: Key learnings are distilled and embedded.
4. **Storage**: Node is stored in Qdrant, indexed by agent role and workspace.
5. **Retrieval**: Agents or users can search memory via API or agent tool.
6. **Evolution**: Over time, related nodes may be combined or linked for better knowledge organization.

## References
- [MemoryService](../../backend/app/services/memory/memory_service.py)
- [MemoryNode schema](../../backend/app/services/memory/schema.py)
- [API routes](../../backend/app/api/routes/memory.py)
- [Agent memory tool](../../backend/app/modules/multi_agents/tools/builtin/memory.py)
- [Base vector store](../../backend/app/services/base_vector_store.py)
- [Configuration](../../backend/app/core/config.py)
