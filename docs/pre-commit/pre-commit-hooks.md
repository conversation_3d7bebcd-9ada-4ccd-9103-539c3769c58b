# Pre-commit Hooks in CloudThinker

This document describes the pre-commit hooks used in the CloudThinker project to ensure code quality, security, and best practices.

## Setup

1. Install pre-commit:
   ```bash
   pip install pre-commit
   ```

2. Install Hadolint for Docker linting:
   ```bash
   ./scripts/development/install-hadolint.sh
   ```

3. Install the git hooks:
   ```bash
   pre-commit install
   ```

## Available Hooks

### Code Quality

- **Formatting Checks**
  - Python: Ruff for formatting and linting
  - JavaScript/TypeScript: ESLint and Prettier
  - YAML/JSON: Basic syntax checks
  - End of file fixing and trailing whitespace removal

- **Basic Checks**
  - Check for merge conflicts
  - Check for case conflicts
  - Check for large files (limit: 3MB)
  - Check for mixed line endings

### Security

- **Secret Detection**
  - Detect-secrets: Scans for potential secrets and credentials
  - Custom script: Additional secret detection

- **Security Scanning**
  - Bandit: Python security vulnerability scanning
  - Safety: Python dependency vulnerability checking
  - npm audit: JavaScript dependency vulnerability checking

- **Infrastructure Security**
  - Checkov: Scans infrastructure code for misconfigurations

- **Docker Security**
  - Hadolint: Docker security best practices and linting

### Testing

- **Unit Test Verification**
  - Custom script: Runs critical unit tests for modified components

### Bypassing Hooks

In some cases, you may need to bypass the pre-commit hooks (not recommended for regular use):

```bash
git commit --no-verify -m "Your commit message"
```

## Adding New Hooks

To add a new hook:

1. Update the `.pre-commit-config.yaml` file
2. Run `pre-commit autoupdate` to update hook versions
3. Test the new hook with `pre-commit run --all-files`

## Troubleshooting

If you encounter issues with pre-commit hooks:

1. Ensure you have the latest version of pre-commit installed
2. Run `pre-commit clean` to clean up cached repositories
3. Run `pre-commit autoupdate` to update hooks to their latest versions
4. Check for any missing dependencies required by specific hooks
