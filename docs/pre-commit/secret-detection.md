# Secret Detection in <PERSON>Thinker

This document explains how CloudThinker prevents accidental commits of secrets and sensitive information to the repository.

## Pre-commit Hooks

We use [pre-commit](https://pre-commit.com/) with the [detect-secrets](https://github.com/Yelp/detect-secrets) plugin to scan code for potential secrets before they are committed.

### Setup

1. Install pre-commit:
   ```bash
   pip install pre-commit
   ```

2. Install detect-secrets:
   ```bash
   pip install detect-secrets
   ```

3. Install the git hooks:
   ```bash
   pre-commit install
   ```

### How It Works

When you attempt to commit changes, the pre-commit hook will:
1. Scan all modified files for potential secrets
2. Compare findings against the baseline file (`.secrets.baseline`)
3. Block the commit if new secrets are detected

### Managing False Positives

The project includes a baseline file (`.secrets.baseline`) that contains known false positives or acceptable secrets (like test credentials). This prevents the hook from repeatedly flagging the same known patterns.

If the hook detects something that is not actually a secret:

1. Update the baseline file:
   ```bash
   detect-secrets scan --baseline .secrets.baseline
   ```

2. Review the changes to the baseline file and commit it

### Updating the Baseline

The baseline should be updated periodically, especially when:
- New developers join the team
- The codebase undergoes significant changes
- You encounter too many false positives

To update the baseline:

```bash
detect-secrets scan --baseline .secrets.baseline
git add .secrets.baseline
git commit -m "Update secrets baseline"
```

### Best Practices

- **Never** commit API keys, passwords, tokens, or other secrets to the repository
- Use environment variables for sensitive values
- Store secrets in a secure vault or secrets manager
- For development, use `.env` files (which are gitignored)
- Consider using AWS Secrets Manager or similar services for production secrets

### What is Detected

The tool can detect various types of secrets, including:
- API keys
- AWS access keys
- Private keys
- Authentication tokens
- Database connection strings
- Passwords

## Manual Checks

In addition to automated checks, please manually review your code before committing to ensure no secrets are included.

## Handling Secrets in CI/CD

For CI/CD pipelines, use:
- Environment variables for GitHub Actions
- AWS Secrets Manager for AWS deployments
- Secure environment variables in your CI/CD platform

## What to Do If You Accidentally Commit a Secret

If you accidentally commit a secret:

1. Immediately revoke/rotate the secret
2. Contact the security team
3. Do NOT try to remove it from git history yourself (this requires special handling)

Remember: Once a secret is committed, it should be considered compromised, even if you delete it later.
