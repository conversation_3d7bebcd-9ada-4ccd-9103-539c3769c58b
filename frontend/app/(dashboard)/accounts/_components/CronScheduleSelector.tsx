import React from 'react';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';
import cronstrue from 'cronstrue';
import { Calendar, Clock, RotateCcw, Settings, Zap } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';

interface CronScheduleSelectorProps {
  form: UseFormReturn<any>;
  name: string;
  cronPatternList: string[];
  label?: React.ReactNode;
  customOptionName: string;
}

// Icon mapping for different schedule types
const getScheduleIcon = (cronPattern: string) => {
  if (cronPattern.includes('30')) return <Zap className="h-5 w-5" />; // Every 30 minutes
  if (cronPattern.includes('0 *')) return <RotateCcw className="h-5 w-5" />; // Every hour
  if (cronPattern.includes('0 0')) return <Calendar className="h-5 w-5" />; // Daily
  if (cronPattern.includes('0 12')) return <Clock className="h-5 w-5" />; // At 12:00
  return <Settings className="h-5 w-5" />; // Default
};

// Get color for different schedule types
const getScheduleColor = (cronPattern: string) => {
  if (cronPattern.includes('30')) return 'text-red-600'; // Frequent
  if (cronPattern.includes('0 *')) return 'text-orange-600'; // Hourly
  if (cronPattern.includes('0 0')) return 'text-green-600'; // Daily
  if (cronPattern.includes('0 12')) return 'text-blue-600'; // Scheduled
  return 'text-gray-600'; // Default
};

// Get description for schedule
const getScheduleDescription = (cronPattern: string) => {
  const descriptions: Record<string, string> = {
    '*/30 * * * *': 'High frequency monitoring - Resource intensive',
    '0 * * * *': 'Balanced resource usage - Recommended',
    '0 */2 * * *': 'Moderate monitoring - Good balance',
    '0 0 * * *': 'Light resource usage - Cost effective',
    '0 0 * * 0': 'Weekly comprehensive scan - Minimal cost',
    '0 12 * * *': 'Daily at noon - Business hours',
    '0 0 1 * *': 'Monthly report - Long term trends',
  };

  return descriptions[cronPattern] || 'Custom schedule';
};

// Get frequency badge for schedule
const getFrequencyBadge = (cronPattern: string) => {
  if (cronPattern.includes('*/30') || cronPattern.includes('*/15'))
    return 'Very High';
  if (cronPattern.includes('0 *') && !cronPattern.includes('*/2'))
    return 'High';
  if (cronPattern.includes('*/2') || cronPattern.includes('*/4'))
    return 'Medium';
  if (cronPattern.includes('0 0') && !cronPattern.includes('0 0 *'))
    return 'Low';
  if (cronPattern.includes('0 0 *') || cronPattern.includes('* * 0'))
    return 'Very Low';
  return 'Custom';
};

// Get frequency color
const getFrequencyColor = (frequency: string) => {
  switch (frequency) {
    case 'Very High':
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
    case 'High':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
    case 'Medium':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
    case 'Low':
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
    case 'Very Low':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
    default:
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
  }
};

const safeParseCronPattern = (pattern: string) => {
  try {
    return cronstrue.toString(pattern);
  } catch (error) {
    return '';
  }
};

export const CronScheduleSelector: React.FC<CronScheduleSelectorProps> = ({
  form,
  name,
  cronPatternList,
  label,
  customOptionName,
}) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className="space-y-4">
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <RadioGroup
              onValueChange={field.onChange}
              value={field.value}
              className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3"
            >
              {cronPatternList.map((cronPattern) => {
                const scheduleIcon = getScheduleIcon(cronPattern);
                const scheduleColor = getScheduleColor(cronPattern);
                const scheduleDescription = getScheduleDescription(cronPattern);
                const humanReadable = safeParseCronPattern(cronPattern);
                const frequency = getFrequencyBadge(cronPattern);
                const frequencyColor = getFrequencyColor(frequency);

                return (
                  <FormItem
                    key={cronPattern}
                    className={cn(
                      'relative flex cursor-pointer flex-col space-y-0 rounded-3xl border transition-all hover:scale-[1.02] hover:shadow-md',
                      field.value === cronPattern
                        ? 'border-primary bg-primary/5 shadow-md ring-2 ring-primary/20'
                        : 'border-border hover:border-primary/50',
                    )}
                  >
                    <FormControl>
                      <RadioGroupItem value={cronPattern} className="sr-only" />
                    </FormControl>
                    <FormLabel
                      className={cn(
                        'flex h-full w-full cursor-pointer items-start gap-3 p-4',
                        field.value === cronPattern
                          ? 'text-primary'
                          : 'text-foreground',
                      )}
                    >
                      <div className={cn('mt-1', scheduleColor)}>
                        {scheduleIcon}
                      </div>
                      <div className="min-w-0 flex-1 space-y-2">
                        <div className="flex items-center gap-2">
                          <code className="rounded bg-muted px-2 py-1 font-mono text-xs">
                            {cronPattern}
                          </code>
                          <span
                            className={cn(
                              'rounded-full px-2 py-1 text-xs font-medium',
                              frequencyColor,
                            )}
                          >
                            {frequency}
                          </span>
                        </div>
                        {humanReadable && (
                          <div className="rounded-lg bg-muted/50 p-2">
                            <p className="text-sm font-medium text-foreground">
                              {humanReadable}
                            </p>
                          </div>
                        )}
                        <p className="text-xs text-muted-foreground">
                          {scheduleDescription}
                        </p>
                      </div>
                      {field.value === cronPattern && (
                        <div className="absolute right-3 top-3">
                          <div className="h-3 w-3 rounded-full bg-primary ring-2 ring-primary/20"></div>
                        </div>
                      )}
                    </FormLabel>
                  </FormItem>
                );
              })}

              {/* Custom Option */}
              <FormItem
                className={cn(
                  'relative flex cursor-pointer flex-col space-y-0 rounded-3xl border transition-all hover:scale-[1.02] hover:shadow-md',
                  field.value === customOptionName
                    ? 'border-primary bg-primary/5 shadow-md ring-2 ring-primary/20'
                    : 'border-dashed border-border hover:border-primary/50',
                )}
              >
                <FormControl>
                  <RadioGroupItem
                    value={customOptionName}
                    className="sr-only"
                  />
                </FormControl>
                <FormLabel
                  className={cn(
                    'flex h-full w-full cursor-pointer items-start gap-3 p-4',
                    field.value === customOptionName
                      ? 'text-primary'
                      : 'text-foreground',
                  )}
                >
                  <div className="mt-1 text-purple-600">
                    <Settings className="h-5 w-5" />
                  </div>
                  <div className="min-w-0 flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">
                        {customOptionName}
                      </span>
                      <span className="rounded-full bg-purple-100 px-2 py-1 text-xs font-medium text-purple-800 dark:bg-purple-900/20 dark:text-purple-400">
                        Custom
                      </span>
                    </div>
                    <div className="rounded-lg bg-muted/50 p-2">
                      <p className="text-sm text-muted-foreground">
                        Create your own schedule pattern
                      </p>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Use the cron builder to define a custom schedule that fits
                      your specific needs
                    </p>
                  </div>
                  {field.value === customOptionName && (
                    <div className="absolute right-3 top-3">
                      <div className="h-3 w-3 rounded-full bg-primary ring-2 ring-primary/20"></div>
                    </div>
                  )}
                </FormLabel>
              </FormItem>
            </RadioGroup>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
