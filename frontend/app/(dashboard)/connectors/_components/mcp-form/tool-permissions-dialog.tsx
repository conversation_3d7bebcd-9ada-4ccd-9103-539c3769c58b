import { useEffect, useState } from 'react';

import { MCPServerResponseSchema } from '@/client/types.gen';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import { Search } from 'lucide-react';

interface ToolPermissionsDialogProps {
  open: boolean;
  onClose: () => void;
  server: MCPServerResponseSchema;
  onSave: (toolPermissions: string[]) => Promise<void>;
}

export default function ToolPermissionsDialog({
  open,
  onClose,
  server,
  onSave,
}: ToolPermissionsDialogProps) {
  const [permissionRequiredTools, setPermissionRequiredTools] = useState<
    Set<string>
  >(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Initialize permissions when dialog opens
  useEffect(() => {
    if (open && server.tool_list) {
      // Initialize with current tool_permissions if they exist
      const initialPermissions = new Set(server.tool_permissions || []);
      setPermissionRequiredTools(initialPermissions);
    }
  }, [open, server.tool_list, server.tool_permissions]);

  const filteredTools =
    server.tool_list?.filter((tool) =>
      tool.toLowerCase().includes(searchTerm.toLowerCase()),
    ) || [];

  const toggleToolPermission = (toolName: string) => {
    setPermissionRequiredTools((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(toolName)) {
        newSet.delete(toolName);
      } else {
        newSet.add(toolName);
      }
      return newSet;
    });
  };

  const toggleAllPermissions = () => {
    const allTools = server.tool_list || [];
    const allHavePermission = allTools.every((tool) =>
      permissionRequiredTools.has(tool),
    );

    if (allHavePermission) {
      setPermissionRequiredTools(new Set());
    } else {
      setPermissionRequiredTools(new Set(allTools));
    }
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await onSave(Array.from(permissionRequiredTools));
      onClose();
    } catch (error) {
      console.error('Failed to save permissions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const enabledToolsCount =
    server.tool_list?.filter((tool) => server.tool_enabled?.includes(tool))
      .length || 0;

  const permissionRequiredCount = permissionRequiredTools.size;

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-hidden">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-lg font-semibold">
            Tool Permissions - {server.name}
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            Choose which tools require approval before execution.
          </p>
        </DialogHeader>

        <div className="space-y-4 overflow-hidden">
          {/* Search */}
          {server.tool_list && server.tool_list.length > 3 && (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground" />
              <Input
                placeholder="Search tools..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          )}

          {/* Tools List */}
          <div className="max-h-80 space-y-2 overflow-auto">
            {filteredTools.map((toolName) => {
              const cleanToolName = toolName.replace(
                server.prefix ? `${server.prefix}__` : '',
                '',
              );
              const isEnabled =
                server.tool_enabled?.includes(toolName) ?? false;
              const requiresPermission = permissionRequiredTools.has(toolName);

              return (
                <div
                  key={toolName}
                  className={cn(
                    'flex items-center justify-between rounded-lg border p-3',
                    'hover:bg-muted/50',
                    !isEnabled && 'opacity-60',
                    requiresPermission &&
                      'border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/20',
                  )}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={cn(
                        'h-2 w-2 rounded-full',
                        isEnabled
                          ? requiresPermission
                            ? 'bg-amber-500'
                            : 'bg-green-500'
                          : 'bg-muted-foreground/40',
                      )}
                    />
                    <div>
                      <div className="font-medium">{cleanToolName}</div>
                      <div className="text-xs text-muted-foreground">
                        {isEnabled ? 'Active' : 'Inactive'}
                        {requiresPermission && ' • Requires approval'}
                      </div>
                    </div>
                  </div>

                  <Switch
                    checked={requiresPermission}
                    onCheckedChange={() => toggleToolPermission(toolName)}
                    className={cn(
                      requiresPermission && 'data-[state=checked]:bg-amber-500',
                    )}
                  />
                </div>
              );
            })}
          </div>

          {filteredTools.length === 0 && searchTerm && (
            <div className="py-8 text-center text-muted-foreground">
              <p>No tools found matching "{searchTerm}"</p>
            </div>
          )}
        </div>

        <DialogFooter className="border-t pt-4">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? 'Saving...' : 'Save'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
