import {
  MCPServerCreateSchema,
  MCPServerResponseSchema,
  MCPServerUpdateSchema,
} from '@/client/types.gen';

export type {
  MCPServerResponseSchema,
  MCPServerCreateSchema,
  MCPServerUpdateSchema,
};

export interface ServerCardProps {
  server: MCPServerResponseSchema;
  onEdit?: (server: MCPServerResponseSchema) => void;
  onRemove?: (server: MCPServerResponseSchema) => void;
  onToggleActive?: (serverId: string, isActive: boolean) => Promise<void>;
  onToggleTool?: (
    serverId: string,
    toolName: string,
    isEnabled: boolean,
  ) => Promise<void>;
  onManagePermissions?: (server: MCPServerResponseSchema) => void;
  isEditMode?: boolean;
  onRefresh?: () => Promise<void>;
  isRefreshing?: boolean;
  relationships?: Array<{ agentId: string; agentName: string }>;
}

export interface ServerFormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (server: MCPServerCreateSchema | MCPServerUpdateSchema) => void;
  server: MCPServerResponseSchema | null;
  existingServerNames: string[];
}
