'use client';

import { ReportsService } from '@/client';
import { useQuery } from '@tanstack/react-query';

export function useServiceSavings(startDate?: string, endDate?: string) {
  return useQuery({
    queryKey: ['service-savings', { startDate, endDate }],
    queryFn: () =>
      ReportsService.getSavingsByService({
        ...(startDate && { startDate }),
        ...(endDate && { endDate }),
      }),
  });
}
