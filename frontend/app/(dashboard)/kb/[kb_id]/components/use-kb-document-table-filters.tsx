'use client';

import { useCallback, useMemo } from 'react';

import { searchParams } from '@/lib/searchparams';
import { useQueryState } from 'nuqs';

export function useKBDocumentTableFilters() {
  const [searchQuery, setSearchQuery] = useQueryState(
    'q',
    searchParams.q
      .withOptions({ shallow: false, throttleMs: 1000 })
      .withDefault(''),
  );

  const [page, setPage] = useQueryState(
    'page',
    searchParams.page.withOptions({ shallow: false }),
  );

  const resetFilters = useCallback(() => {
    setSearchQuery('');
    setPage(1);
  }, [setSearchQuery, setPage]);

  const isAnyFilterActive = useMemo(() => {
    return !!searchQuery && searchQuery.trim() !== '';
  }, [searchQuery]);

  return {
    searchQuery: searchQuery || '',
    setSearchQuery,
    page: page || 1,
    setPage,
    resetFilters,
    isAnyFilterActive,
  };
}
