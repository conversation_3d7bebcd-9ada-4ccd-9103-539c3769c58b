import React from 'react';

import { SyntaxHighlighter } from '@/components/ui/code-block';
import {
  Braces,
  Code,
  Cog,
  Cpu,
  Database,
  FileCode,
  FileJson,
  FileText,
  FileType,
  Globe,
  Hash,
  PenTool,
  Puzzle,
  Terminal,
} from 'lucide-react';
import ReactMarkdown, { Components } from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';

interface KBDocumentMarkdownRendererProps {
  content: string;
}

// Function to get language-specific icon
const getLanguageIcon = (language: string) => {
  switch (language.toLowerCase()) {
    case 'javascript':
    case 'js':
      return <FileJson className="h-3.5 w-3.5" />;
    case 'typescript':
    case 'ts':
      return <FileType className="h-3.5 w-3.5" />;
    case 'jsx':
    case 'tsx':
    case 'react':
      return <Puzzle className="h-3.5 w-3.5" />;
    case 'python':
    case 'py':
      return <Hash className="h-3.5 w-3.5" />;
    case 'java':
      return <Cpu className="h-3.5 w-3.5" />;
    case 'c':
    case 'cpp':
    case 'c++':
      return <Braces className="h-3.5 w-3.5" />;
    case 'csharp':
    case 'c#':
      return <Hash className="h-3.5 w-3.5" />;
    case 'php':
      return <Globe className="h-3.5 w-3.5" />;
    case 'ruby':
    case 'rb':
      return <PenTool className="h-3.5 w-3.5" />;
    case 'go':
      return <FileCode className="h-3.5 w-3.5" />;
    case 'html':
      return <Globe className="h-3.5 w-3.5" />;
    case 'css':
    case 'scss':
    case 'sass':
      return <PenTool className="h-3.5 w-3.5" />;
    case 'json':
      return <FileJson className="h-3.5 w-3.5" />;
    case 'sql':
      return <Database className="h-3.5 w-3.5" />;
    case 'bash':
    case 'sh':
    case 'shell':
      return <Terminal className="h-3.5 w-3.5" />;
    case 'yaml':
    case 'yml':
      return <FileText className="h-3.5 w-3.5" />;
    case 'dockerfile':
      return <Cog className="h-3.5 w-3.5" />;
    case 'markdown':
    case 'md':
      return <FileText className="h-3.5 w-3.5" />;
    default:
      return <Code className="h-3.5 w-3.5" />;
  }
};

// Inline Code Component
const InlineCodeRenderer = ({ children }: { children: React.ReactNode }) => {
  return (
    <code className="bg-muted/80 relative -top-px mx-0.5 inline rounded-md border px-1.5 py-0.5 align-baseline font-mono text-sm break-words">
      {children}
    </code>
  );
};

// Enhanced Code Block Component
const CodeBlockRenderer = ({
  className,
  children,
  inline,
}: {
  className?: string;
  children: React.ReactNode;
  inline?: boolean;
}) => {
  // Handle inline code - use dedicated inline component
  if (inline) {
    return <InlineCodeRenderer>{children}</InlineCodeRenderer>;
  }

  // Extract language from className
  const match = /language-(\w+)/.exec(className || '');
  const language = match ? match[1] : 'text';
  const codeString = String(children).replace(/\n$/, '');

  const languageIcon = getLanguageIcon(language);

  return (
    <div className="border-border/60 bg-card my-4 max-w-full overflow-hidden rounded-lg border shadow-xs">
      {/* Header */}
      <div className="border-border/60 bg-muted/50 flex items-center justify-between border-b px-3 py-2">
        <div className="flex min-w-0 flex-1 items-center gap-2">
          {languageIcon}
          <span className="text-muted-foreground truncate text-xs font-medium tracking-wide uppercase">
            {language}
          </span>
        </div>
      </div>

      {/* Code Content */}
      <div className="relative min-w-0">
        <SyntaxHighlighter
          language={language}
          className="text-sm"
          code={codeString}
          showCopyButton={true}
        />
      </div>
    </div>
  );
};

// Define custom components for the markdown renderer
const components: Components = {
  code(props) {
    const { className, children, inline, ...rest } = props as any;

    // More robust inline detection
    const childrenAsString = React.Children.toArray(children).join('');
    const hasLanguageClass = className && className.startsWith('language-');
    const hasNewlines =
      typeof childrenAsString === 'string' && childrenAsString.includes('\n');
    const isShortCode =
      typeof childrenAsString === 'string' && childrenAsString.length < 100;

    // Determine if this should be inline:
    // - Explicitly marked as inline
    // - No language class and no newlines (typical backtick code)
    // - Short code without language specification
    const isInline =
      inline ||
      (!hasLanguageClass && !hasNewlines) ||
      (!hasLanguageClass && isShortCode);

    return (
      <CodeBlockRenderer className={className} inline={isInline} {...rest}>
        {children}
      </CodeBlockRenderer>
    );
  },
  pre({ children, ...props }) {
    // Only render pre wrapper for actual code blocks, not inline code
    const isCodeBlock = React.Children.toArray(children).some(
      (child) =>
        React.isValidElement(child) &&
        // @ts-expect-error - className is not typed
        child.props?.className?.startsWith('language-'),
    );

    if (isCodeBlock) {
      return <>{children}</>;
    }

    // For other pre content, render normally
    return (
      <pre
        className="bg-muted/30 overflow-x-auto rounded-md border p-3 font-mono text-sm whitespace-pre-wrap"
        {...props}
      >
        {children}
      </pre>
    );
  },

  p({ children, ...props }) {
    return (
      <p className="text-foreground mb-4 leading-relaxed last:mb-0" {...props}>
        {children}
      </p>
    );
  },

  h1({ children, ...props }) {
    return (
      <h1
        className="border-border/30 text-foreground mt-6 mb-4 border-b pb-2 text-2xl font-bold first:mt-0"
        {...props}
      >
        {children}
      </h1>
    );
  },

  h2({ children, ...props }) {
    return (
      <h2
        className="text-foreground mt-5 mb-3 text-xl font-semibold first:mt-0"
        {...props}
      >
        {children}
      </h2>
    );
  },

  h3({ children, ...props }) {
    return (
      <h3
        className="text-foreground mt-4 mb-3 text-lg font-medium first:mt-0"
        {...props}
      >
        {children}
      </h3>
    );
  },

  h4({ children, ...props }) {
    return (
      <h4
        className="text-foreground mt-3 mb-2 text-base font-medium first:mt-0"
        {...props}
      >
        {children}
      </h4>
    );
  },

  h5({ children, ...props }) {
    return (
      <h5
        className="text-foreground mt-3 mb-2 text-sm font-medium first:mt-0"
        {...props}
      >
        {children}
      </h5>
    );
  },

  h6({ children, ...props }) {
    return (
      <h6
        className="text-muted-foreground mt-2 mb-2 text-sm font-medium first:mt-0"
        {...props}
      >
        {children}
      </h6>
    );
  },

  ul({ children, ...props }) {
    return (
      <ul className="mb-4 list-outside list-disc space-y-1 pl-6" {...props}>
        {children}
      </ul>
    );
  },

  ol({ children, ...props }) {
    return (
      <ol className="mb-4 list-outside list-decimal space-y-1 pl-6" {...props}>
        {children}
      </ol>
    );
  },

  li({ children, ...props }) {
    return (
      <li className="leading-relaxed" {...props}>
        {children}
      </li>
    );
  },

  blockquote({ children, ...props }) {
    return (
      <blockquote
        className="border-primary/30 bg-muted/30 text-muted-foreground my-4 rounded-r-md border-l-4 py-2 pl-4 italic"
        {...props}
      >
        {children}
      </blockquote>
    );
  },

  a({ href, children, ...props }) {
    return (
      <a
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className="text-primary hover:text-primary/80 underline decoration-1 underline-offset-2 transition-all hover:decoration-2"
        {...props}
      >
        {children}
      </a>
    );
  },

  table({ children, ...props }) {
    return (
      <div className="border-border/60 my-4 overflow-x-auto rounded-lg border">
        <table className="w-full border-collapse text-sm" {...props}>
          {children}
        </table>
      </div>
    );
  },

  thead({ children, ...props }) {
    return (
      <thead className="bg-muted/50" {...props}>
        {children}
      </thead>
    );
  },

  th({ children, ...props }) {
    return (
      <th
        className="border-border/60 border-b px-4 py-2 text-left font-medium"
        {...props}
      >
        {children}
      </th>
    );
  },

  td({ children, ...props }) {
    return (
      <td
        className="border-border/30 border-b px-4 py-2 last:border-b-0"
        {...props}
      >
        {children}
      </td>
    );
  },

  hr({ ...props }) {
    return <hr className="border-border/40 my-6" {...props} />;
  },

  img({ src, alt, ...props }) {
    return (
      <img
        src={src}
        alt={alt}
        className="border-border/30 my-4 h-auto max-w-full rounded-lg border"
        {...props}
      />
    );
  },

  strong({ children, ...props }) {
    return (
      <strong className="text-foreground font-semibold" {...props}>
        {children}
      </strong>
    );
  },

  em({ children, ...props }) {
    return (
      <em className="italic" {...props}>
        {children}
      </em>
    );
  },
};

export function KBDocumentMarkdownRenderer({
  content,
}: KBDocumentMarkdownRendererProps) {
  return (
    <div className="w-full max-w-full overflow-hidden">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={components}
        className="text-foreground break-words"
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}
