'use client';

import { useEffect, useMemo, useState } from 'react';

import Link from 'next/link';

import { AvailableUser, KBRead, KnowledgeBaseService } from '@/client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { DateFormat, formatDate } from '@/lib/date-utils';
import { Column, ColumnDef } from '@tanstack/react-table';
import {
  ArrowUpDown,
  Clock,
  Eye,
  FileText,
  Settings,
  Tag,
  Trash2,
  Users,
} from 'lucide-react';

import { KBTypeBadge } from './KBTypeBadge';

// Create a reusable SortableHeader component for consistent styling
const SortableHeader = ({
  column,
  title,
  align = 'left',
}: {
  column: Column<any, unknown>;
  title: string | React.ReactNode;
  align?: 'left' | 'center' | 'right';
}) => {
  return (
    <Button
      variant="ghost"
      className={`group flex items-center px-0 font-medium text-foreground hover:bg-transparent hover:text-primary ${
        align === 'center'
          ? 'w-full justify-center'
          : align === 'right'
            ? 'w-full justify-end'
            : ''
      }`}
      onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
    >
      <span className="whitespace-normal text-center group-hover:text-primary">
        {title}
      </span>
      <ArrowUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50 group-hover:text-primary group-hover:opacity-100" />
    </Button>
  );
};

// Create a custom badge for usage mode
function UsageModeBadge({ mode }: { mode: string }) {
  const getIcon = () => {
    switch (mode) {
      case 'manual':
        return <FileText size={16} className="mr-2" />;
      case 'agent_requested':
        return <Tag size={16} className="mr-2" />;
      case 'always':
        return <Clock size={16} className="mr-2" />;
      default:
        return null;
    }
  };

  return (
    <Badge variant="outline" className="p-2">
      {getIcon()}
      <span>{mode.replace('_', ' ')}</span>
    </Badge>
  );
}

// Function to truncate description
const truncateDescription = (
  desc: string | null | undefined,
  maxLength = 80,
) => {
  if (!desc) return '-';
  return desc.length > maxLength ? `${desc.substring(0, maxLength)}...` : desc;
};

// Component to display allowed users
function AllowedUsersCell({
  allowedUsers,
  availableUsers,
}: {
  allowedUsers?: string[];
  availableUsers: AvailableUser[];
}) {
  if (!allowedUsers || allowedUsers.length === 0) {
    return <span className="text-xs text-muted-foreground">Private</span>;
  }

  // Find user details for each allowed user ID
  const userDetails = allowedUsers
    .map((userId) => availableUsers.find((user) => user.id === userId))
    .filter(Boolean) as AvailableUser[];

  if (userDetails.length === 0) {
    return <span className="text-xs text-muted-foreground">-</span>;
  }

  return (
    <div className="max-w-[200px] space-y-1">
      {userDetails.length <= 2 ? (
        // Show all users if 2 or fewer
        userDetails.map((user) => (
          <div key={user.id} className="space-y-0.5">
            <div className="truncate text-sm font-medium text-foreground">
              {user.full_name || 'Unnamed User'}
            </div>
            <div className="truncate text-xs text-muted-foreground">
              {user.email}
            </div>
          </div>
        ))
      ) : (
        // Show first user and count for more than 2
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="cursor-pointer space-y-0.5">
                <div className="truncate text-sm font-medium text-foreground">
                  {userDetails[0].full_name || 'Unnamed User'}
                </div>
                <div className="truncate text-xs text-muted-foreground">
                  {userDetails[0].email}
                </div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Users className="h-3 w-3" />
                  <span>+{userDetails.length - 1} more</span>
                </div>
              </div>
            </TooltipTrigger>
            <TooltipContent side="right" className="max-w-[300px]">
              <div className="space-y-2">
                <div className="text-xs font-medium">
                  All Users ({userDetails.length})
                </div>
                {userDetails.map((user) => (
                  <div key={user.id} className="space-y-0.5">
                    <div className="text-sm font-medium">
                      {user.full_name || 'Unnamed User'}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {user.email}
                    </div>
                  </div>
                ))}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}

// Hook to fetch available users
export function useAvailableUsers(data: KBRead[]) {
  const [availableUsers, setAvailableUsers] = useState<AvailableUser[]>([]);

  // Memoize the check for shared KBs to prevent unnecessary re-runs
  const hasSharedKBs = useMemo(() => {
    return (
      data &&
      data.some(
        (kb) =>
          kb.access_level === 'shared' &&
          kb.allowed_users &&
          kb.allowed_users.length > 0,
      )
    );
  }, [data]);

  // Fetch available users only when there are shared KBs to display
  useEffect(() => {
    if (!hasSharedKBs) {
      setAvailableUsers([]);
      return;
    }

    const fetchUsers = async () => {
      try {
        const response = await KnowledgeBaseService.getAvailableUsers();
        if (response && response.data) {
          setAvailableUsers(response.data);
        }
      } catch (error) {
        console.error('❌ Error fetching available users:', error);
      }
    };

    fetchUsers();
  }, [hasSharedKBs]); // Use memoized value instead of data directly

  return availableUsers;
}

// Create the columns function that takes handlers as parameters
export function createKBColumns(
  onEdit: (kb: KBRead) => void,
  onDelete: (kb: KBRead) => void,
  availableUsers: AvailableUser[],
): ColumnDef<KBRead>[] {
  return [
    {
      accessorKey: 'title',
      header: 'Title & Description',
      size: 300,
      minSize: 250,
      cell: ({ row }) => {
        const kb = row.original;
        return (
          <div className="space-y-1">
            <div className="font-medium text-foreground">{kb.title}</div>
            <p className="line-clamp-2 text-xs text-muted-foreground">
              {truncateDescription(kb.description)}
            </p>
          </div>
        );
      },
    },
    {
      accessorKey: 'access_level',
      header: () => <div className="text-center">Access Level</div>,
      size: 120,
      cell: ({ row }) => (
        <div className="flex justify-center">
          <KBTypeBadge type={row.original.access_level} />
        </div>
      ),
    },
    {
      accessorKey: 'allowed_users',
      header: 'Shared With',
      size: 200,
      minSize: 180,
      cell: ({ row }) => {
        const kb = row.original;
        // Only show allowed users for shared KBs
        if (kb.access_level === 'private') {
          return <span className="text-xs text-muted-foreground">Private</span>;
        }
        return (
          <AllowedUsersCell
            allowedUsers={kb.allowed_users}
            availableUsers={availableUsers}
          />
        );
      },
    },
    {
      accessorKey: 'usage_mode',
      header: () => <div className="text-center">Usage Mode</div>,
      size: 140,
      cell: ({ row }) => (
        <div className="flex justify-center capitalize">
          <UsageModeBadge mode={row.original.usage_mode} />
        </div>
      ),
    },
    {
      accessorKey: 'tags',
      header: 'Tags',
      size: 150,
      cell: ({ row }) => {
        const kb = row.original;
        return (
          <div className="flex max-w-[150px] flex-wrap gap-1">
            {kb.tags && kb.tags.length > 0 ? (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex flex-wrap gap-1">
                      {kb.tags.slice(0, 2).map((tag) => (
                        <Badge
                          key={tag}
                          variant="outline"
                          className="px-1.5 py-0 text-xs"
                        >
                          {tag}
                        </Badge>
                      ))}
                      {kb.tags.length > 2 && (
                        <Badge
                          variant="outline"
                          className="px-1.5 py-0 text-xs"
                        >
                          +{kb.tags.length - 2}
                        </Badge>
                      )}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="flex max-w-[200px] flex-wrap gap-1">
                      {kb.tags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="outline"
                          className="px-1.5 py-0 text-xs"
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : (
              <span className="text-xs text-muted-foreground">-</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'created_at',
      header: ({ column }) => (
        <SortableHeader column={column} title="Created" />
      ),
      size: 120,
      cell: ({ row }) => (
        <span className="text-sm text-muted-foreground">
          {formatDate(row.original.created_at, DateFormat.SHORT_DATE)}
        </span>
      ),
      enableSorting: true,
      sortingFn: 'datetime',
    },
    {
      id: 'actions',
      size: 180,
      cell: ({ row }) => {
        const kb = row.original;
        return (
          <div className="flex items-center justify-end gap-2">
            <Button variant="ghost" size="sm" asChild>
              <Link href={`/kb/${kb.id}`}>
                <Eye className="mr-2 h-4 w-4" />
                View
              </Link>
            </Button>

            <Button variant="ghost" size="sm" onClick={() => onEdit(kb)}>
              <Settings className="mr-2 h-4 w-4" />
              Edit
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="text-destructive hover:bg-destructive/10 hover:text-destructive"
              onClick={() => onDelete(kb)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        );
      },
    },
  ];
}

// Default export that uses empty handlers (for the listing page)
export const columns = createKBColumns(
  () => {},
  () => {},
  [],
);
