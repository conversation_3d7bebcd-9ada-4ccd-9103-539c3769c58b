'use client';

import { useEffect } from 'react';

import { KBRead, KnowledgeBaseService } from '@/client';
import { DataTable } from '@/components/ui/table/data-table';
import { DataTableFilterBox } from '@/components/ui/table/data-table-filter-box';
import { DataTableResetFilter } from '@/components/ui/table/data-table-reset-filter';
import { DataTableSearch } from '@/components/ui/table/data-table-search';
import { CacheKey } from '@/components/utils/cache-key';
import { useQuery, useQueryClient } from '@tanstack/react-query';

import { createKBColumns, useAvailableUsers } from './kb-data-table-columns';
import { useKBTableFilters } from './use-kb-table-filters';

// Define filter options
const accessLevelOptions = [
  { value: 'private', label: 'Private' },
  { value: 'shared', label: 'Shared' },
];

const usageModeOptions = [
  { value: 'manual', label: 'Manual' },
  // { value: 'agent_requested', label: 'Agent Requested' },
  { value: 'always', label: 'Always' },
];

type KBListingPageProps = Readonly<{
  page?: number;
  limit?: number;
  q?: string | null;
  access_level?: string | null;
  usage_mode?: string | null;
  onEdit?: (kb: KBRead) => void;
  onDelete?: (kb: KBRead) => void;
}>;

function getKBsQueryOptions({
  page,
  limit,
  q,
  access_level,
  usage_mode,
}: {
  page: number;
  limit: number;
  q: string | null;
  access_level: string | null;
  usage_mode: string | null;
}) {
  return {
    queryFn: () =>
      KnowledgeBaseService.getKbs({
        skip: (page - 1) * limit,
        limit,
        ...(q && { search: q }),
        ...(access_level && { accessLevel: access_level }),
        ...(usage_mode && { usageMode: usage_mode }),
      }),
    queryKey: [
      CacheKey.KnowledgeBase,
      page,
      limit,
      q,
      access_level,
      usage_mode,
    ],
  };
}

export default function KBListingPage({
  page = 1,
  limit = 10,
  q = null,
  access_level = null,
  usage_mode = null,
  onEdit = () => {},
  onDelete = () => {},
}: KBListingPageProps) {
  const {
    isAnyFilterActive,
    resetFilters,
    searchQuery,
    setPage,
    setSearchQuery,
    accessLevelFilter,
    setAccessLevelFilter,
    usageModeFilter,
    setUsageModeFilter,
  } = useKBTableFilters();

  const queryClient = useQueryClient();

  const {
    data: items,
    isPending,
    isPlaceholderData,
  } = useQuery({
    ...getKBsQueryOptions({
      page,
      limit,
      q,
      access_level,
      usage_mode,
    }),
    placeholderData: (prevData) => prevData,
  });

  const hasNextPage = !isPlaceholderData && items?.data.length === limit;

  // Fetch available users for shared KBs with memoized data
  const availableUsers = useAvailableUsers(items?.data || []);

  // Create columns with handlers
  const columns = createKBColumns(onEdit, onDelete, availableUsers);

  useEffect(() => {
    if (hasNextPage) {
      queryClient.prefetchQuery(
        getKBsQueryOptions({
          page: page + 1,
          limit,
          q,
          access_level,
          usage_mode,
        }),
      );
    }
  }, [page, queryClient, hasNextPage, limit, q, access_level, usage_mode]);

  return (
    <div className="flex h-full flex-col">
      <div className="mb-4 flex-none space-y-4">
        <div className="flex flex-wrap items-center gap-4">
          <DataTableSearch
            searchKey="knowledge bases"
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            setPage={setPage}
          />
          <DataTableFilterBox
            filterKey="access_level"
            title="Access Level"
            options={accessLevelOptions}
            setFilterValue={setAccessLevelFilter}
            filterValue={accessLevelFilter}
          />
          <DataTableFilterBox
            filterKey="usage_mode"
            title="Usage Mode"
            options={usageModeOptions}
            setFilterValue={setUsageModeFilter}
            filterValue={usageModeFilter}
          />
          <DataTableResetFilter
            isFilterActive={isAnyFilterActive}
            onReset={resetFilters}
          />
        </div>
      </div>

      <div className="min-h-0 flex-1">
        <DataTable
          columns={columns}
          data={items?.data ?? []}
          totalItems={items?.count ?? 0}
          loading={isPending}
          customScrollClass="grid h-[calc(100vh-450px)] rounded-xl border"
          enableSorting={true}
          defaultSorting={[{ id: 'created_at', desc: true }]}
          currentPage={page}
          pageSize={limit}
          onPageChange={setPage}
          displayPagination={true}
        />
      </div>
    </div>
  );
}
