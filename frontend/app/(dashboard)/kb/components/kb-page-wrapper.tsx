'use client';

import { use<PERSON><PERSON>back, useMemo, useState } from 'react';

import { K<PERSON>ead, KnowledgeBaseService } from '@/client';
import { PageHeader } from '@/components/layout/page-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { CacheKey } from '@/components/utils/cache-key';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus } from 'lucide-react';
import { toast } from 'sonner';

import { KBCreateDialog } from './dialogs/kb-create-dialog';
import KBListingPage from './kb-listing';
import { KBStatusSummary } from './kb-status-summary';

interface KBPageWrapperProps {
  page?: number;
  limit?: number;
  q?: string;
  access_level?: string;
  usage_mode?: string;
}

type KBAccessLevel = 'private' | 'shared';
type KBUsageMode = 'manual' | 'agent_requested' | 'always';

export function KBPageWrapper({
  page,
  limit,
  q,
  access_level,
  usage_mode,
}: KBPageWrapperProps) {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editingKB, setEditingKB] = useState<KBRead | null>(null);
  const [deletingKB, setDeletingKB] = useState<KBRead | null>(null);
  const queryClient = useQueryClient();

  // Create knowledge base mutation
  const createKBMutation = useMutation({
    mutationFn: (formData: {
      title: string;
      description?: string;
      access_level: KBAccessLevel;
      usage_mode: KBUsageMode;
      tags?: string[];
      allowed_users?: string[];
    }) =>
      KnowledgeBaseService.createKb({
        requestBody: {
          title: formData.title,
          description: formData.description,
          tags: formData.tags || [],
          access_level: formData.access_level,
          usage_mode: formData.usage_mode,
          allowed_users: formData.allowed_users || [],
        },
      }),
    onSuccess: (response) => {
      setCreateDialogOpen(false);
      if (response.id) {
        queryClient.setQueryData(
          [CacheKey.KnowledgeBase, 'detail', response.id],
          response,
        );
        toast.success('Knowledge base created successfully');
        queryClient.invalidateQueries({ queryKey: [CacheKey.KnowledgeBase] });
      }
    },
    onError: (error) => {
      console.error('Error creating knowledge base:', error);
      toast.error('Failed to create knowledge base');
    },
  });

  // Update knowledge base mutation
  const updateKBMutation = useMutation({
    mutationFn: ({
      kbId,
      formData,
    }: {
      kbId: string;
      formData: {
        title?: string;
        description?: string;
        access_level?: KBAccessLevel;
        usage_mode?: KBUsageMode;
        tags?: string[];
        allowed_users?: string[];
      };
    }) =>
      KnowledgeBaseService.updateKb({
        kbId,
        requestBody: {
          title: formData.title,
          description: formData.description,
          tags: formData.tags,
          access_level: formData.access_level,
          usage_mode: formData.usage_mode,
          allowed_users: formData.allowed_users,
        },
      }),
    onSuccess: () => {
      toast.success('Knowledge base updated successfully');
      setEditingKB(null);
      queryClient.invalidateQueries({ queryKey: [CacheKey.KnowledgeBase] });
    },
    onError: (error) => {
      console.error('Error updating knowledge base:', error);
      toast.error('Failed to update knowledge base');
    },
  });

  // Delete knowledge base mutation
  const deleteKBMutation = useMutation({
    mutationFn: (kbId: string) =>
      KnowledgeBaseService.deleteKb({
        kbId,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CacheKey.KnowledgeBase] });
      toast.success('Knowledge base deleted successfully');
      setDeletingKB(null);
    },
    onError: (error) => {
      console.error('Error deleting knowledge base:', error);
      toast.error('Failed to delete knowledge base');
    },
  });

  // Handlers
  const handleCreateSubmit = useCallback(
    (formData: {
      title: string;
      description?: string;
      access_level: KBAccessLevel;
      usage_mode: KBUsageMode;
      tags?: string[];
      allowed_users?: string[];
    }) => {
      createKBMutation.mutate(formData);
    },
    [createKBMutation],
  );

  const handleEditSubmit = useCallback(
    (formData: {
      title: string;
      description?: string;
      access_level: KBAccessLevel;
      usage_mode: KBUsageMode;
      tags?: string[];
      allowed_users?: string[];
    }) => {
      if (!editingKB) return;
      updateKBMutation.mutate({
        kbId: editingKB.id,
        formData,
      });
    },
    [editingKB, updateKBMutation],
  );

  const handleEdit = useCallback((kb: KBRead) => {
    setEditingKB(kb);
  }, []);

  const handleDelete = useCallback((kb: KBRead) => {
    setDeletingKB(kb);
  }, []);

  const confirmDelete = useCallback(() => {
    if (!deletingKB) return;
    deleteKBMutation.mutate(deletingKB.id);
  }, [deletingKB, deleteKBMutation]);

  const initialData = useMemo(
    () =>
      editingKB
        ? {
            title: editingKB.title,
            description: editingKB.description || '',
            access_level: editingKB.access_level || 'private',
            usage_mode: editingKB.usage_mode || 'manual',
            tags: editingKB.tags || [],
            allowed_users: editingKB.allowed_users || [],
          }
        : undefined,
    [editingKB],
  );

  const headerActions = (
    <Button
      onClick={() => setCreateDialogOpen(true)}
      size="sm"
      variant="default"
      className="text-sm font-medium"
    >
      <Plus className="mr-2 h-4 w-4" />
      New
    </Button>
  );

  return (
    <>
      <div className="flex h-[calc(100vh-4rem)] flex-col">
        <div className="flex-none">
          <PageHeader
            title="Knowledge Base"
            description="Create and manage your knowledge bases"
            actions={headerActions}
          />
          <KBStatusSummary />
          <Separator className="my-6" />
        </div>

        <div className="min-h-0 flex-1">
          <KBListingPage
            page={page}
            limit={limit}
            q={q || undefined}
            access_level={access_level || undefined}
            usage_mode={usage_mode || undefined}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />
        </div>
      </div>

      {/* Create/Edit Dialog */}
      <KBCreateDialog
        open={createDialogOpen || !!editingKB}
        onOpenChange={(open) => {
          if (!open) {
            setCreateDialogOpen(false);
            setEditingKB(null);
          }
        }}
        onSubmit={editingKB ? handleEditSubmit : handleCreateSubmit}
        initialData={initialData}
        mode={editingKB ? 'edit' : 'create'}
        isLoading={
          editingKB ? updateKBMutation.isPending : createKBMutation.isPending
        }
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingKB} onOpenChange={() => setDeletingKB(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the knowledge base &quot;
              {deletingKB?.title}&quot; and all its documents. This action
              cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
