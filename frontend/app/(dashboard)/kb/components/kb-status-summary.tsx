'use client';

import { useMemo } from 'react';

import { KnowledgeBaseService } from '@/client';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { CacheKey } from '@/components/utils/cache-key';
import { useQuery } from '@tanstack/react-query';
import {
  Clock,
  Database,
  FileText,
  Settings,
  Shield,
  Tag,
  Users,
} from 'lucide-react';

export function KBStatusSummary() {
  const { data, isLoading } = useQuery({
    queryKey: [CacheKey.KnowledgeBase, 'summary'],
    queryFn: () => KnowledgeBaseService.getKbs({ limit: 1000 }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const accessLevelCounts = useMemo(() => {
    if (!data?.data) return {};
    return data.data.reduce((acc: Record<string, number>, kb) => {
      const accessLevel = kb.access_level || 'private';
      acc[accessLevel] = (acc[accessLevel] || 0) + 1;
      return acc;
    }, {});
  }, [data]);

  const usageModeCounts = useMemo(() => {
    if (!data?.data) return {};
    return data.data.reduce((acc: Record<string, number>, kb) => {
      const usageMode = kb.usage_mode || 'manual';
      acc[usageMode] = (acc[usageMode] || 0) + 1;
      return acc;
    }, {});
  }, [data]);

  const sharedKBs = useMemo(() => {
    if (!data?.data) return [];
    return data.data.filter(
      (kb) =>
        kb.access_level === 'shared' &&
        kb.allowed_users &&
        kb.allowed_users.length > 1,
    );
  }, [data]);

  const topTags = useMemo(() => {
    if (!data?.data) return [];
    const tagCounts = data.data.reduce((acc: Record<string, number>, kb) => {
      if (kb.tags && kb.tags.length > 0) {
        kb.tags.forEach((tag) => {
          acc[tag] = (acc[tag] || 0) + 1;
        });
      }
      return acc;
    }, {});
    return Object.entries(tagCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3); // Show top 3
  }, [data]);

  if (isLoading) {
    return (
      <div className="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="h-[120px] min-w-[220px] flex-1">
            <CardContent className="flex h-full flex-col p-4">
              <Skeleton className="mb-2 h-5 w-24 shrink-0" />
              <div className="flex flex-1 flex-col justify-center space-y-1">
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-4 w-full" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {/* Total Knowledge Bases Card */}
      <Card className="h-[120px] min-w-[220px] flex-1">
        <CardContent className="flex h-full flex-col p-4">
          <div className="mb-3 flex shrink-0 items-center justify-between">
            <p className="text-sm font-medium text-muted-foreground">
              Total Knowledge Bases
            </p>
            <Database className="h-5 w-5 text-muted-foreground/70" />
          </div>
          <div className="flex flex-1 flex-col justify-center">
            <h3 className="text-2xl font-bold">{data?.count || 0}</h3>
            <p className="mt-1 text-xs text-muted-foreground">
              Across the workspace
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Access Level Overview Card */}
      <Card className="h-[120px] min-w-[220px] flex-1">
        <CardContent className="flex h-full flex-col p-4">
          <div className="mb-3 flex shrink-0 items-center justify-between">
            <p className="text-sm font-medium text-muted-foreground">
              Access Levels
            </p>
            <Shield className="h-5 w-5 text-muted-foreground/70" />
          </div>
          <div className="grid flex-1 grid-cols-2 content-start gap-x-2 gap-y-1.5">
            {Object.entries(accessLevelCounts).length > 0 ? (
              Object.entries(accessLevelCounts).map(([level, count]) => (
                <AccessLevelItem key={level} level={level} count={count} />
              ))
            ) : (
              <p className="col-span-2 text-sm text-muted-foreground">
                No data
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Usage Mode Overview Card */}
      <Card className="h-[120px] min-w-[220px] flex-1">
        <CardContent className="flex h-full flex-col p-4">
          <div className="mb-3 flex shrink-0 items-center justify-between">
            <p className="text-sm font-medium text-muted-foreground">
              Usage Modes
            </p>
            <Settings className="h-5 w-5 text-muted-foreground/70" />
          </div>
          <div className="grid flex-1 grid-cols-2 content-start gap-x-2 gap-y-1">
            {Object.entries(usageModeCounts).length > 0 ? (
              Object.entries(usageModeCounts)
                .slice(0, 4)
                .map(([mode, count]) => (
                  <UsageModeItem key={mode} mode={mode} count={count} />
                ))
            ) : (
              <p className="col-span-2 text-sm text-muted-foreground">
                No data
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Top Tags Card */}
      <Card className="h-[120px] min-w-[220px] flex-1">
        <CardContent className="flex h-full flex-col p-4">
          <div className="mb-3 flex shrink-0 items-center justify-between">
            <p className="text-sm font-medium text-muted-foreground">
              Popular Tags
            </p>
            <Tag className="h-5 w-5 text-muted-foreground/70" />
          </div>
          <div className="grid flex-1 grid-cols-2 content-start gap-x-2 gap-y-1">
            {topTags.length > 0 ? (
              topTags.map(([tag, count]) => (
                <div
                  key={tag}
                  className="flex min-w-0 items-center justify-between text-sm"
                >
                  <span className="truncate text-xs font-medium">{tag}</span>
                  <span className="shrink-0 text-xs font-semibold text-muted-foreground">
                    {count}
                  </span>
                </div>
              ))
            ) : (
              <p className="col-span-2 text-sm text-muted-foreground">
                No tags
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Helper component for access level items
function AccessLevelItem({ level, count }: { level: string; count: number }) {
  let icon;
  switch (level.toLowerCase()) {
    case 'private':
      icon = <Shield className="h-4 w-4 text-slate-500" />;
      break;
    case 'shared':
      icon = <Users className="h-4 w-4 text-blue-500" />;
      break;
    default:
      icon = <Shield className="h-4 w-4 text-slate-400" />;
      break;
  }
  return (
    <div className="flex min-w-0 items-center justify-between py-0.5 text-sm">
      <div className="flex min-w-0 items-center gap-1">
        {icon}
        <span className="truncate text-xs capitalize">{level}</span>
      </div>
      <span className="shrink-0 text-xs font-semibold text-muted-foreground">
        {count}
      </span>
    </div>
  );
}

// Helper component for usage mode items
function UsageModeItem({ mode, count }: { mode: string; count: number }) {
  let icon;
  switch (mode.toLowerCase()) {
    case 'manual':
      icon = <FileText className="h-4 w-4 text-green-500" />;
      break;
    case 'agent_requested':
      icon = <Tag className="h-4 w-4 text-orange-500" />;
      break;
    case 'always':
      icon = <Clock className="h-4 w-4 text-purple-500" />;
      break;
    default:
      icon = <Settings className="h-4 w-4 text-slate-400" />;
      break;
  }
  return (
    <div className="flex min-w-0 items-center justify-between py-0.5 text-sm">
      <div className="flex min-w-0 items-center gap-1">
        {icon}
        <span className="truncate text-xs">{mode.replace('_', ' ')}</span>
      </div>
      <span className="shrink-0 text-xs font-semibold text-muted-foreground">
        {count}
      </span>
    </div>
  );
}
