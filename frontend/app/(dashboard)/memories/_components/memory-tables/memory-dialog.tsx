'use client';

import { Memory } from '@/client';
import { MarkdownRenderer } from '@/components/chat/components/message/markdown-renderer';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';

interface MemoryDialogProps {
  memory: Memory | null;
  isOpen: boolean;
  onClose: () => void;
}

export function MemoryDialog({ memory, isOpen, onClose }: MemoryDialogProps) {
  if (!memory) return null;

  const formattedAgentRole = memory.agent_role
    .replace('_', ' ')
    .replace(/\b\w/g, (l) => l.toUpperCase());

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] overflow-hidden sm:max-w-[700px]">
        <DialogHeader className="pb-2">
          <DialogTitle className="flex items-center gap-2 text-xl">
            Memory Details
            <Badge variant="outline">{formattedAgentRole}</Badge>
          </DialogTitle>
          <DialogDescription>
            {memory.task || 'No task specified'}
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[calc(90vh-150px)] pr-4">
          <div className="space-y-6">
            {/* Tags Section */}
            {memory.tags && memory.tags.length > 0 && (
              <div className="mb-4 flex flex-wrap gap-1.5">
                {memory.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}

            {/* Solution Section */}
            {memory.solution && (
              <div className="space-y-2">
                <h3 className="border-b pb-1 text-lg font-semibold">
                  Solution
                </h3>
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <MarkdownRenderer content={memory.solution} />
                </div>
              </div>
            )}

            {/* Links Section */}
            {memory.links && memory.links.length > 0 && (
              <div className="space-y-2">
                <h3 className="border-b pb-1 text-lg font-semibold">
                  Related Links
                </h3>
                <ul className="space-y-2 pl-1">
                  {memory.links.map((link, index) => (
                    <li key={index} className="flex items-center">
                      <Badge variant="info" className="mr-2">
                        Link
                      </Badge>
                      <span>{link}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* ID Section */}
            <div className="border-t pt-2 text-xs text-muted-foreground">
              <span className="font-medium">Memory ID:</span>{' '}
              <span className="font-mono">{memory.id}</span>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
