import PageContainer from '@/components/layout/page-container';
import { PageHeader } from '@/components/layout/page-header';

import MemoryListing from './_components/memory-listing';

export const metadata = {
  title: 'Dashboard: Memories',
};

type PageProps = {
  searchParams: Promise<{
    page: string;
    limit: string;
    q: string;
    agent_role: string;
  }>;
};

export default async function MemoriesPage(props: PageProps) {
  const searchParams = await props.searchParams;
  return (
    <PageContainer>
      <PageHeader
        title="Memories"
        description="View and manage agent memories"
      />
      <MemoryListing
        page={searchParams.page ? parseInt(searchParams.page) : undefined}
        limit={searchParams.limit ? parseInt(searchParams.limit) : undefined}
        q={searchParams.q || undefined}
        agentRole={searchParams.agent_role || undefined}
      />
    </PageContainer>
  );
}
