'use client';

import { RecommendationPublic } from '@/client';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { formatUSD } from '@/lib/currency';

interface ActionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  recommendation: RecommendationPublic | null;
  actionType: 'SAVE' | 'DISMISS' | 'DELETE';
  onConfirm: () => void;
  isLoading?: boolean;
}

export function ActionDialog({
  open,
  onOpenChange,
  recommendation,
  actionType,
  onConfirm,
  isLoading = false,
}: ActionDialogProps) {
  if (!recommendation) return null;

  const getActionDetails = () => {
    switch (actionType) {
      case 'SAVE':
        return {
          title: 'Save Recommendation',
          description: 'Are you sure you want to save this recommendation?',
          confirmText: 'Save',
          confirmVariant: 'default' as const,
        };
      case 'DISMISS':
        return {
          title: 'Dismiss Recommendation',
          description:
            'Are you sure you want to dismiss this recommendation? This action cannot be undone.',
          confirmText: 'Dismiss',
          confirmVariant: 'destructive' as const,
        };
      case 'DELETE':
        return {
          title: 'Delete Recommendation',
          description:
            'Are you sure you want to delete this recommendation? This action cannot be undone.',
          confirmText: 'Delete',
          confirmVariant: 'destructive' as const,
        };
    }
  };

  const { title, description, confirmText, confirmVariant } =
    getActionDetails();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="rounded-lg bg-muted/20 p-4">
            <h4 className="mb-2 font-medium">{recommendation.title}</h4>
            <p className="mb-2 text-sm text-muted-foreground">
              {recommendation.description}
            </p>
            <div className="flex items-center justify-between text-sm">
              <span>Monthly Savings:</span>
              <span className="font-semibold text-green-600">
                {formatUSD(recommendation.potential_savings || 0)}
              </span>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant={confirmVariant}
            onClick={onConfirm}
            disabled={isLoading}
          >
            {isLoading ? 'Processing...' : confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
