'use client';

import { memo, useCallback, useMemo, useState } from 'react';

import { TasksService } from '@/client';
import { TaskHistory, TaskResponse } from '@/client/types.gen';
import { Button } from '@/components/ui/button';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { CacheKey } from '@/components/utils/cache-key';
import { DateFormat, formatUtcDate } from '@/lib/date-utils';
import { cn } from '@/lib/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Activity,
  BarChart3,
  ChevronRight,
  Power,
  PowerOff,
  RefreshCcw,
  StopCircle,
} from 'lucide-react';
import { toast } from 'sonner';

import TimelineExecutionPoint from './timeline-execution-point';
import TimelineStatusLegend from './timeline-status-legend';

interface EnhancedRecentActivityProps {
  taskHistory?: TaskHistory[];
  task?: TaskResponse; // Add task prop for enable/disable functionality
  onViewAll: () => void;
  onExecutionClick?: (executionId: string) => void;
  taskId?: string;
  initialItems?: number; // Default visible items
  maxExpandedItems?: number; // Max items when expanded
  enablePagination?: boolean; // Enable pagination
  variant?: 'compact' | 'standard' | 'detailed';
  className?: string;
}

// Helper function to get status text consistently
const getStatusText = (status?: string | null): string => {
  if (!status) return 'pending';

  const normalizedStatus = status.toLowerCase().replace(/_/g, ' ').trim();

  if (normalizedStatus.includes('fail') || normalizedStatus.includes('error')) {
    return 'failed';
  }

  if (
    normalizedStatus.includes('success') ||
    normalizedStatus === 'completed'
  ) {
    return 'succeeded';
  }

  if (
    normalizedStatus.includes('running') ||
    normalizedStatus.includes('progress')
  ) {
    return 'running';
  }

  if (normalizedStatus.includes('cancel')) {
    return 'cancelled';
  }

  if (normalizedStatus.includes('approval')) {
    return 'required_approval';
  }

  return normalizedStatus;
};

// Enhanced Task Enable/Disable Toggle with Modern UI
const TaskEnableToggle = ({ task }: { task: TaskResponse }) => {
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);
  const isRunning = task.execution_status === 'running';

  const enableMutation = useMutation({
    mutationFn: ({ id, enable }: { id: string; enable: boolean }) =>
      TasksService.updateTaskEnable({ taskId: id, enable }),
    onSuccess: (updatedTask, { enable }) => {
      if (enable) {
        toast.success('Task Enabled', {
          description: `"${task.title}" is now enabled and will run according to its schedule.`,
          duration: 5000,
        });
      } else {
        toast.success('Task Disabled', {
          description: `"${task.title}" has been disabled. Scheduled executions have been cancelled.`,
          duration: 5000,
        });
      }
    },
    onError: (error: any) => {
      const errorMessage =
        error?.body?.detail || error?.message || 'Failed to update task status';
      toast.error('Operation Failed', {
        description: `Could not ${task.enable ? 'disable' : 'enable'} task: ${errorMessage}`,
        duration: 6000,
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['task', task.id] });
      queryClient.invalidateQueries({ queryKey: [CacheKey.Items] });
    },
  });

  const handleToggle = async () => {
    if (isRunning && task.enable) {
      toast.warning('Task Currently Running', {
        description: `"${task.title}" is currently running. Disabling will prevent future executions.`,
        duration: 6000,
      });
    }

    setLoading(true);
    try {
      await enableMutation.mutateAsync({ id: task.id, enable: !task.enable });
    } finally {
      setLoading(false);
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center gap-2">
            <span
              className={cn(
                'text-sm font-medium',
                task.enable
                  ? 'text-emerald-700 dark:text-emerald-300'
                  : 'text-gray-600 dark:text-gray-400',
              )}
            >
              {task.enable ? 'Enabled' : 'Disabled'}
            </span>
            <button
              onClick={handleToggle}
              disabled={loading}
              className={cn(
                'relative inline-flex h-6 w-11 items-center rounded-full transition-all duration-200 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                task.enable
                  ? 'bg-emerald-600 shadow-emerald-200 dark:shadow-emerald-800'
                  : 'bg-gray-200 dark:bg-gray-700',
                loading && 'cursor-not-allowed opacity-50',
                isRunning && task.enable && 'ring-2 ring-blue-500/30',
              )}
              role="switch"
              aria-checked={task.enable}
              aria-label={task.enable ? 'Disable task' : 'Enable task'}
            >
              <span
                className={cn(
                  'inline-block flex h-4 w-4 transform items-center justify-center rounded-full bg-white shadow-lg transition-all duration-200',
                  task.enable ? 'translate-x-6' : 'translate-x-1',
                )}
              >
                {loading ? (
                  <RefreshCcw className="h-2 w-2 text-gray-600" />
                ) : task.enable ? (
                  <Power className="h-2 w-2 text-emerald-600" />
                ) : (
                  <PowerOff className="h-2 w-2 text-gray-400" />
                )}
              </span>
            </button>
          </div>
        </TooltipTrigger>
        <TooltipContent
          side="bottom"
          className="max-w-xs border-border/60 bg-card shadow-lg backdrop-blur-xs"
        >
          <div className="space-y-1.5 text-center">
            <div className="font-medium text-card-foreground">
              {task.enable ? 'Disable Task' : 'Enable Task'}
            </div>
            <div className="text-xs text-muted-foreground">
              {task.enable
                ? 'Stop scheduled and running executions'
                : 'Allow task to run according to schedule'}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Enhanced Stop Execution Control - Always Visible for Running Tasks
const StopExecutionControl = ({
  task,
  execution,
}: {
  task: TaskResponse;
  execution: TaskHistory;
}) => {
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);
  const status = getStatusText(execution.status);

  const stopMutation = useMutation({
    mutationFn: () =>
      TasksService.stopTaskExecution({
        taskId: task.id,
        conversationId: execution.conversation_id,
      }),
    onSuccess: () => {
      toast.success('Execution Stopped', {
        description: `Execution has been cancelled successfully.`,
        duration: 4000,
      });
    },
    onError: (error: any) => {
      const errorMessage =
        error?.body?.detail || error?.message || 'Failed to stop execution';
      toast.error('Stop Failed', {
        description: `Could not stop execution: ${errorMessage}`,
        duration: 6000,
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['task', task.id] });
      queryClient.invalidateQueries({ queryKey: [CacheKey.Items] });
    },
  });

  const handleStop = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setLoading(true);
    try {
      await stopMutation.mutateAsync();
    } finally {
      setLoading(false);
    }
  };

  if (status !== 'running') return null;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            onClick={handleStop}
            disabled={loading}
            className={cn(
              'flex h-6 w-6 items-center justify-center p-0 transition-colors duration-200',
              'border-red-500 bg-red-50 text-red-600',
              'dark:border-red-500 dark:bg-red-950/30 dark:text-red-400',
              loading && 'cursor-not-allowed opacity-70',
            )}
          >
            {loading ? (
              <RefreshCcw className="h-3 w-3" />
            ) : (
              <StopCircle className="h-3 w-3" />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent
          className="tooltip-error border-border/60 bg-card/95 shadow-lg backdrop-blur-xs"
          side="top"
        >
          <div className="tooltip-content-enhanced space-y-1.5 text-center">
            <div className="tooltip-title text-destructive-foreground">
              {loading ? 'Stopping Execution' : 'Stop Execution'}
            </div>
            <div className="tooltip-description text-muted-foreground">
              {loading
                ? 'Cancelling the running task execution...'
                : 'Immediately terminate this running execution'}
            </div>
            {!loading && (
              <div className="mt-2 border-t border-border/40 pt-1.5 text-xs text-orange-600 dark:text-orange-400">
                ⚠️ This action cannot be undone
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

const EnhancedRecentActivity = memo(function EnhancedRecentActivity({
  taskHistory,
  task,
  onViewAll,
  onExecutionClick,
  taskId,
  initialItems = 8,
  maxExpandedItems = 20,
  enablePagination = true,
  variant = 'standard',
  className,
}: EnhancedRecentActivityProps) {
  // Pagination state
  const [showAll, setShowAll] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = showAll ? maxExpandedItems : initialItems;

  // Calculate visible executions with pagination
  const { visibleExecutions, totalPages, hasMore, canPaginate } =
    useMemo(() => {
      const executions = taskHistory || [];
      const total = executions.length;
      const hasMoreThanInitial = total > initialItems;

      if (!showAll) {
        // Show initial items only
        return {
          visibleExecutions: executions.slice(0, initialItems),
          totalPages: 1,
          hasMore: hasMoreThanInitial,
          canPaginate: false,
        };
      }

      // Show paginated expanded view
      const pages = Math.ceil(total / maxExpandedItems);
      const startIndex = currentPage * maxExpandedItems;
      const endIndex = startIndex + maxExpandedItems;

      return {
        visibleExecutions: executions.slice(startIndex, endIndex),
        totalPages: pages,
        hasMore: hasMoreThanInitial,
        canPaginate: pages > 1,
      };
    }, [taskHistory, showAll, currentPage, initialItems, maxExpandedItems]);

  // Handle expand/collapse
  const handleToggleExpand = useCallback(() => {
    setShowAll((prev) => {
      if (prev) {
        // Collapsing - reset to first page
        setCurrentPage(0);
      }
      return !prev;
    });
  }, []);

  // Handle pagination
  const handlePrevPage = useCallback(() => {
    setCurrentPage((prev) => Math.max(0, prev - 1));
  }, []);

  const handleNextPage = useCallback(() => {
    setCurrentPage((prev) => Math.min(totalPages - 1, prev + 1));
  }, [totalPages]);

  // Empty state
  if (!taskHistory || taskHistory.length === 0) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
              <Activity className="h-4 w-4 text-primary" />
            </div>
            <div>
              <div className="flex items-center gap-4">
                <h3 className="text-base font-medium">Recent Activity</h3>
                {task && <TaskEnableToggle task={task} />}
              </div>
              <p className="text-sm text-muted-foreground">
                Monitor task execution history and controls
              </p>
            </div>
          </div>
        </div>

        <div className="card border-dashed p-8">
          <div className="flex flex-col items-center gap-3 text-center">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-muted/50">
              <Activity className="h-6 w-6 text-muted-foreground" />
            </div>
            <div>
              <h4 className="font-medium text-muted-foreground">
                No Recent Activity
              </h4>
              <p className="mt-1 max-w-sm text-sm text-muted-foreground/70">
                Task executions will appear here as an interactive timeline with
                controls.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Enhanced Header Section with Task Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
            <Activity className="h-4 w-4 text-primary" />
          </div>
          <div>
            <div className="flex items-center gap-4">
              <h3 className="text-base font-medium">Recent Activity</h3>
              {task && <TaskEnableToggle task={task} />}

              {/* Running Executions Stop Controls */}
              {task &&
                taskHistory &&
                taskHistory.some(
                  (exec) => getStatusText(exec.status) === 'running',
                ) && (
                  <div className="flex items-center gap-2 rounded-lg border border-blue-200 bg-blue-50 px-3 py-1.5 dark:border-blue-800/50 dark:bg-blue-950/30">
                    <div className="flex items-center gap-1 text-xs text-blue-700 dark:text-blue-300">
                      <div className="h-2 w-2 rounded-full bg-blue-500" />
                      <span className="font-medium">Running executions:</span>
                    </div>
                    <div className="flex items-center gap-1">
                      {taskHistory
                        .filter(
                          (exec) => getStatusText(exec.status) === 'running',
                        )
                        .map((execution, index) => (
                          <StopExecutionControl
                            key={execution.id || `stop-${index}`}
                            task={task}
                            execution={execution}
                          />
                        ))}
                    </div>
                  </div>
                )}
            </div>
            <p className="mt-0.5 text-sm text-muted-foreground">
              {showAll ? (
                <>
                  Showing {visibleExecutions.length} of {taskHistory.length}{' '}
                  executions
                </>
              ) : (
                <>
                  Last {visibleExecutions.length} execution
                  {visibleExecutions.length > 1 ? 's' : ''}
                </>
              )}
            </p>
          </div>
        </div>

        <Button
          variant="outline"
          size="sm"
          className="button h-8 px-3 text-xs"
          onClick={onViewAll}
        >
          <BarChart3 className="mr-1.5 h-3 w-3" />
          View All History
          <ChevronRight className="ml-1.5 h-3 w-3" />
        </Button>
      </div>

      {/* Timeline Container */}
      <div className="card overflow-hidden p-6">
        {/* Enhanced Beautiful Timeline with Intelligent Spacing */}
        <div className="relative px-20 py-8">
          {/* Calculate intelligent spacing based on execution count and UI/UX best practices */}
          {(() => {
            const executionCount = visibleExecutions.length;
            const hasExecutions = executionCount > 0;
            const containerWidth = executionCount <= 3 ? 'max-w-2xl' : 'w-full';

            // Calculate intelligent positioning
            const getExecutionPositions = () => {
              if (executionCount <= 1) return ['50%']; // Center single execution
              if (executionCount === 2) return ['25%', '75%']; // Balanced positioning for 2
              if (executionCount === 3) return ['16.67%', '50%', '83.33%']; // Balanced for 3

              // For 4+ executions, use even distribution
              return visibleExecutions.map(
                (_, index) =>
                  `${(index / Math.max(executionCount - 1, 1)) * 100}%`,
              );
            };

            const positions = getExecutionPositions();
            return (
              <div
                className={cn(
                  'timeline-intelligent-spacing mx-auto',
                  containerWidth,
                )}
              >
                {/* Intelligent Timeline Line - Centered */}
                <div className="relative" style={{ height: '6rem' }}>
                  {/* Main timeline line positioned in the middle */}
                  <div className="absolute left-0 right-0 top-1/4 z-0 -translate-y-1 transform">
                    <div className="timeline-line-enhanced relative h-1">
                      {/* Subtle glow effect */}
                      <div className="timeline-line-glow absolute inset-0 h-1 rounded-full"></div>
                      {/* Smart end caps - only show if we have executions */}
                      {hasExecutions && (
                        <>
                          <div className="timeline-end-cap absolute -left-1 top-1/2 h-3 w-3 -translate-y-1/2 transform rounded-full"></div>
                          <div className="timeline-end-cap absolute -right-1 top-1/2 h-3 w-3 -translate-y-1/2 transform rounded-full"></div>
                        </>
                      )}

                      {/* Latest/Oldest indicators positioned at the tips of the timeline bar */}
                      {hasExecutions && visibleExecutions.length > 1 && (
                        <>
                          {/* Latest indicator at the left tip */}
                          <div className="absolute -left-24 top-1/2 -translate-y-1/2 transform">
                            <div className="timeline-time-label flex items-center gap-2 rounded border border-border/50 bg-background/90 px-2 py-1 text-xs shadow-xs">
                              <span className="whitespace-nowrap font-medium text-foreground">
                                Latest
                              </span>
                              <div className="h-2 w-2 rounded-full bg-primary" />
                            </div>
                          </div>

                          {/* Oldest indicator at the right tip */}
                          <div className="absolute -right-24 top-1/2 -translate-y-1/2 transform">
                            <div className="timeline-time-label flex items-center gap-2 rounded border border-border/50 bg-background/90 px-2 py-1 text-xs shadow-xs">
                              <div className="h-2 w-2 rounded-full bg-primary" />
                              <span className="whitespace-nowrap font-medium text-foreground">
                                Oldest
                              </span>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Timeline Execution Points positioned exactly on the timeline */}
                  <div className="timeline-execution-container">
                    {hasExecutions ? (
                      visibleExecutions.map((execution, index) => {
                        // Format time for display above execution point (convert UTC to local time)
                        const timeLabel = execution.start_time
                          ? formatUtcDate(execution.start_time, DateFormat.TIME)
                          : 'Time here';

                        return (
                          <div
                            key={execution.id || `timeline-${index}`}
                            className="timeline-point-positioned"
                            style={{
                              left: positions[index],
                            }}
                          >
                            {/* Time label above the execution point */}
                            <div className="absolute bottom-full left-1/2 mb-4 -translate-x-1/2 transform">
                              <div className="whitespace-nowrap rounded-md border border-border/60 bg-background/95 px-3 py-1.5 text-xs font-medium text-foreground shadow-md backdrop-blur-xs">
                                {execution.start_time
                                  ? formatUtcDate(
                                      execution.start_time,
                                      DateFormat.SHORT_DATE,
                                    ) +
                                    ' ' +
                                    timeLabel
                                  : 'Time here'}
                              </div>
                            </div>

                            <div className="timeline-execution-point group relative flex flex-col items-center">
                              <TimelineExecutionPoint
                                execution={execution}
                                index={index}
                                onClick={
                                  onExecutionClick ? undefined : onViewAll
                                }
                                onExecutionClick={onExecutionClick}
                                taskId={taskId}
                                size={
                                  variant === 'compact'
                                    ? 'sm'
                                    : variant === 'detailed'
                                      ? 'lg'
                                      : 'md'
                                }
                                showOrderIndicator={false}
                                showStatusBadge={true}
                              />
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      /* Placeholder when no executions visible */
                      <div className="flex items-center justify-center">
                        <div className="text-sm italic text-muted-foreground">
                          No executions to display
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })()}
        </div>

        {/* Summary Section */}
        <TimelineStatusLegend size="sm" className="flex-wrap" />
      </div>
    </div>
  );
});

export default EnhancedRecentActivity;
