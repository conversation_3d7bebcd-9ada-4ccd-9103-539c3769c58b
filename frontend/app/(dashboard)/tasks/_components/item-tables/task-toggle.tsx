'use client';

import { useState } from 'react';

import { TasksService } from '@/client';
import { Switch } from '@/components/ui/switch';
import { CacheKey } from '@/components/utils/cache-key';
import { useQueryClient } from '@tanstack/react-query';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface TaskToggleProps {
  taskId: string;
  enabled: boolean;
  onToggle?: (taskId: string, enabled: boolean) => Promise<void>;
  disabled?: boolean;
}

export function TaskToggle({
  taskId,
  enabled,
  onToggle,
  disabled = false,
}: TaskToggleProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [currentState, setCurrentState] = useState(enabled);
  const queryClient = useQueryClient();

  const handleToggle = async (newState: boolean) => {
    if (isLoading || disabled) return;

    // Optimistic update
    setCurrentState(newState);
    setIsLoading(true);

    try {
      if (onToggle) {
        await onToggle(taskId, newState);
      } else {
        // Use the API service
        await TasksService.updateTaskEnable({
          taskId,
          enable: newState,
        });
      }

      // Invalidate tasks cache to refresh the data
      await queryClient.invalidateQueries({
        queryKey: [CacheKey.Items],
      });

      toast.success(`Task ${newState ? 'enabled' : 'disabled'} successfully`, {
        description: `The task is now ${newState ? 'active and can be scheduled' : 'inactive and will not run'}.`,
      });
    } catch (error) {
      // Rollback optimistic update on error
      setCurrentState(enabled);

      console.error('Failed to toggle task:', error);
      toast.error(`Failed to ${newState ? 'enable' : 'disable'} task`, {
        description:
          'Please try again or contact support if the problem persists.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <div className="relative">
        <Switch
          checked={currentState}
          onCheckedChange={handleToggle}
          disabled={disabled || isLoading}
          aria-label={`${currentState ? 'Disable' : 'Enable'} task`}
          className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-gray-200"
        />
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Loader2 className="h-3 w-3 animate-spin text-primary" />
          </div>
        )}
      </div>
      <span className="hidden text-xs text-muted-foreground sm:inline">
        {currentState ? 'On' : 'Off'}
      </span>
    </div>
  );
}
