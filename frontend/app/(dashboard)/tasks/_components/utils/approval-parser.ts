export interface ParsedApprovalMessage {
  toolName: string;
  toolArgs: string;
  isStructured: boolean;
}

/**
 * Parses the structured approval message format:
 * ⚠️ This operation requires permissions.
 * Operation:
 * {tool_name}
 *
 * Details:
 * {tool_args}
 *
 * Please confirm to proceed with these changes.
 */
export function parseApprovalMessage(message: string): ParsedApprovalMessage {
  // Check if this follows the structured format
  const operationMatch = message.match(/Operation:\s*([^\n]+)/);
  const detailsMatch = message.match(
    /Details:\s*([\s\S]*?)(?:\n\nPlease confirm|$)/,
  );

  const isStructured = !!(operationMatch && detailsMatch);

  return {
    toolName: operationMatch?.[1]?.trim() || 'Unknown Operation',
    toolArgs: detailsMatch?.[1]?.trim() || message,
    isStructured,
  };
}

/**
 * Formats the tool name for display, truncating if necessary
 */
export function formatToolName(
  toolName: string,
  maxLength: number = 50,
): string {
  if (toolName.length <= maxLength) return toolName;
  return toolName.slice(0, maxLength) + '...';
}

/**
 * Formats the tool arguments for display, handling JSON and plain text
 */
export function formatToolArgs(toolArgs: string, maxLength?: number): string {
  let formatted = toolArgs;

  // Try to format JSON if it looks like JSON
  try {
    if (toolArgs.trim().startsWith('{') || toolArgs.trim().startsWith('[')) {
      const parsed = JSON.parse(toolArgs);
      formatted = JSON.stringify(parsed, null, 2);
    }
  } catch {
    // Not JSON, keep as is
  }

  if (maxLength && formatted.length > maxLength) {
    return formatted.slice(0, maxLength) + '...';
  }

  return formatted;
}
