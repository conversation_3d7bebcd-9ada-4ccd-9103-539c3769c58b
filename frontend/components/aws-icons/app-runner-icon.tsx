import { LucideProps } from 'lucide-react';

export function AppRunnerIcon(props: LucideProps) {
  return (
    <svg
      {...props}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient
          id="app-runner-gradient"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#C8511B" />
          <stop offset="100%" stopColor="#FF9900" />
        </linearGradient>
      </defs>
      <rect width="24" height="24" rx="4" fill="url(#app-runner-gradient)" />
      <g transform="translate(4, 4)">
        <rect
          x="2"
          y="3"
          width="12"
          height="10"
          rx="2"
          fill="white"
          opacity="0.2"
        />
        <rect
          x="3"
          y="4"
          width="10"
          height="8"
          rx="1"
          fill="white"
          opacity="0.9"
        />

        <circle
          cx="6"
          cy="7"
          r="1.5"
          fill="url(#app-runner-gradient)"
          opacity="0.8"
        />
        <rect
          x="8.5"
          y="6"
          width="3"
          height="1"
          rx="0.5"
          fill="url(#app-runner-gradient)"
          opacity="0.7"
        />
        <rect
          x="8.5"
          y="8"
          width="2"
          height="1"
          rx="0.5"
          fill="url(#app-runner-gradient)"
          opacity="0.6"
        />

        <polygon
          points="5,10 7,12 9,10"
          fill="url(#app-runner-gradient)"
          opacity="0.8"
        />
        <rect
          x="10"
          y="10"
          width="2"
          height="2"
          rx="1"
          fill="url(#app-runner-gradient)"
          opacity="0.7"
        />
      </g>
    </svg>
  );
}
