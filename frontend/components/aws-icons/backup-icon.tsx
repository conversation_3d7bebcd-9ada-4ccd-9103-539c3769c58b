import { LucideProps } from 'lucide-react';

export const BackupIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_AWS-Backup_16</title>
    <desc>Created with Sketch.</desc>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="backup-gradient">
        <stop stopColor="#1B660F" offset="0%"></stop>
        <stop stopColor="#6CAE3E" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g
      id="Icon-Architecture/16/Arch_AWS-Backup_16"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g id="Icon-Architecture-BG/16/Storage" fill="url(#backup-gradient)">
        <rect
          id="Rectangle"
          x="0"
          y="0"
          width="24"
          height="24"
          rx="4"
          ry="4"
        ></rect>
      </g>
      <path
        d="M12,20 C7.589,20 4,16.411 4,12 C4,7.589 7.589,4 12,4 C16.411,4 20,7.589 20,12 C20,16.411 16.411,20 12,20 M12,3 C6.486,3 2,7.486 2,12 C2,16.514 6.486,21 12,21 C17.514,21 22,16.514 22,12 C22,7.486 17.514,3 12,3 M16,11 L13,11 L13,8 C13,7.724 12.776,7.5 12.5,7.5 C12.224,7.5 12,7.724 12,8 L12,11 L9,11 C8.724,11 8.5,11.224 8.5,11.5 C8.5,11.776 8.724,12 9,12 L12,12 L12,15 C12,15.276 12.224,15.5 12.5,15.5 C12.776,15.5 13,15.276 13,15 L13,12 L16,12 C16.276,12 16.5,11.776 16.5,11.5 C16.5,11.224 16.276,11 16,11 M7,17 L7,16 L8,16 L8,17 L7,17 Z M6,18 L9,18 L9,15 L6,15 L6,18 Z M17,7 L16,7 L16,8 L17,8 L17,7 Z M18,6 L15,6 L15,9 L18,9 L18,6 Z"
        id="AWS-Backup_Icon_16_Squid"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
