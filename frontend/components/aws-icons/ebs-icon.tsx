import { LucideProps } from 'lucide-react';

export const EbsIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_Amazon-EBS_16</title>
    <desc>Created with Sketch.</desc>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="ebs-gradient">
        <stop stopColor="#1B660F" offset="0%"></stop>
        <stop stopColor="#6CAE3E" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g
      id="Icon-Architecture/16/Arch_Amazon-EBS_16"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g id="Icon-Architecture-BG/16/Storage" fill="url(#ebs-gradient)">
        <rect
          id="Rectangle"
          x="0"
          y="0"
          width="24"
          height="24"
          rx="4"
          ry="4"
        ></rect>
      </g>
      <path
        d="M19,6 L19,18 L5,18 L5,6 L19,6 Z M20,5 L4,5 C3.448,5 3,5.448 3,6 L3,18 C3,18.552 3.448,19 4,19 L20,19 C20.552,19 21,18.552 21,18 L21,6 C21,5.448 20.552,5 20,5 L20,5 Z M17,8 L17,16 L7,16 L7,8 L17,8 Z M18,7 L6,7 C5.448,7 5,7.448 5,8 L5,16 C5,16.552 5.448,17 6,17 L18,17 C18.552,17 19,16.552 19,16 L19,8 C19,7.448 18.552,7 18,7 L18,7 Z M15,10 L15,14 L9,14 L9,10 L15,10 Z M16,9 L8,9 C7.448,9 7,9.448 7,10 L7,14 C7,14.552 7.448,15 8,15 L16,15 C16.552,15 17,14.552 17,14 L17,10 C17,9.448 16.552,9 16,9 L16,9 Z"
        id="Amazon-EBS_Icon_16_Squid"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
