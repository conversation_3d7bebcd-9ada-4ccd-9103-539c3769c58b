import { LucideProps } from 'lucide-react';

export function ElasticBeanstalkIcon(props: LucideProps) {
  return (
    <svg
      {...props}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient
          id="elastic-beanstalk-gradient"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#C8511B" />
          <stop offset="100%" stopColor="#FF9900" />
        </linearGradient>
      </defs>
      <rect
        width="24"
        height="24"
        rx="4"
        fill="url(#elastic-beanstalk-gradient)"
      />
      <g transform="translate(4, 4)">
        <ellipse cx="8" cy="4" rx="6" ry="2" fill="white" opacity="0.9" />
        <rect x="2" y="4" width="12" height="8" fill="white" opacity="0.1" />
        <ellipse cx="8" cy="12" rx="6" ry="2" fill="white" opacity="0.7" />

        <circle cx="5" cy="7" r="1" fill="white" opacity="0.8" />
        <circle cx="8" cy="8" r="1" fill="white" opacity="0.9" />
        <circle cx="11" cy="7" r="1" fill="white" opacity="0.8" />
        <circle cx="6" cy="10" r="1" fill="white" opacity="0.7" />
        <circle cx="10" cy="10" r="1" fill="white" opacity="0.7" />

        <path
          d="M8 2 L8 14"
          stroke="white"
          strokeWidth="1"
          opacity="0.6"
          strokeDasharray="2,2"
        />
      </g>
    </svg>
  );
}
