import { LucideProps } from 'lucide-react';

export const ElbIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_Elastic-Load-Balancing_16</title>
    <desc>Created with Sketch.</desc>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="elb-gradient">
        <stop stopColor="#4B612C" offset="0%"></stop>
        <stop stopColor="#7AA116" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g
      id="Icon-Architecture/16/Arch_Elastic-Load-Balancing_16"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g id="Icon-Architecture-BG/16/Networking" fill="url(#elb-gradient)">
        <rect
          id="Rectangle"
          x="0"
          y="0"
          width="24"
          height="24"
          rx="4"
          ry="4"
        ></rect>
      </g>
      <path
        d="M12,16 C10.343,16 9,14.657 9,13 C9,11.343 10.343,10 12,10 C13.657,10 15,11.343 15,13 C15,14.657 13.657,16 12,16 M12,9 C9.791,9 8,10.791 8,13 C8,15.209 9.791,17 12,17 C14.209,17 16,15.209 16,13 C16,10.791 14.209,9 12,9 M6,8 C4.343,8 3,6.657 3,5 C3,3.343 4.343,2 6,2 C7.657,2 9,3.343 9,5 C9,6.657 7.657,8 6,8 M6,1 C3.791,1 2,2.791 2,5 C2,7.209 3.791,9 6,9 C8.209,9 10,7.209 10,5 C10,2.791 8.209,1 6,1 M18,8 C16.343,8 15,6.657 15,5 C15,3.343 16.343,2 18,2 C19.657,2 21,3.343 21,5 C21,6.657 19.657,8 18,8 M18,1 C15.791,1 14,2.791 14,5 C14,7.209 15.791,9 18,9 C20.209,9 22,7.209 22,5 C22,2.791 20.209,1 18,1 M6,22 C4.343,22 3,20.657 3,19 C3,17.343 4.343,16 6,16 C7.657,16 9,17.343 9,19 C9,20.657 7.657,22 6,22 M6,15 C3.791,15 2,16.791 2,19 C2,21.209 3.791,23 6,23 C8.209,23 10,21.209 10,19 C10,16.791 8.209,15 6,15 M18,22 C16.343,22 15,20.657 15,19 C15,17.343 16.343,16 18,16 C19.657,16 21,17.343 21,19 C21,20.657 19.657,22 18,22 M18,15 C15.791,15 14,16.791 14,19 C14,21.209 15.791,23 18,23 C20.209,23 22,21.209 22,19 C22,16.791 20.209,15 18,15"
        id="Elastic-Load-Balancing_Icon_16_Squid"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
