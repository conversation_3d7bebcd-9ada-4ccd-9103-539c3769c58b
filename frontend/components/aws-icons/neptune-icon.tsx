import { LucideProps } from 'lucide-react';

export const NeptuneIcon = (props: LucideProps) => (
  <svg
    width="24px"
    height="24px"
    viewBox="0 0 24 24"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <title>Icon-Architecture/16/Arch_Amazon-Neptune_16</title>
    <desc>Created with Sketch.</desc>
    <defs>
      <linearGradient x1="0%" y1="100%" x2="100%" y2="0%" id="neptune-gradient">
        <stop stopColor="#2E27AD" offset="0%"></stop>
        <stop stopColor="#527FFF" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g
      id="Icon-Architecture/16/Arch_Amazon-Neptune_16"
      stroke="none"
      strokeWidth="1"
      fill="none"
      fillRule="evenodd"
    >
      <g id="Icon-Architecture-BG/16/Database" fill="url(#neptune-gradient)">
        <rect
          id="Rectangle"
          x="0"
          y="0"
          width="24"
          height="24"
          rx="4"
          ry="4"
        ></rect>
      </g>
      <path
        d="M12,20 C7.589,20 4,16.411 4,12 C4,7.589 7.589,4 12,4 C16.411,4 20,7.589 20,12 C20,16.411 16.411,20 12,20 M12,3 C6.486,3 2,7.486 2,12 C2,16.514 6.486,21 12,21 C17.514,21 22,16.514 22,12 C22,7.486 17.514,3 12,3 M16.5,12 C16.5,14.485 14.485,16.5 12,16.5 C9.515,16.5 7.5,14.485 7.5,12 C7.5,9.515 9.515,7.5 12,7.5 C14.485,7.5 16.5,9.515 16.5,12 M18,12 C18,15.314 15.314,18 12,18 C8.686,18 6,15.314 6,12 C6,8.686 8.686,6 12,6 C15.314,6 18,8.686 18,12 M14,12 C14,13.105 13.105,14 12,14 C10.895,14 10,13.105 10,12 C10,10.895 10.895,10 12,10 C13.105,10 14,10.895 14,12 M15,12 C15,13.657 13.657,15 12,15 C10.343,15 9,13.657 9,12 C9,10.343 10.343,9 12,9 C13.657,9 15,10.343 15,12"
        id="Amazon-Neptune_Icon_16_Squid"
        fill="#FFFFFF"
      ></path>
    </g>
  </svg>
);
