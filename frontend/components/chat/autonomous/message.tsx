'use client';

import React, { useCallback, useMemo, useRef, useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { InterruptConfirmation } from '@/hooks/message-stream';
import { useMessageFeedback } from '@/hooks/use-message-feedback';
import { cn } from '@/lib/utils';
import { History, ThumbsDown, ThumbsUp } from 'lucide-react';

import { BotAvatar } from '../components/common/bot-icon';
import { CopyButton } from '../components/message/copy-button';
import { MessageContent } from '../components/message/message-content';
import { MessageAttachments } from '../components/message/message-attachments';
import { CHAT_CONFIG } from '../config/chat';
import { Message as ChatMessage, ContentPart } from '../types';
import { highlightMentions } from '../utils/mention-highlighting';
import { FeedbackDialog } from './feedback-dialog';

interface MessageProps {
  message: ChatMessage;
  index: number;
  onNextStepClick?: (content: string) => void;
  showToolCalls?: boolean;
  showThinking?: boolean;
  showStreaming?: boolean;
  onRestore?: (messageId: string, content: string) => void;
  confirmation?: InterruptConfirmation | null;
  contentDisplay?: React.ReactNode;
  disableDisplayComponents?: boolean;
  disableToolCalls?: boolean;
  groupChatOnly?: boolean;
  isSharedView?: boolean;
}

// eslint-disable-next-line react/display-name
const ProcessUserContent = React.memo(({ content }: { content: string }) => {
  const { highlightedText, hasMentions } = highlightMentions(content);
  return hasMentions ? (
    <div className="whitespace-pre-wrap">{highlightedText}</div>
  ) : (
    <div className="whitespace-pre-wrap">{content}</div>
  );
});

export const AutonomousMessage = React.memo(function AutonomousMessage({
  message,
  index,
  onNextStepClick,
  showToolCalls = CHAT_CONFIG.showToolCalls,
  showThinking = CHAT_CONFIG.showThinking,
  showStreaming = CHAT_CONFIG.showStreaming,
  onRestore,
  confirmation,
  contentDisplay,
  disableDisplayComponents = false,
  disableToolCalls = false,
  groupChatOnly = false,
  isSharedView = false,
}: MessageProps) {
  const isAI = message.role != 'user';

  // Memoize the formatted timestamp
  const formattedTimestamp = useMemo(() => {
    return message.timestamp
      ? new Date(message.timestamp).toLocaleString('en-US', {
          hour: 'numeric',
          minute: 'numeric',
          second: 'numeric',
          hour12: true,
          month: 'short',
          day: 'numeric',
        })
      : '';
  }, [message.timestamp]);

  // For AI messages, use structured fields directly
  const [openSections, setOpenSections] = React.useState<number[]>([]);
  const messageRef = useRef<HTMLDivElement>(null);
  const [isHovering, setIsHovering] = useState(false);

  // For user messages, just show the content
  // For AI messages, pass toolCalls, displayComponents, and empty contentParts
  const messageRenderProps = useMemo(() => {
    return {
      contentParts: isAI
        ? ([] as ContentPart[])
        : [{ type: 'content', content: message.content } as ContentPart],
      nextContent: null,
      toolCalls: isAI && !disableToolCalls ? message.toolCalls : undefined,
      showToolCalls: !disableToolCalls,
      isStreaming: showStreaming,
      openSections,
      onToggleSection: (i: number) =>
        setOpenSections((prev) =>
          prev.includes(i) ? prev.filter((x) => x !== i) : [...prev, i],
        ),
      onNextStepClick,
      displayComponents:
        isAI && !disableDisplayComponents
          ? message.displayComponents
          : undefined,
      confirmation,
      groupChatOnly,
      agentInfo: {
        name: message.role,
        role: message.role,
      },
    };
  }, [
    isAI,
    message.toolCalls,
    showStreaming,
    openSections,
    onNextStepClick,
    message.displayComponents,
    confirmation,
    disableToolCalls,
    disableDisplayComponents,
    groupChatOnly,
    message.content,
  ]);

  // Feedback functionality
  const { feedback, submitFeedback, isSubmitting } = useMessageFeedback(
    message.id,
  );
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);

  // Handle like immediately without dialog
  const handleLike = useCallback(() => {
    submitFeedback('good');
  }, [submitFeedback]);

  // Handle dislike - show simplified dialog
  const handleDislike = useCallback(() => {
    setFeedbackDialogOpen(true);
  }, []);

  const handleFeedbackSubmit = useCallback(
    (reason: string) => {
      submitFeedback('bad', reason);
      setFeedbackDialogOpen(false);
    },
    [submitFeedback],
  );

  // Reference for the message content
  const messageContentRef = useRef<HTMLDivElement>(null);

  // Detect if the message has display components or tables
  const hasRichContent = useMemo(() => {
    return (
      isAI &&
      ((message.displayComponents && message.displayComponents.length > 0) ||
        (message.toolCalls &&
          message.toolCalls.some(
            (tc) => tc.name === 'thought' || tc.name === 'group_chat',
          )))
    );
  }, [isAI, message.displayComponents, message.toolCalls]);

  return (
    <div
      className={cn(
        'flex w-full flex-col py-2',
        !isAI ? 'items-end' : 'items-start',
      )}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      ref={messageRef}
    >
      <div className="flex w-full items-center">
        <div
          className={cn(
            'relative flex flex-col gap-1',
            !isAI
              ? 'ml-auto max-w-[60%] items-end'
              : 'w-full flex-1 items-start',
          )}
        >
          {isAI && <BotAvatar role={message.role} variant="default" />}

          {/* Attachments for user messages - display above message */}
          {!isAI && message.attachments && message.attachments.length > 0 && (
            <div className="mb-2">
              <MessageAttachments attachments={message.attachments} />
            </div>
          )}

          <div
            className={cn(
              !isAI
                ? 'group relative rounded-3xl border border-primary/20 bg-primary/10 px-3 py-1.5 text-black dark:border-gray-700 dark:text-white'
                : 'group relative min-h-[40px] w-full max-w-4xl',
            )}
            ref={messageContentRef}
          >
            {/* Timestamp tooltip on hover */}
            <div
              className={cn(
                'absolute -top-12 right-2 rounded border border-border bg-popover/90 px-2 py-1 text-xs shadow-xs backdrop-blur-xs',
                'rounded-xl opacity-0 transition-opacity duration-200 group-hover:opacity-100',
                'pointer-events-none z-10',
              )}
            >
              {formattedTimestamp}
            </div>
            {isAI ? (
              <MessageContent {...messageRenderProps} />
            ) : (
              <div className="flex min-h-[32px] items-center text-sm">
                {contentDisplay || (
                  <ProcessUserContent content={message.content} />
                )}
              </div>
            )}
          </div>
          {/* Action buttons container */}
          <div
            className={cn(
              'mt-1 flex items-center gap-1 transition-opacity duration-200',
              isHovering ? 'opacity-100' : 'opacity-0',
              !isAI ? 'justify-end' : 'justify-start', // Align buttons based on sender
            )}
          >
            {/* Restore button - show for user messages */}
            {!isAI && index !== 0 && onRestore && !isSharedView && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-6 w-6 rounded-full text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                      onClick={() => onRestore(message.id, message.content)}
                    >
                      <History className="h-3.5 w-3.5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <p>Restore this message</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}

            {/* Like, Dislike, Copy buttons - show only for AI messages and when not streaming */}
            {isAI && !showStreaming && !isSharedView && (
              <>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="icon"
                        variant="ghost"
                        className={cn(
                          'h-6 w-6 rounded-full text-muted-foreground hover:bg-accent hover:text-accent-foreground',
                          feedback?.feedback_type === 'good' &&
                            'bg-green-50 text-green-600 hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50',
                        )}
                        onClick={handleLike}
                        disabled={isSubmitting}
                      >
                        <ThumbsUp className="h-3.5 w-3.5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <p>
                        {feedback?.feedback_type === 'good' ? 'Liked' : 'Like'}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="icon"
                        variant="ghost"
                        className={cn(
                          'h-6 w-6 rounded-full text-muted-foreground hover:bg-accent hover:text-accent-foreground',
                          feedback?.feedback_type === 'bad' &&
                            'bg-red-50 text-red-600 hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50',
                        )}
                        onClick={handleDislike}
                        disabled={isSubmitting}
                      >
                        <ThumbsDown className="h-3.5 w-3.5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <p>
                        {feedback?.feedback_type === 'bad'
                          ? 'Disliked'
                          : 'Dislike'}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-6 w-6 rounded-full text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                      >
                        <CopyButton
                          targetRef={messageContentRef}
                          text={isAI ? undefined : message.content}
                          isRichContent={false}
                          hasTable={false}
                          isFullBox={isAI}
                          className="h-6 w-6 rounded-full"
                          iconSize={3.5}
                        />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <p>Copy</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Simplified Feedback Dialog - only for dislike */}
      <FeedbackDialog
        open={feedbackDialogOpen}
        onOpenChange={setFeedbackDialogOpen}
        feedbackType="bad"
        onSubmit={handleFeedbackSubmit}
        isSubmitting={isSubmitting}
      />
    </div>
  );
});
