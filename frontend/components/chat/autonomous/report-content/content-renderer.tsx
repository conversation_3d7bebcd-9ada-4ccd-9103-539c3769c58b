import React from 'react';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/chat/components/message/charts';
import { processChartData } from '@/components/chat/components/message/charts/common/utils';
import { BasicTable } from '@/components/chat/components/message/tables';
import { TableColumnConfig } from '@/components/chat/components/message/tables/common/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import * as LucideIcons from 'lucide-react';
import { BarChart3, Database, FileText } from 'lucide-react';

import {
  CardSkeleton,
  ChartSkeleton,
  EmptyState,
  TableSkeleton,
} from './skeletons';

interface ChartContent {
  title: string;
  description?: string;
  chart_type: 'bar' | 'pie' | 'line' | 'area' | 'step_area' | 'radar';
  categories: string[];
  datasets: {
    data: number[];
    label?: string;
  }[];
  x_axis?: { title?: string; type?: string };
  y_axis?: { title?: string; type?: string };
  show_legend?: boolean;
  show_grid?: boolean;
  currency_format?: boolean;
  percentage_format?: boolean;
}

interface TableStructuredOutput {
  title: string;
  description?: string;
  columns: TableColumnConfig[];
  rows: string[][];
}

interface CardContent {
  title: string;
  description?: string;
  lucide_icon?: string;
  data?: string;
}

interface ContentItem {
  index: number;
  type: 'paragraph' | 'chart' | 'table' | 'card';
  content: string | ChartContent | TableStructuredOutput | CardContent;
}

interface ContentRendererProps {
  content: ContentItem;
  isLoading?: boolean;
}

const ContentRenderer: React.FC<ContentRendererProps> = ({
  content,
  isLoading = false,
}) => {
  // Safety check for content object
  if (!content || typeof content !== 'object') {
    return null;
  }

  const renderContent = () => {
    switch (content.type) {
      case 'paragraph':
        if (isLoading || !content.content) {
          return (
            <div className="prose prose-sm max-w-none animate-pulse">
              <div className="space-y-2">
                <div className="bg-muted h-4 w-full rounded"></div>
                <div className="bg-muted h-4 w-4/5 rounded"></div>
                <div className="bg-muted h-4 w-3/5 rounded"></div>
              </div>
            </div>
          );
        }
        return (
          <div className="prose prose-sm max-w-none">
            <p className="text-foreground leading-relaxed transition-opacity duration-300">
              {content.content as string}
            </p>
          </div>
        );

      case 'chart':
        if (isLoading || !content.content) {
          return <ChartSkeleton />;
        }

        const chartContent = content.content as ChartContent;

        if (
          !chartContent.categories?.length ||
          !chartContent.datasets?.length
        ) {
          return (
            <div className="my-6">
              <div className="bg-card rounded-xl border p-4">
                <EmptyState
                  title="Chart Data Not Available"
                  description="Chart data is being processed. The visualization will appear here once ready."
                  icon={
                    <BarChart3 className="text-muted-foreground/50 h-8 w-8" />
                  }
                />
              </div>
            </div>
          );
        }

        // Convert to frontend chart format
        const chartData = {
          labels: chartContent.categories,
          datasets: chartContent.datasets.map((dataset) => ({
            ...dataset,
            backgroundColor: undefined, // Let chart components handle colors
          })),
          x_axis: chartContent.x_axis,
          y_axis: chartContent.y_axis,
          display_options: {
            show_legend: chartContent.show_legend !== false,
            show_grid: chartContent.show_grid !== false,
            currency_format: chartContent.currency_format || false,
            percentage_format: chartContent.percentage_format || false,
          },
        };

        // Process the data for chart components
        const processedData = processChartData(chartData);

        // Render the appropriate chart component
        const renderChart = () => {
          const commonProps = {
            data: processedData,
            chartData: chartData,
            height: 350,
            title: chartContent.title,
          };

          switch (chartContent.chart_type) {
            case 'bar':
              return <BarChart {...commonProps} />;
            case 'line':
              return <LineChart {...commonProps} />;
            case 'pie':
              return <PieChart {...commonProps} height={400} />;
            case 'area':
              return <AreaChart {...commonProps} />;
            case 'step_area':
              return <StepAreaChart {...commonProps} />;
            case 'radar':
              return <RadarChart {...commonProps} height={400} />;
            default:
              return <BarChart {...commonProps} />;
          }
        };

        return (
          <div className="my-6">
            <div className="bg-card rounded-xl border p-6 shadow-sm">
              <div className="mb-4 flex items-center">
                <div className="flex items-center gap-3">
                  <div className="bg-primary/10 rounded-lg p-2">
                    <BarChart3 className="text-primary h-4 w-4" />
                  </div>
                  {chartContent.title && (
                    <h4 className="text-foreground text-lg font-semibold">
                      {chartContent.title}
                    </h4>
                  )}
                </div>
              </div>
              {chartContent.description && (
                <div className="bg-muted/30 mb-4 rounded-lg p-3">
                  <p className="text-muted-foreground text-sm">
                    {chartContent.description}
                  </p>
                </div>
              )}
              <div className="relative">{renderChart()}</div>
            </div>
          </div>
        );

      case 'table':
        if (isLoading || !content.content) {
          return <TableSkeleton />;
        }

        const tableContent = content.content as TableStructuredOutput;

        if (!tableContent.columns?.length || !tableContent.rows?.length) {
          return (
            <div className="my-6">
              <div className="bg-card rounded-xl border p-4">
                <EmptyState
                  title="Table Data Not Available"
                  description="Table data is being processed. The table will appear here once ready."
                  icon={
                    <Database className="text-muted-foreground/50 h-8 w-8" />
                  }
                />
              </div>
            </div>
          );
        }

        const tableData = {
          headers: tableContent.columns,
          rows: tableContent.rows,
        };

        return (
          <div className="my-6">
            <div className="bg-card rounded-xl border p-6 shadow-sm">
              <div className="mb-4 flex items-center">
                <div className="flex items-center gap-3">
                  <div className="bg-primary/10 rounded-lg p-2">
                    <Database className="text-primary h-4 w-4" />
                  </div>
                  {tableContent.title && (
                    <h4 className="text-foreground text-lg font-semibold">
                      {tableContent.title}
                    </h4>
                  )}
                </div>
              </div>
              {tableContent.description && (
                <div className="bg-muted/30 mb-4 rounded-lg p-3">
                  <p className="text-muted-foreground text-sm">
                    {tableContent.description}
                  </p>
                </div>
              )}
              <div className="bg-card overflow-hidden rounded-xl border-none dark:bg-transparent">
                <BasicTable data={tableData} showFullView />
              </div>
            </div>
          </div>
        );

      case 'card':
        if (isLoading || !content.content) {
          return <CardSkeleton />;
        }

        const cardContent = content.content as CardContent;

        // Get the icon component
        const getIcon = (iconName?: string) => {
          if (!iconName)
            return <FileText className="text-muted-foreground h-4 w-4" />;
          if (iconName.includes('-')) {
            iconName = iconName.replace('-', '');
            iconName = iconName.charAt(0).toUpperCase() + iconName.slice(1);
          }
          const IconComponent = (LucideIcons as any)[iconName];
          return IconComponent ? (
            <IconComponent className="text-muted-foreground h-4 w-4" />
          ) : (
            <FileText className="text-muted-foreground h-4 w-4" />
          );
        };

        if (!cardContent.title && !cardContent.data) {
          return <CardSkeleton />;
        }

        return (
          <Card className="from-card to-card/80 rounded-3xl border-none bg-gradient-to-br shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-foreground/80 text-sm font-medium">
                {cardContent.title || 'Metric'}
              </CardTitle>
              <div className="bg-muted/50 rounded-lg p-1.5">
                {getIcon(cardContent.lucide_icon)}
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-foreground mb-1 text-2xl font-bold">
                {cardContent.data || 'N/A'}
              </div>
              {cardContent.description && (
                <p className="text-muted-foreground text-xs leading-relaxed">
                  {cardContent.description}
                </p>
              )}
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  return <div className="content-item">{renderContent()}</div>;
};

export default ContentRenderer;
