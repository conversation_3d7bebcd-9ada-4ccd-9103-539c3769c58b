import React from 'react';

import { DollarSign, Lightbulb, Target, TrendingUp } from 'lucide-react';

import { EmptyState, ExecutiveSummarySkeleton } from './skeletons';

interface ExecutiveSummaryData {
  key_findings: string[];
  business_impact: string[];
}

interface ExecutiveSummaryProps {
  data?: ExecutiveSummaryData;
  isLoading?: boolean;
}

const ExecutiveSummary: React.FC<ExecutiveSummaryProps> = ({
  data,
  isLoading = false,
}) => {
  if (isLoading) {
    return <ExecutiveSummarySkeleton />;
  }

  if (!data || (!data.key_findings?.length && !data.business_impact?.length)) {
    return (
      <div className="mb-8">
        <h2 className="text-foreground mb-6 flex items-center gap-3 text-2xl font-bold">
          <div className="bg-primary/10 rounded-lg p-2">
            <Lightbulb className="text-primary h-5 w-5" />
          </div>
          Executive Summary
        </h2>
        <EmptyState
          title="Executive Summary Not Available"
          description="The executive summary is being generated. Key findings and business impact will appear here once the analysis is complete."
          icon={<Target className="text-muted-foreground/50 h-8 w-8" />}
        />
      </div>
    );
  }

  return (
    <div className="mb-8">
      <h2 className="text-foreground mb-6 flex items-center gap-3 text-2xl font-bold">
        <div className="bg-primary/10 rounded-lg p-2">
          <Lightbulb className="text-primary h-5 w-5" />
        </div>
        Executive Summary
      </h2>

      <div className="grid gap-6 md:grid-cols-1">
        {data.key_findings?.length > 0 && (
          <div className="border-primary/30 from-primary/5 to-primary/10 hover:shadow-primary/10 relative rounded-3xl border bg-linear-to-br p-6 transition-all duration-300 hover:shadow-lg">
            <div className="from-primary/5 absolute inset-0 rounded-3xl bg-linear-to-br to-transparent"></div>
            <div className="relative">
              <div className="mb-5 flex items-center gap-3">
                <div className="bg-primary/20 rounded-lg p-2">
                  <TrendingUp className="text-primary h-5 w-5" />
                </div>
                <h3 className="text-foreground text-lg font-semibold">
                  Key Findings
                </h3>
              </div>
              <ul className="space-y-4">
                {data.key_findings.map((finding, index) => (
                  <li key={index} className="group flex items-start gap-4">
                    <div className="bg-primary/20 group-hover:bg-primary/30 mt-0.5 flex h-6 w-6 shrink-0 items-center justify-center rounded-full transition-colors">
                      <span className="bg-primary h-2 w-2 rounded-full" />
                    </div>
                    <span className="text-foreground group-hover:text-foreground/90 flex-1 leading-relaxed transition-colors">
                      {finding}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {data.business_impact?.length > 0 && (
          <div className="relative rounded-3xl border border-green-200 bg-linear-to-br from-green-50 to-green-100/50 p-6 transition-all duration-300 hover:shadow-lg hover:shadow-green-100 dark:border-green-800 dark:from-green-950/30 dark:to-green-900/20 dark:hover:shadow-green-900/20">
            <div className="absolute inset-0 rounded-3xl bg-linear-to-br from-green-50/50 to-transparent dark:from-green-950/20 dark:to-transparent"></div>
            <div className="relative">
              <div className="mb-5 flex items-center gap-3">
                <div className="rounded-lg bg-green-200 p-2 dark:bg-green-800">
                  <DollarSign className="h-5 w-5 text-green-700 dark:text-green-300" />
                </div>
                <h3 className="text-foreground text-lg font-semibold">
                  Business Impact
                </h3>
              </div>
              <ul className="space-y-4">
                {data.business_impact.map((impact, index) => (
                  <li key={index} className="group flex items-start gap-4">
                    <div className="mt-0.5 flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-green-200 transition-colors group-hover:bg-green-300 dark:bg-green-800 dark:group-hover:bg-green-700">
                      <span className="h-2 w-2 rounded-full bg-green-600 dark:bg-green-400" />
                    </div>
                    <span className="text-foreground group-hover:text-foreground/90 flex-1 leading-relaxed transition-colors">
                      {impact}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExecutiveSummary;
