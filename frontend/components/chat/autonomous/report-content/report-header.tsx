import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Calendar, FileText } from 'lucide-react';

import { ReportHeaderSkeleton } from './skeletons';

interface ReportHeaderProps {
  title?: string;
  description?: string;
  createdAt?: string;
  status?: 'generating' | 'complete' | 'error';
  onExportPDF?: () => void;
  isLoading?: boolean;
}

const ReportHeader: React.FC<ReportHeaderProps> = ({
  title,
  description,
  createdAt,
  status = 'complete',
  onExportPDF,
  isLoading = false,
}) => {
  if (isLoading || !title) {
    return <ReportHeaderSkeleton />;
  }

  const handleExportPDF = () => {
    if (onExportPDF) {
      onExportPDF();
    }
  };

  const getStatusBadge = () => {
    switch (status) {
      case 'generating':
        return (
          <Badge
            variant="secondary"
            className="animate-pulse border-blue-200 bg-blue-50 text-blue-700"
          >
            <div className="mr-2 h-2 w-2 animate-ping rounded-full bg-blue-500"></div>
            Generating...
          </Badge>
        );
      case 'error':
        return (
          <Badge
            variant="destructive"
            className="border-red-200 bg-red-50 text-red-700"
          >
            <div className="mr-2 h-2 w-2 rounded-full bg-red-500"></div>
            Error
          </Badge>
        );
      case 'complete':
      default:
        return (
          <Badge
            variant="secondary"
            className="border-green-200 bg-green-50 text-green-700"
          >
            <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
            Complete
          </Badge>
        );
    }
  };

  return (
    <div className="border-border/50 from-background via-background to-muted/20 -mx-6 mb-8 rounded-xl border-b bg-linear-to-r p-6 pb-8">
      <div className="mb-6 flex items-start justify-between">
        <div className="flex-1 space-y-3">
          <div className="flex items-center gap-3">
            <div className="bg-primary/10 rounded-lg p-2">
              <FileText className="text-primary h-5 w-5" />
            </div>
          </div>
          <h1 className="from-foreground to-foreground/80 text-foreground bg-linear-to-r bg-clip-text text-4xl leading-tight font-bold">
            {title}
          </h1>
        </div>

        <div className="ml-6 flex items-center gap-3">
          {createdAt && (
            <div className="bg-muted/50 text-muted-foreground flex items-center gap-2 rounded-full border px-3 py-2 text-sm">
              <Calendar className="h-4 w-4" />
              <span>
                {new Date(createdAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </span>
            </div>
          )}
        </div>
      </div>

      {description && (
        <div className="bg-muted/30 rounded-lg p-4">
          <p className="text-muted-foreground text-lg leading-relaxed">
            {description}
          </p>
        </div>
      )}
    </div>
  );
};

export default ReportHeader;
