import React from 'react';

import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export const ReportHeaderSkeleton: React.FC = () => (
  <div className="mb-8 animate-pulse border-b border-border pb-6">
    <div className="mb-4 flex items-start justify-between">
      <div className="flex-1 space-y-3">
        <Skeleton className="h-8 w-3/4 bg-linear-to-r from-muted via-muted/50 to-muted" />
        <Skeleton className="h-4 w-1/2 bg-linear-to-r from-muted via-muted/50 to-muted" />
      </div>
      <div className="flex items-center gap-3">
        <Skeleton className="h-8 w-24 rounded-full" />
        <Skeleton className="h-8 w-28" />
      </div>
    </div>
    <div className="space-y-2">
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-4/5" />
    </div>
  </div>
);

export const ExecutiveSummarySkeleton: React.FC = () => (
  <div className="mb-8 animate-pulse">
    <div className="mb-6 flex items-center gap-3">
      <Skeleton className="h-7 w-48" />
    </div>

    <div className="grid gap-6 md:grid-cols-1">
      {/* Key Findings Skeleton */}
      <div className="rounded-3xl border border-primary/30 bg-primary/5 p-6">
        <div className="mb-4 flex items-center gap-2">
          <Skeleton className="h-5 w-5 rounded-full" />
          <Skeleton className="h-5 w-32" />
        </div>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-start gap-3">
              <Skeleton className="mt-2 h-2 w-2 shrink-0 rounded-full" />
              <Skeleton className="h-4 w-full" />
            </div>
          ))}
        </div>
      </div>

      {/* Business Impact Skeleton */}
      <div className="rounded-3xl border border-primary/30 bg-primary/5 p-6">
        <div className="mb-4 flex items-center gap-2">
          <Skeleton className="h-5 w-5 rounded-full" />
          <Skeleton className="h-5 w-36" />
        </div>
        <div className="space-y-3">
          {[1, 2].map((i) => (
            <div key={i} className="flex items-start gap-3">
              <Skeleton className="mt-2 h-2 w-2 shrink-0 rounded-full" />
              <Skeleton className="h-4 w-full" />
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

export const ChartSkeleton: React.FC = () => (
  <div className="my-6 animate-pulse">
    <div className="rounded-xl border bg-card p-4">
      <div className="space-y-4">
        <Skeleton className="h-6 w-64" />
        <Skeleton className="h-4 w-96" />
        <div className="relative">
          <Skeleton className="h-[350px] w-full rounded-lg" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="space-y-2 text-center">
              <div className="mx-auto h-16 w-16 animate-spin rounded-full border-4 border-primary/20 border-t-primary"></div>
              <p className="text-sm text-muted-foreground">Loading chart...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export const TableSkeleton: React.FC = () => (
  <div className="my-6 animate-pulse">
    <div className="space-y-4">
      <Skeleton className="h-6 w-48" />
      <Skeleton className="h-4 w-72" />
      <div className="overflow-hidden rounded-xl border bg-card">
        {/* Table Header */}
        <div className="border-b bg-muted/50 p-4">
          <div className="grid grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-4" />
            ))}
          </div>
        </div>
        {/* Table Rows */}
        {[1, 2, 3, 4, 5].map((row) => (
          <div key={row} className="border-b p-4 last:border-b-0">
            <div className="grid grid-cols-4 gap-4">
              {[1, 2, 3, 4].map((col) => (
                <Skeleton key={col} className="h-4" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export const CardSkeleton: React.FC = () => (
  <Card className="animate-pulse rounded-3xl border-none">
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
      <Skeleton className="h-4 w-32" />
      <Skeleton className="h-4 w-4 rounded-full" />
    </CardHeader>
    <CardContent className="pt-0">
      <Skeleton className="mb-2 h-8 w-24" />
      <Skeleton className="h-3 w-48" />
    </CardContent>
  </Card>
);

export const ReportSectionSkeleton: React.FC = () => (
  <div className="mb-12 animate-pulse">
    <div className="mb-6">
      <Skeleton className="mb-2 h-7 w-64" />
    </div>

    <div className="space-y-6">
      {/* Mixed content skeleton */}
      <div className="prose prose-sm max-w-none">
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-4/5" />
          <Skeleton className="h-4 w-3/5" />
        </div>
      </div>

      {/* Cards grid skeleton */}
      <div className="my-6 grid grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <CardSkeleton key={i} />
        ))}
      </div>

      <ChartSkeleton />
    </div>
  </div>
);

interface EmptyStateProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  icon,
}) => (
  <div className="flex flex-col items-center justify-center px-6 py-12 text-center">
    <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-muted/50">
      {icon || (
        <div className="h-8 w-8 rounded border-2 border-dashed border-muted-foreground/30"></div>
      )}
    </div>
    <h3 className="mb-2 text-lg font-semibold text-foreground">{title}</h3>
    <p className="max-w-md text-sm leading-relaxed text-muted-foreground">
      {description}
    </p>
  </div>
);
