'use client';

import { useEffect, useRef, useState } from 'react';

import { Icons } from '@/components/icons';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

// Interface for autocomplete suggestions
export interface AutocompleteSuggestion {
  id: string;
  title: string;
  description: string;
}

interface AutocompleteDropdownProps {
  isLoading: boolean;
  isVisible: boolean;
  suggestions: AutocompleteSuggestion[];
  onSelect: (suggestion: AutocompleteSuggestion) => void;
  onClose: () => void;
}

export function AutocompleteDropdown({
  isLoading,
  isVisible,
  suggestions,
  onSelect,
  onClose,
}: AutocompleteDropdownProps) {
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Handle ESC key press
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isVisible) {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isVisible, onClose]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        isVisible
      ) {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  return (
    <div className="mx-auto mt-4 h-full w-full max-w-[1000px] px-4">
      <div
        ref={dropdownRef}
        className={cn(
          'bg-background/95 backdrop-blur-xs',
          'border border-muted-foreground/10',
          'rounded-xl shadow-lg',
          'h-full overflow-hidden',
          'transition-all duration-200 ease-in-out',
          'animate-in fade-in-50 zoom-in-95',
        )}
      >
        {isLoading && (
          <div className="flex items-center justify-center p-3">
            <Icons.spinner className="h-4 w-4 animate-spin text-muted-foreground" />
          </div>
        )}
        {!isLoading && (
          <>
            <div className="flex items-center border-b border-muted-foreground/10 bg-muted/30 p-3">
              <span className="text-sm font-medium">Task suggestions</span>
              <div className="ml-2 rounded-full bg-primary/10 px-2 py-0.5 text-xs text-primary">
                {suggestions.length}{' '}
                {suggestions.length === 1 ? 'suggestion' : 'suggestions'}
              </div>
            </div>

            <div className="custom-scrollbar box-border max-h-[230px] overflow-y-auto">
              <TooltipProvider>
                {suggestions.map((suggestion, index) => (
                  <Tooltip key={suggestion.id} delayDuration={200}>
                    <TooltipTrigger asChild>
                      <div
                        className={cn(
                          'flex flex-col px-4 py-2.5',
                          'cursor-pointer transition-colors duration-150 hover:bg-muted/50',
                          'border-l-2',
                          'border-transparent',
                        )}
                        onClick={() => onSelect(suggestion)}
                      >
                        <div className="flex items-center gap-2">
                          <div className="flex h-5 w-5 shrink-0 items-center justify-center rounded-lg bg-primary/10">
                            <Icons.calendarCog className="h-3 w-3 text-primary" />
                          </div>
                          <span className="text-sm font-medium text-foreground">
                            {suggestion.title}
                          </span>
                        </div>
                        <div className="ml-7 mt-1">
                          <p className="line-clamp-2 text-xs text-muted-foreground">
                            {suggestion.description}
                          </p>
                        </div>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="max-w-xs">
                      <p className="mb-1 font-semibold">Task details:</p>
                      <p>{suggestion.description}</p>
                    </TooltipContent>
                  </Tooltip>
                ))}
              </TooltipProvider>
            </div>

            {suggestions.length === 0 && (
              <div className="flex flex-col items-center justify-center py-6 text-center">
                <div className="mb-2 flex h-10 w-10 items-center justify-center rounded-full bg-muted/50">
                  <Icons.calendarCog className="h-5 w-5 text-muted-foreground/70" />
                </div>
                <p className="text-sm text-muted-foreground">
                  No suggestions found
                </p>
                <p className="mt-1 max-w-[80%] text-xs text-muted-foreground/70">
                  Try a different search term
                </p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
