import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { PlayCircle } from 'lucide-react';

import { NextStep } from '../../utils/next-steps-parser';

interface NextStepsCardsProps {
  nextSteps: NextStep[];
  onStepClick: (stepText: string) => void;
}

export function NextStepsCards({
  nextSteps,
  onStepClick,
}: NextStepsCardsProps) {
  if (!nextSteps || nextSteps.length === 0) {
    return null;
  }

  return (
    <div className="mt-4 space-y-2">
      <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
        {nextSteps.map((step) => (
          <Card
            key={step.id}
            className="group cursor-pointer border-dashed pl-2 transition-all duration-200 hover:border-primary/30 hover:bg-primary/5"
            onClick={() => onStepClick(step.text)}
          >
            <CardContent className="p-3">
              <p className="line-clamp-2 text-xs leading-relaxed text-foreground/80">
                {step.text}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
