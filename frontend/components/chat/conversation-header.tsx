'use client';

import React, { useEffect, useRef, useState } from 'react';

import { AutonomousAgentsService } from '@/client';
import { useToast } from '@/hooks/use-toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface ConversationHeaderProps {
  conversationId?: string;
  conversationTitle?: string;
  conversationCreatedAt?: string;
  modelProvider?: string;
}

export function ConversationHeader({
  conversationId,
  conversationTitle,
}: ConversationHeaderProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(conversationTitle || '');
  const [currentTitle, setCurrentTitle] = useState(conversationTitle || '');
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const renameMutation = useMutation({
    mutationFn: ({
      conversationId,
      name,
    }: {
      conversationId: string;
      name: string;
    }) => AutonomousAgentsService.renameConversation({ conversationId, name }),
    onSuccess: (_, variables) => {
      // Update local title immediately
      setCurrentTitle(variables.name);
      // Invalidate conversations queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      // Also invalidate any conversation-specific queries
      queryClient.invalidateQueries({
        queryKey: ['conversation', conversationId],
      });
      toast({
        title: 'Success',
        description: 'Conversation renamed successfully',
      });
      setIsEditing(false);
    },
    onError: () => {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to rename conversation. Please try again.',
      });
      setEditValue(currentTitle); // Reset to current title
      setIsEditing(false);
    },
  });

  // Focus input when editing starts and auto-resize
  useEffect(() => {
    if (isEditing && inputRef.current) {
      const textarea = inputRef.current;
      textarea.focus();
      textarea.select();

      // Auto-resize textarea
      textarea.style.height = 'auto';
      textarea.style.height = textarea.scrollHeight + 'px';
    }
  }, [isEditing]);

  // Auto-resize textarea on value change
  useEffect(() => {
    if (isEditing && inputRef.current) {
      const textarea = inputRef.current;
      textarea.style.height = 'auto';
      textarea.style.height = textarea.scrollHeight + 'px';
    }
  }, [editValue, isEditing]);

  // Reset edit value and current title when conversation title changes
  useEffect(() => {
    setEditValue(conversationTitle || '');
    setCurrentTitle(conversationTitle || '');
  }, [conversationTitle]);

  if (!conversationId || !currentTitle) {
    return null;
  }

  const handleClick = () => {
    setIsEditing(true);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      setEditValue(currentTitle);
      setIsEditing(false);
    }
  };

  const handleSave = () => {
    const trimmedValue = editValue.trim();
    if (trimmedValue && trimmedValue !== currentTitle && conversationId) {
      renameMutation.mutate({ conversationId, name: trimmedValue });
    } else {
      setEditValue(currentTitle);
      setIsEditing(false);
    }
  };

  return (
    <div className="grid max-w-[300px] items-start">
      {/* Sizer element to ensure consistent width */}
      <div className="invisible col-start-1 row-start-1 whitespace-pre-wrap p-0 text-sm font-medium leading-relaxed">
        {editValue || ' '}
      </div>

      {isEditing ? (
        <textarea
          ref={inputRef}
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onKeyDown={handleKeyDown}
          onBlur={handleSave}
          className="col-start-1 row-start-1 w-full resize-none break-words border-none bg-transparent p-0 text-sm font-medium leading-relaxed text-foreground outline-hidden"
          disabled={renameMutation.isPending}
          rows={1}
          style={{ minHeight: 'auto', height: 'auto' }}
        />
      ) : (
        <span
          onClick={handleClick}
          className="col-start-1 row-start-1 w-full cursor-pointer break-words text-sm font-medium leading-relaxed text-foreground transition-colors hover:text-primary"
          title={currentTitle}
        >
          {currentTitle}
        </span>
      )}
    </div>
  );
}
