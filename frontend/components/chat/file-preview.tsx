'use client';

import React, { useState } from 'react';
import { X, File, Image, FileText, Download, Eye } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/hooks/use-toast';
import { AttachmentsService } from '@/client';
import { UploadingFile, FileUploadStatus } from '@/hooks/use-file-upload';
import { attachmentUrlCache } from '@/lib/attachment-url-cache';
import { SimplePDFViewer } from '@/app/(dashboard)/kb/components/SimplePDFViewer';

interface FilePreviewProps {
  file: UploadingFile;
  onRemove: (fileId: string) => void;
  showPreview?: boolean;
}

// Get file type category
function getFileCategory(mimeType: string): 'image' | 'pdf' | 'text' | 'document' | 'other' {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType === 'application/pdf') return 'pdf';
  if (mimeType.startsWith('text/') || mimeType === 'application/json') return 'text';
  if (mimeType.includes('word') || mimeType.includes('sheet')) return 'document';
  return 'other';
}

// Get file icon based on type
function getFileIcon(mimeType: string) {
  const category = getFileCategory(mimeType);
  
  switch (category) {
    case 'image':
      return <Image className="h-4 w-4" />;
    case 'pdf':
    case 'document':
      return <FileText className="h-4 w-4" />;
    case 'text':
      return <FileText className="h-4 w-4" />;
    default:
      return <File className="h-4 w-4" />;
  }
}

// Get status color
function getStatusColor(status: FileUploadStatus): string {
  switch (status) {
    case 'completed':
      return 'bg-green-500';
    case 'failed':
      return 'bg-red-500';
    case 'uploading':
    case 'processing':
      return 'bg-blue-500';
    case 'validating':
      return 'bg-yellow-500';
    default:
      return 'bg-gray-500';
  }
}

// Get status text
function getStatusText(status: FileUploadStatus, progress?: number): string {
  switch (status) {
    case 'validating':
      return 'Validating';
    case 'uploading':
      return 'Uploading';
    case 'processing':
      return `${progress || 0}%`;
    case 'completed':
      return 'Ready';
    case 'failed':
      return 'Failed';
    default:
      return 'Idle';
  }
}

// Format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
}

export function FilePreview({ file, onRemove, showPreview = true }: FilePreviewProps) {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);
  const [textContent, setTextContent] = useState<string | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);

  const fileCategory = getFileCategory(file.file.type);
  const canPreview = fileCategory === 'image' || fileCategory === 'text' || fileCategory === 'pdf';
  const isCompleted = file.status === 'completed';

  // Generate thumbnail for images
  React.useEffect(() => {
    if (fileCategory === 'image') {
      const url = URL.createObjectURL(file.file);
      setThumbnailUrl(url);

      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [file.file, fileCategory]);

  // Handle preview
  const handlePreview = async () => {
    if (!canPreview || !showPreview) return;

    setIsLoadingPreview(true);

    try {
      if (fileCategory === 'image') {
        // For images, create object URL from the file
        const url = URL.createObjectURL(file.file);
        setImagePreviewUrl(url);
        setPreviewOpen(true);
      } else if (fileCategory === 'text' && file.file.size < 1024 * 1024) { // Max 1MB for text preview
        // For text files, read content directly
        const text = await file.file.text();
        setTextContent(text);
        setPreviewOpen(true);
      } else if (fileCategory === 'pdf') {
        if (isCompleted && file.attachmentId) {
          // For completed PDFs, get download URL from backend
          try {
            const response = await AttachmentsService.getAttachmentDownloadUrl({
              attachmentId: file.attachmentId
            });
            setPdfUrl(response.download_url);
            setPreviewOpen(true);
          } catch (error) {
            toast({
              title: 'PDF Preview Error',
              description: 'Failed to load PDF for preview.',
              variant: 'destructive',
            });
          }
        } else {
          // For PDFs still being processed, create object URL from the file
          const url = URL.createObjectURL(file.file);
          setPdfUrl(url);
          setPreviewOpen(true);
        }
      }
    } catch (error) {
      toast({
        title: 'Preview Error',
        description: 'Failed to load file preview.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingPreview(false);
    }
  };

  // Handle download (for completed files)
  const handleDownload = async () => {
    if (!isCompleted || !file.attachmentId) return;

    try {
      const downloadUrl = await attachmentUrlCache.getDownloadUrl(file.attachmentId);

      // Open download URL in new tab
      window.open(downloadUrl, '_blank');
    } catch (error) {
      toast({
        title: 'Download Error',
        description: 'Failed to generate download link.',
        variant: 'destructive',
      });
    }
  };

  // Cleanup URLs when component unmounts
  const handleClosePreview = () => {
    if (imagePreviewUrl) {
      URL.revokeObjectURL(imagePreviewUrl);
      setImagePreviewUrl(null);
    }
    if (pdfUrl && !isCompleted) {
      // Only revoke object URLs we created, not presigned URLs from backend
      URL.revokeObjectURL(pdfUrl);
    }
    setPdfUrl(null);
    setTextContent(null);
    setPreviewOpen(false);
  };

  return (
    <>
      <div className="relative group">
        {/* Square Container */}
        <div className="w-20 h-20 border rounded-lg bg-card overflow-hidden relative">
          {/* Image Preview or File Icon */}
          {fileCategory === 'image' && thumbnailUrl ? (
            <img
              src={thumbnailUrl}
              alt={file.file.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-muted">
              {getFileIcon(file.file.type)}
            </div>
          )}

          {/* Status Badge */}
          <div className="absolute top-1 right-1">
            <Badge
              variant="outline"
              className={`text-xs px-1 py-0 h-4 ${getStatusColor(file.status)} text-white border-none`}
            >
              {getStatusText(file.status, file.progress)}
            </Badge>
          </div>

          {/* Progress Overlay */}
          {(file.status === 'uploading' || file.status === 'processing') && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
              <div className="text-white text-xs font-medium">
                {file.progress}%
              </div>
            </div>
          )}

          {/* Error Overlay */}
          {file.status === 'failed' && (
            <div className="absolute inset-0 bg-red-500/80 flex items-center justify-center">
              <div className="text-white text-xs text-center p-1">
                Failed
              </div>
            </div>
          )}

          {/* Action Buttons Overlay */}
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-1">
            {/* Preview Button */}
            {canPreview && showPreview && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handlePreview}
                disabled={isLoadingPreview}
                className="h-8 w-8 p-0 text-white hover:bg-white/20"
              >
                <Eye className="h-4 w-4" />
              </Button>
            )}

            {/* Download Button (only for completed files) */}
            {isCompleted && file.attachmentId && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDownload}
                className="h-8 w-8 p-0 text-white hover:bg-white/20"
              >
                <Download className="h-4 w-4" />
              </Button>
            )}

            {/* Remove Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onRemove(file.id)}
              className="h-6 w-6 p-0 text-white hover:bg-red-500/50"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {/* File Name */}
        <div className="mt-1 text-xs text-center text-muted-foreground truncate max-w-20" title={file.file.name}>
          {file.file.name}
        </div>

        {/* File Size */}
        <div className="text-xs text-center text-muted-foreground">
          {formatFileSize(file.file.size)}
        </div>

        {/* Error Message */}
        {file.status === 'failed' && file.error && (
          <div className="text-xs text-red-500 text-center mt-1 max-w-20 truncate" title={file.error}>
            {file.error}
          </div>
        )}
      </div>

      {/* Preview Dialog */}
      <Dialog open={previewOpen} onOpenChange={handleClosePreview}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>{file.file.name}</DialogTitle>
          </DialogHeader>
          
          <div className="overflow-auto">
            {/* Image Preview */}
            {imagePreviewUrl && (
              <div className="flex justify-center">
                <img
                  src={imagePreviewUrl}
                  alt={file.file.name}
                  className="max-w-full max-h-[60vh] object-contain"
                />
              </div>
            )}

            {/* PDF Preview */}
            {pdfUrl && (
              <div className="h-[60vh]">
                <SimplePDFViewer
                  url={pdfUrl}
                  fileName={file.file.name}
                  className="h-full"
                />
              </div>
            )}

            {/* Text Preview */}
            {textContent && (
              <div className="bg-muted p-4 rounded-lg">
                <pre className="whitespace-pre-wrap text-sm font-mono overflow-auto max-h-[50vh]">
                  {textContent}
                </pre>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

// Component for displaying multiple file previews
interface FilePreviewListProps {
  files: UploadingFile[];
  onRemoveFile: (fileId: string) => void;
  onClearAll: () => void;
  showPreview?: boolean;
}

export function FilePreviewList({
  files,
  onRemoveFile,
  onClearAll,
  showPreview = true
}: FilePreviewListProps) {
  if (files.length === 0) return null;

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium">
          Attached Files ({files.length})
        </span>
        {files.length > 1 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearAll}
            className="text-xs text-muted-foreground hover:text-destructive"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Grid Layout for Square Previews */}
      <div className="flex flex-wrap gap-3 max-h-48 overflow-y-auto">
        {files.map((file) => (
          <FilePreview
            key={file.id}
            file={file}
            onRemove={onRemoveFile}
            showPreview={showPreview}
          />
        ))}
      </div>
    </div>
  );
}
