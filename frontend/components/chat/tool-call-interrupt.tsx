import { useCallback, useEffect, useLayoutEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { SyntaxHighlighter } from '@/components/ui/code-block';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { TooltipProvider } from '@/components/ui/tooltip';
import { InterruptConfirmation } from '@/hooks/message-stream';
import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { AlertTriangle, ChevronDown, ChevronUp } from 'lucide-react';

import { ColoredToolIcon } from './components/colored-tool-icon';
import { getToolType } from './components/tool-config';

interface ConfirmationProps {
  confirmation: InterruptConfirmation | null;
}

// Operation status configuration
const STATUS_CONFIG = {
  waiting: {
    icon: <AlertTriangle className="h-3.5 w-3.5" />,
    color: 'text-amber-600 dark:text-amber-400 border-none',
    bgColor: 'bg-amber-500/10 dark:bg-amber-500/20',
    border: 'border-amber-200/60 dark:border-amber-800/60',
    ariaLabel: 'Operation awaiting approval',
    label: 'Awaiting Approval',
  },
};

// Utility functions like in tool-call-renderer
// function isJsonContent(content: any): boolean {
//   if (!content) return false;
//   if (typeof content === 'object') return true;
//   if (typeof content === 'string') {
//     try {
//       JSON.parse(content);
//       return true;
//     } catch {
//       return false;
//     }
//   }
//   return false;
// }

// function CopyButton({
//   getContent,
//   label,
// }: {
//   getContent: () => string;
//   label: string;
// }) {
//   const [copied, setCopied] = useState(false);
//   const handleCopy = async () => {
//     try {
//       await navigator.clipboard.writeText(getContent());
//       setCopied(true);
//       setTimeout(() => setCopied(false), 1200);
//     } catch {}
//   };
//   return (
//     <TooltipProvider>
//       <Tooltip>
//         <TooltipTrigger asChild>
//           <Button
//             type="button"
//             size="icon"
//             variant="ghost"
//             className="p-0 ml-1 w-6 h-6"
//             aria-label={`Copy ${label}`}
//             onClick={(e) => {
//               e.stopPropagation();
//               handleCopy();
//             }}
//             tabIndex={0}
//           >
//             {copied ? (
//               <Check className="w-4 h-4 text-green-600" />
//             ) : (
//               <Copy className="w-4 h-4" />
//             )}
//           </Button>
//         </TooltipTrigger>
//         <TooltipContent>{copied ? 'Copied!' : `Copy ${label}`}</TooltipContent>
//       </Tooltip>
//     </TooltipProvider>
//   );
// }

export function InlineConfirmation({ confirmation }: ConfirmationProps) {
  const [isExpandedState, setIsExpandedState] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [cancelMessage, setCancelMessage] = useState('');
  const [isDetailsExpanded, setIsDetailsExpanded] = useState(false);
  const [showDetailsExpand, setShowDetailsExpand] = useState(false);
  // const { resolvedTheme } = useTheme();
  // const isDarkMode = resolvedTheme === 'dark';

  // const detailsRef = useRef<HTMLDivElement>(null);
  const [detailsNode, setDetailsNode] = useState<HTMLDivElement | null>(null);
  const detailsCallbackRef = useCallback((node: HTMLDivElement | null) => {
    setDetailsNode(node);
  }, []);

  useLayoutEffect(() => {
    const checkOverflow = () => {
      if (detailsNode) {
        setShowDetailsExpand(
          detailsNode.scrollHeight > detailsNode.clientHeight,
        );
      }
    };

    const timerId = setTimeout(checkOverflow, 100);
    window.addEventListener('resize', checkOverflow);

    return () => {
      clearTimeout(timerId);
      window.removeEventListener('resize', checkOverflow);
    };
  }, [detailsNode, isDetailsExpanded, confirmation]);

  // Use an effect to properly handle appearance/disappearance
  useEffect(() => {
    if (confirmation) {
      setIsVisible(true);
    } else {
      // Add a slight delay before hiding to allow for animations
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [confirmation]);

  // If we have no confirmation and it's not visible, render nothing
  if (!confirmation && !isVisible) return null;

  // If confirmation was cleared but we're still visible during transition,
  // use the last known confirmation data
  const safeConfirmation = confirmation || {
    message: { value: 'Operation in progress' },
    onConfirm: () => {},
    onCancel: () => {},
  };

  // Parse the message content to extract different parts
  const parseMessage = (content: string) => {
    const lines = content.split('\n');

    // Extract operation name (tool_name)
    const operationIndex = lines.findIndex((line) =>
      line.startsWith('Operation:'),
    );
    const operation =
      operationIndex !== -1 && operationIndex + 1 < lines.length
        ? lines[operationIndex + 1].trim()
        : 'Unknown Operation';

    // Extract tool_args as script
    const detailsIndex = lines.findIndex((line) => line.startsWith('Details:'));
    let toolArgsJson = '';
    let script = '';
    let reasoning = null;

    if (detailsIndex !== -1 && detailsIndex + 1 < lines.length) {
      // Get the tool_args section
      const nextSectionIndex = lines
        .slice(detailsIndex + 1)
        .findIndex(
          (line) => line.trim() === '' || line.includes('Please confirm'),
        );
      const endIndex =
        nextSectionIndex !== -1
          ? detailsIndex + 1 + nextSectionIndex
          : lines.length;

      const toolArgs = lines
        .slice(detailsIndex + 1, endIndex)
        .join('\n')
        .trim();
      toolArgsJson = toolArgs;

      try {
        // Handle different possible formats
        let parsedArgs;

        // Try to parse as-is first
        try {
          parsedArgs = JSON.parse(toolArgs);
        } catch {
          // If that fails, try preprocessing
          const preprocessedArgs = toolArgs
            .replace(/'/g, '"') // Replace single quotes with double quotes
            .replace(/\\"/g, '\\"'); // Preserve escaped quotes
          parsedArgs = JSON.parse(preprocessedArgs);
        }

        // Extract reasoning from the JSON args
        reasoning = parsedArgs.reasoning || null;

        // For AWS tools, extract the script field
        const isAwsTool = operation.startsWith('aws_');
        if (
          isAwsTool &&
          parsedArgs &&
          typeof parsedArgs === 'object' &&
          'script' in parsedArgs
        ) {
          script = String(parsedArgs.script);
        } else {
          // For non-AWS tools, show the formatted JSON (excluding reasoning if it exists)
          const { reasoning: _, ...argsWithoutReasoning } = parsedArgs;
          script = JSON.stringify(
            Object.keys(argsWithoutReasoning).length > 0
              ? argsWithoutReasoning
              : parsedArgs,
            null,
            2,
          );
        }
      } catch {
        // If parsing fails, try to extract script and reasoning manually
        if (toolArgs.includes("'script':") || toolArgs.includes('"script":')) {
          // Try to extract script content manually with better regex
          let scriptMatch = null;

          // Try single-quoted script first (most common in this format)
          const singleQuotePattern = /'script':\s*'((?:[^'\\]|\\.)*)'/s;
          scriptMatch = toolArgs.match(singleQuotePattern);

          if (!scriptMatch) {
            // Try double-quoted script
            const doubleQuotePattern = /'script':\s*"((?:[^"\\]|\\.)*)"/s;
            scriptMatch = toolArgs.match(doubleQuotePattern);
          }

          if (!scriptMatch) {
            // Try with "script" key (double quotes around key)
            const doubleKeyPattern = /"script":\s*"((?:[^"\\]|\\.)*)"/s;
            scriptMatch = toolArgs.match(doubleKeyPattern);
          }

          if (!scriptMatch) {
            // Try with "script" key and single quote value
            const mixedPattern = /"script":\s*'((?:[^'\\]|\\.)*)'/s;
            scriptMatch = toolArgs.match(mixedPattern);
          }

          if (scriptMatch) {
            script = scriptMatch[1]
              .replace(/\\n/g, '\n')
              .replace(/\\'/g, "'")
              .replace(/\\"/g, '"')
              .replace(/\\t/g, '\t');
          }
        }

        if (
          toolArgs.includes("'reasoning':") ||
          toolArgs.includes('"reasoning":')
        ) {
          // Try to extract reasoning manually with better regex that handles nested quotes
          // Look for 'reasoning': "..." or 'reasoning': '...'
          let reasoningMatch = null;

          // Try double-quoted reasoning first (most common)
          const doubleQuotePattern = /'reasoning':\s*"((?:[^"\\]|\\.)*)"/;
          reasoningMatch = toolArgs.match(doubleQuotePattern);

          if (!reasoningMatch) {
            // Try single-quoted reasoning
            const singleQuotePattern = /'reasoning':\s*'((?:[^'\\]|\\.)*)'/;
            reasoningMatch = toolArgs.match(singleQuotePattern);
          }

          if (!reasoningMatch) {
            // Try with "reasoning" key (double quotes around key)
            const doubleKeyPattern = /"reasoning":\s*"((?:[^"\\]|\\.)*)"/;
            reasoningMatch = toolArgs.match(doubleKeyPattern);
          }

          if (!reasoningMatch) {
            // Try with "reasoning" key and single quote value
            const mixedPattern = /"reasoning":\s*'((?:[^'\\]|\\.)*)'/;
            reasoningMatch = toolArgs.match(mixedPattern);
          }

          if (reasoningMatch) {
            reasoning = reasoningMatch[1]
              .replace(/\\'/g, "'")
              .replace(/\\"/g, '"')
              .replace(/\\n/g, '\n')
              .replace(/\\t/g, '\t');
          }
        }

        // If script extraction failed, use the raw tool_args but clean it up
        if (!script) {
          script = toolArgs;
        }
      }
    }

    // Clean up the script
    script = script
      .replace(/\\\\n/g, '\n')
      .replace(/\\n/g, '\n')
      .replace(/\\\\/g, '\\')
      .replace(/\\"/g, '"')
      .replace(/\\'/g, "'");

    return {
      operation, // This will be the tool_name
      script, // This will be the formatted script/args
      reasoning, // Extracted reasoning
      toolArgsJson, // Raw JSON for parsing
    };
  };

  const { operation, script, reasoning } = parseMessage(
    safeConfirmation.message.value,
  );

  // Use the same tool type detection as tool-call-renderer
  const toolType = getToolType(operation);
  const status = STATUS_CONFIG.waiting;
  const isAwsTool = operation.startsWith('aws_');

  // Determine if reasoning exists and has content
  const hasReasoning = !!(
    reasoning &&
    typeof reasoning === 'string' &&
    reasoning.trim()
  );

  const handleConfirm = () => {
    setIsVisible(false);
    safeConfirmation.onConfirm();
  };

  const handleCancel = () => {
    setShowCancelDialog(true);
  };

  const handleCancelConfirm = () => {
    try {
      // Store the message before clearing states
      const message = cancelMessage.trim();

      // Clear UI states
      setIsVisible(false);
      setShowCancelDialog(false);
      setCancelMessage('');

      // Call onCancel with the stored message
      safeConfirmation.onCancel(message);
    } catch (error) {
      console.error('Error in handleCancelConfirm:', error);
    }
  };

  const handleCancelDialogClose = () => {
    setShowCancelDialog(false);
    setCancelMessage('');
  };

  const toggleExpand = () => {
    setIsExpandedState(!isExpandedState);
  };

  return (
    <>
      <div
        className={cn(
          'mx-1 my-2 overflow-hidden rounded-xl border border-black/10 will-change-transform dark:border-white/10 md:mx-0 md:my-4 md:rounded-2xl',
          'transition-shadow duration-200',
          isExpandedState ? 'shadow-md' : 'hover:shadow-xs',
          isVisible ? 'opacity-100' : 'pointer-events-none opacity-0',
        )}
        data-confirmation-dialog="true"
      >
        {/* Header Button */}
        <TooltipProvider>
          <Button
            variant="ghost"
            className={cn(
              'flex h-auto w-full items-center rounded-none px-2 py-2 md:px-3 md:py-2',
              'transition-colors hover:bg-muted/30 hover:text-foreground',
              'group focus-visible:ring-1 focus-visible:ring-primary/40',
              isExpandedState && 'bg-muted/20',
            )}
            onClick={toggleExpand}
            aria-expanded={isExpandedState}
          >
            <div className="flex w-full min-w-0 items-center justify-between">
              <div className="flex min-w-0 flex-1 items-center gap-2 overflow-hidden">
                {/* Operation Icon */}
                <ColoredToolIcon
                  icon={toolType.icon}
                  iconColor={toolType.iconColor}
                  bgColor={toolType.bgColor}
                  size={28}
                  className="shrink-0 rounded-lg p-1 md:rounded-xl md:p-1.5"
                />

                {/* Operation Info */}
                <div className="flex min-w-0 flex-1 flex-col items-start overflow-hidden">
                  <div className="flex w-full items-center gap-1 md:gap-2">
                    <span className="flex-1 truncate text-left text-xs font-medium md:text-sm">
                      {toolType.display}
                    </span>
                    <Badge
                      variant="outline"
                      className={cn(
                        'h-4 shrink-0 border-none px-1 text-xs md:h-5',
                        status.color,
                        'transition-colors',
                      )}
                    >
                      <span className="flex items-center gap-0.5 md:gap-1">
                        {status.icon}
                        <span className="text-[9px] font-medium md:text-[10px]">
                          {status.label}
                        </span>
                      </span>
                    </Badge>
                  </div>

                  {/* Reasoning */}
                  {hasReasoning && (
                    <div className="w-full text-left text-xs text-muted-foreground">
                      <span className="line-clamp-1 italic md:line-clamp-1">
                        {reasoning}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Expand/Collapse */}
              <ChevronDown
                className={cn(
                  'ml-2 h-4 w-4 shrink-0 text-muted-foreground/60 transition-transform',
                  'group-hover:text-muted-foreground',
                  isExpandedState && 'rotate-180',
                )}
              />
            </div>
          </Button>
        </TooltipProvider>

        {/* Action Buttons Section - Always Visible */}
        <div className="border-t border-border/40 bg-muted/5 p-2 md:p-3">
          <div className="flex flex-col justify-between gap-3 sm:flex-row sm:items-center">
            <p className="text-sm text-foreground/80">
              Are you sure you want to proceed with this operation?
            </p>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleCancel}
                size="sm"
                className="h-8 text-sm"
              >
                Cancel
              </Button>
              <Button
                variant="default"
                onClick={handleConfirm}
                size="sm"
                className="h-8 bg-amber-500 text-sm text-white hover:bg-amber-600 dark:bg-amber-600/90 dark:text-white/95 dark:hover:bg-amber-600"
              >
                Proceed
              </Button>
            </div>
          </div>
        </div>

        {/* Content (when expanded) */}
        <AnimatePresence>
          {isExpandedState && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.1 }}
              className="overflow-hidden"
            >
              <div className="space-y-2 border-t border-border/40 bg-muted/5 p-2 md:space-y-3 md:p-3">
                {/* Reasoning Section */}
                {hasReasoning && (
                  <div className="space-y-1 md:space-y-1.5">
                    <div className="flex items-center gap-1 text-xs font-medium text-muted-foreground">
                      Reasoning
                    </div>
                    <div className="overflow-hidden rounded-lg border border-border/60 bg-card/30 md:rounded-xl">
                      <div
                        className="custom-scrollbar max-h-[150px] overflow-x-auto overflow-y-auto p-3 font-mono text-xs md:max-h-[200px]"
                        style={{
                          wordBreak: 'break-word',
                          whiteSpace: 'pre-wrap',
                        }}
                      >
                        {reasoning}
                      </div>
                    </div>
                  </div>
                )}

                {/* Operation Details Section */}
                <div className="space-y-1 md:space-y-1.5">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1 text-xs font-medium text-muted-foreground">
                      {isAwsTool ? 'Script' : 'Arguments'}
                      {(showDetailsExpand || isDetailsExpanded) && (
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          className="ml-1 flex items-center gap-1 px-1.5 py-0.5 text-xs md:ml-2 md:px-2 md:py-1"
                          aria-label={
                            isDetailsExpanded
                              ? `Collapse ${isAwsTool ? 'Script' : 'Arguments'}`
                              : `Expand ${isAwsTool ? 'Script' : 'Arguments'}`
                          }
                          onClick={(e) => {
                            e.stopPropagation();
                            setIsDetailsExpanded((v) => !v);
                          }}
                          tabIndex={0}
                        >
                          {isDetailsExpanded ? (
                            <ChevronUp className="h-3.5 w-3.5 md:h-4 md:w-4" />
                          ) : (
                            <ChevronDown className="h-3.5 w-3.5 md:h-4 md:w-4" />
                          )}
                          <span className="hidden sm:inline md:inline">
                            {isDetailsExpanded ? 'Collapse' : 'Expand'}
                          </span>
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="overflow-hidden rounded-lg border border-border/60 md:rounded-xl">
                    <div
                      ref={detailsCallbackRef}
                      className={cn(
                        'overflow-x-auto overflow-y-auto bg-muted/30',
                        isDetailsExpanded
                          ? 'max-h-none'
                          : 'max-h-[200px] md:max-h-[300px]',
                        'custom-scrollbar',
                      )}
                    >
                      <SyntaxHighlighter
                        language={isAwsTool ? 'bash' : 'json'}
                        className="min-w-0 text-xs"
                        wrapLongLines={false}
                        customStyle={{
                          margin: 0,
                          padding: '0.75rem',
                          background: 'transparent',
                          overflowX: 'auto',
                          whiteSpace: 'pre',
                        }}
                      >
                        {script}
                      </SyntaxHighlighter>
                    </div>
                  </div>
                </div>

                {/* Responsive Notice */}
                <div className="mt-1 block sm:hidden">
                  <div className="text-xs text-muted-foreground">
                    For best experience, view in landscape mode on small
                    screens.
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Cancel Confirmation Dialog */}
      <Dialog open={showCancelDialog} onOpenChange={handleCancelDialogClose}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Cancel Operation</DialogTitle>
            <DialogDescription>
              Optionally provide a reason for canceling this operation.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="cancel-message">Cancel Message (Optional)</Label>
              <Input
                id="cancel-message"
                placeholder="Enter your reason for canceling... (optional)"
                value={cancelMessage}
                onChange={(e) => setCancelMessage(e.target.value)}
                className="col-span-3"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleCancelConfirm();
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelDialogClose}>
              Back
            </Button>
            <Button variant="destructive" onClick={handleCancelConfirm}>
              Confirm Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
