import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Check, Copy } from 'lucide-react';
import { useTheme } from 'next-themes';
import { type Highlighter, getHighlighter } from 'shiki';

interface CodeBlockProps {
  code: string;
  language?: string;
  className?: string;
  showCopyButton?: boolean;
  title?: string;
  showLineNumbers?: boolean;
  wrapLongLines?: boolean;
}

export function CodeBlock({
  code,
  language = 'text',
  className,
  showCopyButton = true,
  title,
  showLineNumbers = false,
  wrapLongLines = true,
}: CodeBlockProps) {
  const { theme } = useTheme();
  const [highlighter, setHighlighter] = useState<Highlighter | null>(null);
  const [highlightedCode, setHighlightedCode] = useState<string>('');
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    const initHighlighter = async () => {
      try {
        const hl = await getHighlighter({
          themes: ['github-light', 'github-dark'],
          langs: [
            'javascript',
            'typescript',
            'python',
            'bash',
            'json',
            'yaml',
            'sql',
            'html',
            'css',
            'markdown',
            'text',
          ],
        });
        setHighlighter(hl);
      } catch (error) {
        console.error('Failed to initialize highlighter:', error);
      }
    };

    initHighlighter();
  }, []);

  useEffect(() => {
    if (highlighter && code) {
      try {
        const currentTheme = theme === 'dark' ? 'github-dark' : 'github-light';
        const highlighted = highlighter.codeToHtml(code, {
          lang: language,
          theme: currentTheme,
        });
        setHighlightedCode(highlighted);
      } catch (error) {
        console.error('Failed to highlight code:', error);
        // Fallback to plain text
        setHighlightedCode(`<pre><code>${code}</code></pre>`);
      }
    }
  }, [highlighter, code, language, theme]);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  if (!highlightedCode) {
    return (
      <div className={cn('relative max-w-full', className)}>
        <pre className="max-w-full overflow-x-auto whitespace-pre-wrap break-words rounded-lg bg-muted/30 p-4">
          <code className="break-words">{code}</code>
        </pre>
      </div>
    );
  }

  const codeLines = code.split('\n');
  const lineCount = codeLines.length;

  // Determine responsive classes based on wrapLongLines
  const getResponsiveClasses = () => {
    if (wrapLongLines) {
      return {
        container: 'max-w-full',
        pre: '[&>pre]:max-w-full! [&>pre]:whitespace-pre-wrap! [&>pre]:break-words! [&>pre]:!overflow-wrap-anywhere',
        code: '[&_code]:break-words! [&_code]:whitespace-pre-wrap! [&_code]:!overflow-wrap-anywhere [&_code]:!word-break-break-all',
      };
    }
    return {
      container: '',
      pre: '[&>pre]:whitespace-pre!',
      code: '',
    };
  };

  const responsiveClasses = getResponsiveClasses();

  return (
    <div className={cn('group relative max-w-full', className)}>
      {title && (
        <div className="min-w-0 border-b bg-muted/50 px-4 py-2 text-sm font-medium">
          <span className="block truncate">{title}</span>
        </div>
      )}

      {showCopyButton && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute right-2 top-2 z-10 opacity-0 transition-opacity group-hover:opacity-100"
          onClick={handleCopy}
        >
          {copied ? (
            <Check className="h-4 w-4 text-green-500" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </Button>
      )}

      {showLineNumbers ? (
        <div className="flex max-w-full overflow-hidden rounded-lg bg-muted/30">
          <div className="shrink-0 select-none border-r border-border/50 bg-muted/50">
            <div className="space-y-0 px-2 py-2 text-right font-mono text-xs text-muted-foreground">
              {Array.from({ length: lineCount }, (_, i) => (
                <div key={i} className="h-6 leading-6">
                  {i + 1}
                </div>
              ))}
            </div>
          </div>
          <div className="overflow-wrap-anywhere min-w-0 flex-1 overflow-x-auto break-words">
            <div
              className={cn(
                '[&>pre]:m-0! [&>pre]:bg-transparent! [&>pre]:p-2! [&>pre]:leading-6! [&_code]:leading-6!',
                responsiveClasses.container,
                responsiveClasses.pre,
                responsiveClasses.code,
              )}
              style={{ wordBreak: 'break-all', overflowWrap: 'anywhere' }}
              dangerouslySetInnerHTML={{ __html: highlightedCode }}
            />
          </div>
        </div>
      ) : (
        <div
          className={cn(
            'overflow-wrap-anywhere overflow-x-auto break-words rounded-lg bg-muted/30 [&>pre]:m-0! [&>pre]:bg-transparent! [&>pre]:p-4!',
            responsiveClasses.container,
            responsiveClasses.pre,
            responsiveClasses.code,
          )}
          style={{ wordBreak: 'break-all', overflowWrap: 'anywhere' }}
          dangerouslySetInnerHTML={{ __html: highlightedCode }}
        />
      )}
    </div>
  );
}

// Legacy compatibility component
export const SyntaxHighlighter = ({
  children,
  language,
  className,
  showLineNumbers,
  wrapLongLines = true,
  ...props
}: any) => {
  const code = typeof children === 'string' ? children : String(children);

  return (
    <CodeBlock
      code={code}
      language={language}
      className={className}
      showLineNumbers={showLineNumbers}
      wrapLongLines={wrapLongLines}
      {...props}
    />
  );
};

// Prism compatibility export
export const Prism = SyntaxHighlighter;
