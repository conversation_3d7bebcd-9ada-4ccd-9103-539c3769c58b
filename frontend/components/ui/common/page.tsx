import { cn } from '@/lib/utils';
import { range } from 'lodash';

import { Skeleton } from '../skeleton';

export function PageTitle(
  props: React.PropsWithChildren<{ className?: string }>,
) {
  return (
    <h1
      className={cn(
        'font-heading text-2xl leading-none font-normal tracking-tight dark:text-white',
        props.className,
      )}
    >
      {props.children}
    </h1>
  );
}

export function PageBody(
  props: React.PropsWithChildren<{
    className?: string;
  }>,
) {
  const className = cn(
    'flex w-full flex-1 flex-col pt-1 lg:px-4',
    props.className,
  );

  return <div className={className}>{props.children}</div>;
}

export function PageSkeleton(
  props: React.PropsWithChildren<{
    className?: string;
    lines?: number;
  }>,
) {
  const { className, lines = 10, children } = props;

  return (
    <div className="mt-2 flex w-full flex-col gap-y-10 p-4">
      <PageTitle>
        <Skeleton className="h-6 w-full" />
      </PageTitle>
      <PageBody className={cn('w-full space-y-2', className)}>
        {children ||
          range(lines).map((i) => <Skeleton key={i} className="h-4 w-full" />)}
      </PageBody>
    </div>
  );
}
