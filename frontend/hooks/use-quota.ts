import { QuotasService } from '@/client';
import useAuth from '@/hooks/useAuth';
import { useQuery } from '@tanstack/react-query';

interface QuotaInfo {
  quota_used: number;
  quota_limit: number;
  quota_remaining: number;
  usage_percentage: number;
}

interface UseQuotaReturn {
  quotaInfo: QuotaInfo | undefined;
  refreshQuota: () => Promise<void>;
  isLoading: boolean;
}

export function useQuota(): UseQuotaReturn {
  const { user } = useAuth();

  // Use React Query for efficient caching and fetching
  const {
    data: quotaInfo,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['quota-info', user?.id],
    queryFn: async () => {
      if (!user) return undefined;

      const response = await QuotasService.getQuotaInfo({
        userId: user.id,
      });
      return response;
    },
    enabled: !!user,
    staleTime: 120000, // 2 minutes - data stays fresh
    gcTime: 300000, // 5 minutes - data stays in cache
    refetchOnWindowFocus: false,
  });

  // Async refreshQuota function
  const refreshQuota = async () => {
    await refetch();
  };

  return {
    quotaInfo,
    refreshQuota,
    isLoading,
  };
}
