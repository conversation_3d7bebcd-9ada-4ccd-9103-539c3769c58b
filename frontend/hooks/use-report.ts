import { ReportsService } from '@/client';
import { useQuery } from '@tanstack/react-query';

interface UseReportOptions {
  enabled?: boolean;
}

interface UseReportReturn {
  report: unknown | null | undefined;
  isLoading: boolean;
  error: Error | null;
  refreshReport: () => Promise<void>;
}

export const useReport = (
  conversationId: string | null,
  options: UseReportOptions = {},
): UseReportReturn => {
  const {
    data: report,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['report', conversationId],
    queryFn: async (): Promise<unknown | null> => {
      if (!conversationId) {
        return null;
      }

      try {
        const result = await ReportsService.getReportByConversation({
          conversationId,
        });

        return result || null;
      } catch (error: any) {
        // Return null for 404 (report not found) or any network/parsing errors
        if (
          error.status === 404 ||
          error.name === 'TypeError' ||
          error.name === 'SyntaxError'
        ) {
          return null;
        }
        // Log the error for debugging but don't throw to prevent UI crashes
        console.warn('Error fetching report:', error);
        return null;
      }
    },
    enabled: Boolean(conversationId && options.enabled !== false),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 404 (report not found) or client errors
      if (error?.status === 404 || error?.status < 500) {
        return false;
      }
      return failureCount < 2; // Reduce retry attempts
    },
  });

  // Async refresh function
  const refreshReport = async () => {
    await refetch();
  };

  return {
    report,
    isLoading,
    error: error as Error | null,
    refreshReport,
  };
};
