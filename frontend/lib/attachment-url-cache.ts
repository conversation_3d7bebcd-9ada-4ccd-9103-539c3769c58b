// lib/attachment-url-cache.ts
import { AttachmentsService } from '@/client/sdk.gen';

interface CachedUrl {
  url: string;
  expiresAt: number; // timestamp
}

const CACHE_KEY_PREFIX = 'attachment_url_';
const DEFAULT_CACHE_DURATION = 55 * 60 * 1000; // 55 minutes (AWS presigned URLs typically expire in 1 hour)

class AttachmentUrlCache {
  private pendingRequests = new Map<string, Promise<string>>();

  private getCacheKey(attachmentId: string): string {
    return `${CACHE_KEY_PREFIX}${attachmentId}`;
  }

  private isExpired(cachedUrl: CachedUrl): boolean {
    return Date.now() >= cachedUrl.expiresAt;
  }

  private getCachedUrl(attachmentId: string): string | null {
    try {
      const cached = localStorage.getItem(this.getCacheKey(attachmentId));
      if (!cached) return null;

      const cachedUrl: CachedUrl = JSON.parse(cached);
      if (this.isExpired(cachedUrl)) {
        // Remove expired entry
        localStorage.removeItem(this.getCacheKey(attachmentId));
        return null;
      }

      return cachedUrl.url;
    } catch (error) {
      console.error('Error reading cached URL:', error);
      return null;
    }
  }

  private setCachedUrl(attachmentId: string, url: string): void {
    try {
      const cachedUrl: CachedUrl = {
        url,
        expiresAt: Date.now() + DEFAULT_CACHE_DURATION,
      };
      localStorage.setItem(this.getCacheKey(attachmentId), JSON.stringify(cachedUrl));
    } catch (error) {
      console.error('Error caching URL:', error);
    }
  }

  private async testUrl(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  async getDownloadUrl(attachmentId: string): Promise<string> {
    // Check if there's already a pending request for this attachment
    if (this.pendingRequests.has(attachmentId)) {
      console.log(`[AttachmentCache] Using pending request for ${attachmentId}`);
      return this.pendingRequests.get(attachmentId)!;
    }

    // Try to get from cache first
    const cachedUrl = this.getCachedUrl(attachmentId);
    if (cachedUrl) {
      console.log(`[AttachmentCache] Using cached URL for ${attachmentId}`);
      // Test if the cached URL still works
      const isValid = await this.testUrl(cachedUrl);
      if (isValid) {
        return cachedUrl;
      } else {
        console.log(`[AttachmentCache] Cached URL expired for ${attachmentId}`);
        // Remove invalid cached URL
        localStorage.removeItem(this.getCacheKey(attachmentId));
      }
    }

    // Create a promise for the backend request
    const requestPromise = this.fetchFromBackend(attachmentId);
    this.pendingRequests.set(attachmentId, requestPromise);

    try {
      const url = await requestPromise;
      return url;
    } finally {
      // Clean up the pending request
      this.pendingRequests.delete(attachmentId);
    }
  }

  private async fetchFromBackend(attachmentId: string): Promise<string> {
    console.log(`[AttachmentCache] Fetching new URL from backend for ${attachmentId}`);
    // Fetch new URL from backend
    const response = await AttachmentsService.getAttachmentDownloadUrl({
      attachmentId,
    });

    // Cache the new URL
    this.setCachedUrl(attachmentId, response.download_url);

    return response.download_url;
  }

  clearCache(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(CACHE_KEY_PREFIX)) {
          localStorage.removeItem(key);
        }
      });
      // Also clear pending requests
      this.pendingRequests.clear();
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  clearExpiredEntries(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(CACHE_KEY_PREFIX)) {
          const cached = localStorage.getItem(key);
          if (cached) {
            try {
              const cachedUrl: CachedUrl = JSON.parse(cached);
              if (this.isExpired(cachedUrl)) {
                localStorage.removeItem(key);
              }
            } catch (error) {
              // Remove invalid entries
              localStorage.removeItem(key);
            }
          }
        }
      });
    } catch (error) {
      console.error('Error clearing expired entries:', error);
    }
  }
}

export const attachmentUrlCache = new AttachmentUrlCache();
