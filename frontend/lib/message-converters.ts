// utils/message-converters.ts
import { ConversationPublic } from '@/client';
import { Message as ChatMessage, ToolCall } from '@/components/chat/types';
import { Session } from '@/components/chat/types';
import { ProcessMessageStreamProps } from '@/hooks/message-stream/utils/message-processor';

export const convertToUIMessage = (
  apiMessage: any,
  props: ProcessMessageStreamProps | null,
): ChatMessage => {
  const toolCalls: ToolCall[] =
    apiMessage.agent_thoughts?.map((thought: any) => ({
      id: thought.id,
      name: thought.tool || 'thought',
      arguments: thought.tool_input,
      output: thought.observation,
      status: thought.observation ? 'completed' : 'running',
      position: thought.position,
      thought: thought.thought,
    })) || [];

  return {
    id: apiMessage.id,
    content: apiMessage.content,
    role: apiMessage.role,
    timestamp: new Date(apiMessage.created_at * 1000),
    toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
    actionType: apiMessage.action_type,
    displayComponents: apiMessage.display_components || undefined,
    isInterrupt: apiMessage.is_interrupt,
    interruptMessage: apiMessage.interrupt_message,
    attachments: apiMessage.attachments || undefined,
  };
};

export const convertToSession = (
  conversation: ConversationPublic,
): Session => ({
  id: conversation.id,
  title: conversation.name || 'New Chat',
  timestamp: new Date(conversation.created_at),
  resource: conversation.resource
    ? {
        id: conversation.resource.id || '',
        name: conversation.resource.name,
        arn: conversation.resource.arn,
        type: conversation.resource.type,
        region: conversation.resource.region,
        description: conversation.resource.description,
      }
    : null,
});
