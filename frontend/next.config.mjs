/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: getRemotePatterns(),
  },
  turbopack: {
    resolveExtensions: ['.ts', '.tsx', '.js', '.jsx'],
  },
  transpilePackages: ['geist', 'shiki'],
  output: 'standalone',

  webpack: (config, { isServer }) => {
    // Handle client-side fallbacks
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
        canvas: false,
        encoding: false,
      };
    }

    // Handle PDF.js and canvas dependencies
    config.resolve.alias = {
      ...config.resolve.alias,
      canvas: false,
    };

    // Ignore canvas module for PDF.js
    config.externals = config.externals || [];
    if (!isServer) {
      config.externals.push('canvas');
    }

    return config;
  },
  modularizeImports: {
    lodash: {
      transform: 'lodash/{{member}}',
    },
  },

  /** We already do linting and typechecking as separate tasks in CI */
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },
};

export default nextConfig;

function getRemotePatterns() {
  /** @type {import('next').NextConfig['remotePatterns']} */
  return [
    {
      protocol: 'https',
      hostname: 'utfs.io',
      port: '',
    },
    {
      protocol: 'https',
      hostname: 'api.slingacademy.com',
      port: '',
    },
  ];
}
