{"name": "frontend", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "clean": "git clean -xdf node_modules dist .next", "dev": "next dev --turbopack -p 5173", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,md,json}\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,md,json}\"", "generate-client": "openapi-ts", "postinstall": "sort-package-json", "lint": "eslint .", "lint:fix": "next lint --fix", "start": "next start", "typecheck": "tsc --noEmit"}, "lint-staged": {"**/*": ["prettier --write --ignore-unknown"]}, "prettier": "./prettier/base.mjs", "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@next/eslint-plugin-next": "15.3.3", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "1.2.7", "@tailwindcss/postcss": "^4.1.10", "@tanstack/react-query": "^5.62.3", "@tanstack/react-table": "^8.10.7", "@tanstack/react-virtual": "^3.13.6", "@types/eslint": "9.6.1", "@types/lodash": "^4.17.16", "@types/react-dom": "19.1.6", "@types/react-pdf": "^7.0.0", "@uiw/react-markdown-preview": "^5.1.3", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "axios": "^1.7.9", "caniuse-lite": "^1.0.30001723", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "cron-parser": "^4.9.0", "cron-validate": "^1.4.5", "cronstrue": "^2.54.0", "dagre": "^0.8.5", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "date-formatter": "^0.1.1", "embla-carousel-react": "^8.5.2", "es-toolkit": "^1.30.1", "eslint-config-next": "15.3.3", "eslint-config-turbo": "^2.5.4", "framer-motion": "^12.0.0", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "input-otp": "1.4.2", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "kbar": "^0.1.0-beta.45", "lodash": "^4.17.21", "lucide-react": "^0.516.0", "match-sorter": "^6.3.4", "mermaid": "^11.4.1", "motion": "^12.4.5", "next": "15.3.4", "next-auth": "^5.0.0-beta.18", "next-themes": "^0.2.1", "nextjs-toploader": "^1.6.12", "nuqs": "^1.19.1", "postcss": "^8.5.1", "prism-react-renderer": "^2.4.1", "react": "19.1.0", "react-collapsed": "^4.2.0", "react-day-picker": "^8.9.1", "react-dom": "19.1.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.58.0", "react-icons": "^5.4.0", "react-intersection-observer": "^9.16.0", "react-json-view-lite": "^2.4.1", "react-markdown": "^9.1.0", "react-markdown-toc": "^1.4.0", "react-pdf": "^9.2.1", "react-resizable-panels": "^2.1.7", "react-responsive": "^10.0.0", "react-top-loading-bar": "3.0.2", "reactflow": "^11.11.4", "recharts": "2.15.3", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "rehype-prism": "^2.3.3", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sharp": "^0.32.5", "shiki": "^1.29.2", "sonner": "^1.5.0", "sort-by": "^0.0.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "tailwindcss-animate": "^1.0.7", "typescript-eslint": "8.34.1", "uuid": "^9.0.1", "zod": "^3.25.67", "zustand": "^4.4.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.29.0", "@faker-js/faker": "^9.0.3", "@hey-api/client-axios": "^0.3.1", "@hey-api/openapi-ts": "^0.59.2", "@tailwindcss/typography": "^0.5.16", "@trivago/prettier-plugin-sort-imports": "5.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "22.13.1", "@types/react": "19.1.8", "@types/sort-by": "^1.2.3", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.34.1", "eslint": "^9.29.0", "husky": "^9.0.11", "lint-staged": "^15.2.7", "null-loader": "^4.0.1", "openapi-typescript": "^7.4.4", "openapi-typescript-codegen": "^0.29.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.13", "react-scan": "^0.3.4", "sort-package-json": "^3.2.1", "tailwind-scrollbar": "^4.0.2", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.12.0", "pnpm": {"peerDependencyRules": {"allowedVersions": {"kbar": {"react": "19", "react-dom": "19"}}, "ignoreMissing": []}, "overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6", "kbar": "$kbar"}}}