/* ==========================================================================
      Form Components
      ========================================================================== */
@utility input {
  @apply border-input bg-background border;
  box-shadow: inset 0 1px 2px var(--color-border);

  .dark & {
    @apply bg-secondary;
    box-shadow:
      inset 0 1px 2px hsl(220 13% 8% / 0.1),
      0 1px 2px 0 hsl(220 13% 8% / 0.05);
  }
}

/* ==========================================================================
      UI Components
      ========================================================================== */
@utility card {
  /* Card Component */
  @apply relative overflow-hidden;
  background: linear-gradient(
    180deg,
    var(--color-card) 0%,
    color-mix(in srgb, var(--color-card), transparent 2%) 100%
  );
  box-shadow:
    0 0 0 1px color-mix(in srgb, var(--color-border), transparent 92%),
    0 1px 2px 0 color-mix(in srgb, var(--color-border), transparent 95%);
  backdrop-filter: blur(8px);

  .dark & {
    background: linear-gradient(
      180deg,
      var(--color-card) 0%,
      color-mix(in srgb, var(--color-card), transparent 5%) 100%
    );
    box-shadow:
      0 0 0 1px color-mix(in srgb, var(--color-border), transparent 85%),
      0 1px 3px 0 hsl(220 13% 8% / 0.15);
  }
}

@utility button {
  /* Button Component */
  @apply relative overflow-hidden;
  transform: translateZ(0);

  &:active {
    transform: scale(0.98);
  }
}

@utility sidebar {
  /* Sidebar Component */
  @apply bg-sidebar border-sidebar-border/10 fixed top-0 left-0 z-40 h-screen border-r;
  backdrop-filter: blur(8px);
}

/* ==========================================================================
      Dark Mode Component Overrides
      ========================================================================== */
@utility dark {
  & .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(100, 100, 100, 0.4);
  }

  & .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(120, 120, 120, 0.6);
  }

  & .scroll-area-thumb {
    background-color: rgba(100, 100, 100, 0.5);
  }

  & .scroll-area-thumb:hover {
    background-color: rgba(120, 120, 120, 0.7);
  }

  & .scroll-area-thumb:active {
    background-color: rgba(140, 140, 140, 0.9);
  }

  & .input {
    @apply bg-secondary;
    box-shadow:
      inset 0 1px 2px hsl(220 13% 8% / 0.1),
      0 1px 2px 0 hsl(220 13% 8% / 0.05);
  }

  & .card {
    background: linear-gradient(
      180deg,
      var(--color-card) 0%,
      color-mix(in srgb, var(--color-card), transparent 5%) 100%
    );
    box-shadow:
      0 0 0 1px color-mix(in srgb, var(--color-border), transparent 85%),
      0 1px 3px 0 hsl(220 13% 8% / 0.15);
  }
}
