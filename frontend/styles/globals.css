/*
* global.css
*
* Global styles for the entire application
 */

/* Tailwind CSS */
@import 'tailwindcss';

/* plugins - update the below if you add a new plugin */
@plugin 'tailwindcss-animate';
@plugin '@tailwindcss/typography';

@plugin 'tailwind-scrollbar' {
  nocompatible: true;
}

@import './theme.css';
@import './utilities.css';
@import './variables.css';
@import './components.css';
@import './animations.css';
@import './timeline.css';
@import './tooltips.css';
@import './theme.utilities.css';
@import './typography.css';

@source "./{app,components,constants,src,sections}/**/*.{ts,tsx}";

/* @config '../tailwind.config.js'; */

/* variants - update the below if you add a new variant */
@variant dark (&:where(.dark, .dark *));

/*
           The default border color has changed to `currentcolor` in Tailwind CSS v4,
           so we've added these compatibility styles to make sure everything still
           looks the same as it did with Tailwind CSS v3.

           If we ever want to remove these styles, we need to add an explicit border
           color utility to any element that depends on these defaults.
         */

/* ==========================================================================
               Base Element Styles
               ========================================================================== */

/* ==========================================================================
      Base Element Styles
      ========================================================================== */
@layer base {
  /* Global Reset */
  * {
    @apply border-border;
  }

  /* Base Typography and Body */
  body {
    @apply bg-background text-foreground font-sans font-medium antialiased;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
    text-rendering: optimizeLegibility;
  }

  /* Global Transitions */
  body *,
  .card,
  .button,
  .input {
    transition:
      background-color var(--transition-duration) var(--transition-ease),
      border-color var(--transition-duration) var(--transition-ease),
      color var(--transition-duration) var(--transition-ease),
      box-shadow var(--transition-duration) var(--transition-ease),
      opacity var(--transition-duration) var(--transition-ease);
  }

  /* ==========================================================================
        Global Scrollbar Styles
        ========================================================================== */

  /* WebKit Scrollbars */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.5);
    border-radius: 3px;
    border: 1px solid transparent;
    background-clip: content-box;
    transition: background-color var(--transition-duration)
      var(--transition-ease);
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(155, 155, 155, 0.7);
  }

  ::-webkit-scrollbar-thumb:active {
    background-color: rgba(155, 155, 155, 0.9);
  }

  ::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Firefox Scrollbars */
  * {
    scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
    scrollbar-width: thin;
  }

  /* Dark Mode Scrollbars */
  .dark ::-webkit-scrollbar-thumb {
    background-color: rgba(100, 100, 100, 0.5);
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(120, 120, 120, 0.7);
  }

  .dark ::-webkit-scrollbar-thumb:active {
    background-color: rgba(140, 140, 140, 0.9);
  }

  .dark * {
    scrollbar-color: rgba(100, 100, 100, 0.5) transparent;
  }
}

/* ==========================================================================
               Component Styles
               ========================================================================== */
@layer components {
  textarea {
    overflow: auto;
    resize: vertical;
  }

  /* Message Field */
  #message {
    overflow-y: auto;
    max-height: 300px;
  }
}
