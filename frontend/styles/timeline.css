/* ==========================================================================
      Execution Timeline Animation Styles
      ========================================================================== */

/* Simple persistent highlight for execution cards - no animations */
.execution-highlight {
  @apply border-primary/60;
  box-shadow: 0 0 0 1px
    color-mix(in srgb, var(--color-primary), transparent 70%);
  transition:
    border-color 0.15s ease,
    box-shadow 0.15s ease;
}

/* Enhanced failed status styling with modern shadows */
.failed-status-enhanced {
  box-shadow:
    0 0 0 1px hsl(0 72% 51% / 0.2),
    0 2px 4px hsl(0 72% 51% / 0.1);
  transition: box-shadow 0.3s ease;
}

.failed-status-enhanced:hover {
  box-shadow:
    0 0 0 1px hsl(0 72% 51% / 0.3),
    0 4px 8px hsl(0 72% 51% / 0.15),
    0 8px 16px hsl(0 72% 51% / 0.1);
}

.dark .failed-status-enhanced {
  box-shadow:
    0 0 0 1px hsl(0 75% 60% / 0.2),
    0 2px 4px hsl(0 75% 60% / 0.1);
}

.dark .failed-status-enhanced:hover {
  box-shadow:
    0 0 0 1px hsl(0 75% 60% / 0.3),
    0 4px 8px hsl(0 75% 60% / 0.15),
    0 8px 16px hsl(0 75% 60% / 0.1);
}

/* Simplified timeline execution point click feedback */
.timeline-point-clicked {
  animation: timeline-point-click 0.2s ease-out;
}

@keyframes timeline-point-click {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Simplified navigation loading state */
.navigation-loading {
  position: relative;
  opacity: 0.7;
  transition: opacity 0.2s ease-out;
}

/* ==========================================================================
      Pagination Animation Styles
      ========================================================================== */

/* Pagination transition animations */
.execution-list-enter {
  opacity: 0;
  transform: translateY(10px);
}

.execution-list-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition:
    opacity 300ms ease-out,
    transform 300ms ease-out;
}

.execution-list-exit {
  opacity: 1;
  transform: translateY(0);
}

.execution-list-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition:
    opacity 200ms ease-in,
    transform 200ms ease-in;
}

.pagination-button {
  transition: opacity 0.15s ease-out;
}

.pagination-button:hover:not(:disabled) {
  opacity: 0.8;
}

/* ==========================================================================
      Enhanced Timeline Horizontal Line Styles
      ========================================================================== */

/* Beautiful timeline line with consistent positioning */
.timeline-line-enhanced {
  position: relative;
  background: linear-gradient(
    90deg,
    var(--color-border),
    color-mix(in srgb, var(--color-primary), transparent 60%),
    var(--color-border)
  );
  border-radius: 9999px;
  box-shadow: 0 1px 3px
    color-mix(in srgb, var(--color-primary), transparent 90%);
}

/* Subtle glow effect for modern appearance */
.timeline-line-glow {
  background: linear-gradient(
    90deg,
    transparent,
    color-mix(in srgb, var(--color-primary), transparent 80%),
    transparent
  );
  filter: blur(2px);
}

/* Decorative end caps for timeline */
.timeline-end-cap {
  background: linear-gradient(
    135deg,
    color-mix(in srgb, var(--color-primary), transparent 70%),
    color-mix(in srgb, var(--color-primary), transparent 40%)
  );
  box-shadow: 0 1px 2px
    color-mix(in srgb, var(--color-primary), transparent 80%);
  border: 1px solid var(--color-background);
}

/* Dark mode adjustments for timeline */
.dark .timeline-line-enhanced {
  box-shadow: 0 1px 3px
    color-mix(in srgb, var(--color-primary), transparent 80%);
}

.dark .timeline-end-cap {
  box-shadow: 0 1px 2px
    color-mix(in srgb, var(--color-primary), transparent 70%);
  border: 1px solid var(--color-background);
}

/* Responsive timeline adjustments */
@media (max-width: 768px) {
  .timeline-line-enhanced {
    height: 2px;
  }

  .timeline-end-cap {
    width: 8px;
    height: 8px;
  }

  /* Adjust Latest/Oldest indicators for mobile */
  .timeline-time-label {
    font-size: 10px;
    padding: 4px 8px;
  }

  /* Reduce time label padding on mobile */
  .timeline-point-positioned .absolute.bottom-full {
    margin-bottom: 12px;
  }
}

/* Animation for timeline line appearance */
.timeline-line-appear {
  animation: timeline-line-draw 0.8s ease-out;
}

@keyframes timeline-line-draw {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

/* Simplified timeline UX improvements */
.timeline-intelligent-spacing {
  transition: opacity 0.2s ease-out;
}

/* Perfect timeline execution container positioning */
.timeline-execution-container {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  z-index: 10;
  /* Ensure container is perfectly centered on timeline bar */
  pointer-events: none; /* Allow pointer events to pass through to children */
  /* Improve centering precision */
  contain: layout;
  will-change: transform;
}

.timeline-execution-container > * {
  pointer-events: auto; /* Re-enable pointer events for children */
  /* Ensure child elements don't affect container positioning */
  position: relative;
}

.timeline-execution-point {
  transform-origin: center;
  transition: transform 0.15s ease-out;
}

.timeline-execution-point:hover {
  transform: scale(1.02);
}

.timeline-start-indicator {
  animation: fade-in-up 0.6s ease-out;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Improved timeline spacing for small counts */
.timeline-container-adaptive {
  max-width: min(100%, 800px);
  margin: 0 auto;
}

.timeline-container-compact {
  max-width: min(100%, 600px);
  margin: 0 auto;
}

.timeline-container-minimal {
  max-width: min(100%, 400px);
  margin: 0 auto;
}

/* Simplified positioning system for timeline points */
.timeline-point-positioned {
  transition: opacity 0.2s ease-out;
  /* Ensure perfect pixel alignment on timeline bar */
  position: absolute;
  top: 0 !important; /* Force positioning at container center */
  transform: translate(-50%, -50%) !important; /* Perfect center on both axes */
  /* Hardware acceleration for smooth transforms */
  will-change: transform;
}

/* Better visual hierarchy for time labels */
.timeline-time-label {
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
  /* Prevent text truncation issues */
  min-width: 0;
  flex-shrink: 0;
  white-space: nowrap;
  overflow: visible;
  /* Enhanced readability */
  backdrop-filter: blur(8px);
  border-radius: 8px;
}

/* Connection lines for below-timeline execution points */
.timeline-point-positioned::before {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 1px;
  height: 1rem;
  background: linear-gradient(to bottom, var(--color-border), transparent);
  opacity: 0.6;
  z-index: 1;
}

/* Simplified time labels above execution points */
.timeline-point-positioned .absolute.-top-8 {
  transition: opacity 0.15s ease-out;
}

.timeline-point-positioned:hover .absolute.-top-8 {
  opacity: 0.9;
}

/* Timeline end indicators styling */
.timeline-end-indicators {
  animation: fade-in-slide 0.5s ease-out;
}

@keyframes fade-in-slide {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
