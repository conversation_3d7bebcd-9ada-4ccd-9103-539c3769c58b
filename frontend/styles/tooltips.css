/* ==========================================================================
      Enhanced Tooltip Styles
      ========================================================================== */

/* Custom tooltip styles for better design system integration */
.tooltip-enhanced {
  @apply bg-card/95 text-card-foreground border-border/60 border shadow-lg backdrop-blur-xs;
  animation: tooltip-appear 0.15s ease-out;
}

.tooltip-enhanced::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border: 5px solid transparent;
}

/* Tooltip arrow positioning */
.tooltip-enhanced[data-side='top']::before {
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: color-mix(in srgb, var(--color-border), transparent 40%);
}

.tooltip-enhanced[data-side='bottom']::before {
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: color-mix(in srgb, var(--color-border), transparent 40%);
}

.tooltip-enhanced[data-side='left']::before {
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: color-mix(in srgb, var(--color-border), transparent 40%);
}

.tooltip-enhanced[data-side='right']::before {
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: color-mix(in srgb, var(--color-border), transparent 40%);
}

@keyframes tooltip-appear {
  0% {
    opacity: 0;
    transform: scale(0.95) translateY(-2px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Enhanced tooltip content styles */
.tooltip-content-enhanced {
  @apply text-sm leading-relaxed font-medium;
  max-width: 280px;
  padding: 8px 12px;
}

.tooltip-content-enhanced .tooltip-title {
  @apply text-card-foreground mb-1 font-semibold;
}

.tooltip-content-enhanced .tooltip-description {
  @apply text-muted-foreground text-xs leading-relaxed;
}

.tooltip-content-enhanced .tooltip-accent {
  @apply text-primary font-medium;
}

/* Status-specific tooltip variants */
.tooltip-success {
  @apply bg-success/10 border-success/30;
}

.tooltip-success .tooltip-title {
  @apply text-success-foreground;
}

.tooltip-warning {
  @apply bg-warning/10 border-warning/30;
}

.tooltip-warning .tooltip-title {
  @apply text-warning-foreground;
}

.tooltip-error {
  @apply bg-destructive/10 border-destructive/30;
  /* Enhanced red tint for destructive actions */
  background: linear-gradient(
    135deg,
    color-mix(in srgb, var(--color-destructive), transparent 92%) 0%,
    color-mix(in srgb, var(--color-destructive), transparent 88%) 100%
  );
}

.tooltip-error .tooltip-title {
  @apply text-destructive font-semibold;
}

.tooltip-error .tooltip-description {
  @apply text-muted-foreground;
}

.tooltip-info {
  @apply bg-info/10 border-info/30;
}

.tooltip-info .tooltip-title {
  @apply text-info-foreground;
}

/* Enhanced warning accent for irreversible actions */
.tooltip-warning-accent {
  @apply text-orange-600;
  font-size: 11px;
  font-weight: 500;
  letter-spacing: 0.025em;
}

.dark .tooltip-warning-accent {
  @apply text-orange-400;
}
