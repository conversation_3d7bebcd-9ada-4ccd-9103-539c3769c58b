/* ==========================================================================
      Scrollbar Utilities
      ========================================================================== */
@utility scrollbar-hide {
  /* Hide Scrollbar */
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

@utility scrollbar-thin {
  /* Thin Scrollbar */
  scrollbar-width: thin;

  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.4);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(155, 155, 155, 0.6);
  }

  .dark &::-webkit-scrollbar-thumb {
    background-color: rgba(100, 100, 100, 0.4);
  }

  .dark &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(120, 120, 120, 0.6);
  }
}

@utility scroll-area-thumb {
  /* ScrollArea Component */
  @apply relative flex-1;
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 3px;
  border: 1px solid transparent;
  background-clip: content-box;
  transition: background-color var(--transition-duration) var(--transition-ease);
  width: 6px;
  height: auto;
  min-height: 20px;

  &:hover {
    background-color: rgba(155, 155, 155, 0.7);
  }

  &:active {
    background-color: rgba(155, 155, 155, 0.9);
  }

  .dark & {
    background-color: rgba(100, 100, 100, 0.5);
  }

  .dark &:hover {
    background-color: rgba(120, 120, 120, 0.7);
  }

  .dark &:active {
    background-color: rgba(140, 140, 140, 0.9);
  }
}

@utility custom-scrollbar {
  @-moz-document url-prefix() {
    &textarea {
      scrollbar-width: thin;
      scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
    }
  }
}

/* ==========================================================================
      Utility Classes
      ========================================================================== */
@utility glass {
  /* Glass Effect */
  @apply backdrop-blur-lg;
  background: linear-gradient(
    135deg,
    color-mix(in srgb, var(--color-background), transparent 20%) 0%,
    color-mix(in srgb, var(--color-background), transparent 30%) 100%
  );
  border: 1px solid color-mix(in srgb, var(--color-border), transparent 90%);
  box-shadow:
    0 4px 6px -1px color-mix(in srgb, var(--color-border), transparent 95%),
    0 2px 4px -1px color-mix(in srgb, var(--color-border), transparent 95%);
}

@utility shadow-soft {
  /* Shadow Utilities */
  box-shadow:
    0 1px 2px 0 hsl(220 13% 8% / 0.08),
    0 1px 3px 0 hsl(220 13% 8% / 0.1);
}

@utility shadow-soft-lg {
  box-shadow:
    0 2px 4px 0 hsl(220 13% 8% / 0.08),
    0 4px 6px -1px hsl(220 13% 8% / 0.1);
}

@utility min-h-screen {
  /* Modern Viewport Units */
  min-height: 100vh;
  min-height: 100dvh;
}

@utility h-screen {
  height: 100vh;
  height: 100dvh;
}

/* ==========================================================================
      Text Effects
      ========================================================================== */
@utility mention-resource-gradient {
  @apply font-medium;
  background: linear-gradient(
    90deg,
    var(--color-brand-teal) 0%,
    var(--color-brand-blue) 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 0 1px
    color-mix(in srgb, var(--color-brand-teal), transparent 70%);

  /* Fallback styles for image capture - use solid colors when gradients fail */
  &.image-capture-fallback {
    background: none !important;
    -webkit-background-clip: unset !important;
    background-clip: unset !important;
    color: var(--color-brand-teal) !important;
    text-shadow: none !important;
  }
}

@utility mention-agent-gradient {
  @apply font-medium;
  background: linear-gradient(
    90deg,
    var(--color-brand-blue) 0%,
    hsl(279, 85%, 75%) 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 0 1px
    color-mix(in srgb, var(--color-brand-blue), transparent 70%);

  &.image-capture-fallback {
    background: none !important;
    -webkit-background-clip: unset !important;
    background-clip: unset !important;
    color: var(--color-brand-blue) !important;
    text-shadow: none !important;
  }
}

/* ==========================================================================
      Image Capture and Print Styles
      ========================================================================== */
@utility image-capture-fallback {
  /* Fallback styles for image capture - use solid colors when gradients fail */
  &.mention-resource-gradient {
    background: none !important;
    -webkit-background-clip: unset !important;
    background-clip: unset !important;
    color: var(--color-brand-teal) !important;
    text-shadow: none !important;
  }

  & .mention-agent-gradient {
    background: none !important;
    -webkit-background-clip: unset !important;
    background-clip: unset !important;
    color: var(--color-brand-teal) !important;
    text-shadow: none !important;
  }

  &.mention-agent-gradient {
    color: var(--color-brand-blue) !important;
  }
}

@utility badge {
  /* Badge rendering fixes for html2canvas */
  /* Ensure badges render properly in html2canvas */
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
  color-adjust: exact;

  /* Fix for inline-flex badges during image capture */
  [data-capturing] & {
    display: inline-block !important;
    white-space: nowrap !important;
    vertical-align: middle !important;
  }

  /* Ensure icons in badges render properly */
  [data-capturing] & svg {
    display: inline-block !important;
    vertical-align: middle !important;
  }
}

/* Background Noise Effect */
.bg-noise {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%' height='100%' filter='url(%23noise)' opacity='0.015'/%3E%3C/svg%3E");
}
