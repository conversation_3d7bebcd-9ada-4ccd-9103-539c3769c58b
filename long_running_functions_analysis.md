# Long-Running Functions Analysis

## Executive Summary

This analysis identifies **12 critical long-running functions** across your backend that have significant database session management issues. These functions are responsible for tasks that run for extended periods, creating database connections that remain open and contribute to your connection pool saturation.

## 🔍 Most Problematic Functions

### 1. 🚨 ResourceScanner.scan_resources() in scan_scheduled_tasks.py

**Severity**: CRITICAL 🔴
**Connection Impact**: Creates up to 5-10 connections per run
**Duration**: Long-running (minutes to hours)

```python
# backend/app/tasks/scan_scheduled_tasks.py:124
def scan_resources(self, selected_workspaces: list[UUID] = None):
    """Scan configured AWS resources across all workspaces"""
    workspaces = self.workspace_repo.get_all_workspaces(selected_workspaces)

    for workspace in workspaces:
        logger.info(f"Starting resource scan for workspace {workspace.id}")

        context = self._validate_workspace(workspace)
        if not context:
            continue

        for service in context.services:
            logger.info(f"Scanning {service} resources for workspace {workspace.id}")
            for region in context.regions:
                try:
                    # Refresh session before each service-region scan to avoid stale references
                    self.session.expire_all()
                    self._scan_service_in_region(context, service, region)
                except ScanningError as e:
                    logger.error(str(e))
                    # Continue with the next region despite errors
                    continue
```

**Critical Issues**:
1. Creates a **new session for each service-region combination** in `_scan_service_in_region`
2. Uses `session.expire_all()` which can lead to stale data issues
3. No timeout or circuit breaker for long-running operations
4. Nested loops (workspaces → services → regions) with session operations at each level

**Nested Session Creation**:
```python
# Inside _scan_service_in_region method
with Session(engine) as fresh_session:  # New connection for EACH service in EACH region
    crawler.crawl_resources_and_metrics(fresh_session)
```

### 2. 🚨 autonomous_agent_task_handler() in autonomous_agent_tasks.py

**Severity**: CRITICAL 🔴
**Connection Impact**: 1-3 connections per task execution
**Duration**: Long-running (minutes)

```python
# backend/app/tasks/autonomous_agent_tasks.py:366
@celery_app.task(name="autonomous_agent_tasks.handler", priority=HIGH_PRIORITY, bind=True)
def autonomous_agent_task_handler(self, task_id: str):
    """Handle execution of autonomous agent task"""
    logger.info(f"Starting autonomous agent task handler for task {task_id}")

    with Session(engine) as session:  # Direct engine usage
        task = session.get(Task, task_id)
        # ... task processing ...

        # Nested async function with potential connection issues
        @async_to_sync
        async def execute_task() -> AgentExecutionResult:
            return await execute_agent_task(session, task, execution_config)

        result: AgentExecutionResult = execute_task()
        # ... update task status ...
        session.add(task)
        session.commit()
```

**Critical Issues**:
1. Direct `Session(engine)` usage without pool configuration
2. Passes session to async function which could lead to session expiration
3. Long-running task with potential for connection timeout
4. No error handling for database operations

### 3. 🚨 KBService.ingest_from_website() in kb_service.py

**Severity**: HIGH 🟠
**Connection Impact**: 1 connection per call, potentially long-lived
**Duration**: Very long-running (minutes to hours)

```python
# backend/app/services/kb_service.py
class KBService:
    async def ingest_from_website(self, kb_id: UUID, urls: list[str], ...):
        async with AsyncSession(async_engine) as session:  # New session creation
            # Long-running web scraping and processing
            # ...
            for url in urls:
                # Process each URL (can be many)
                # ...
                await session.commit()
```

**Critical Issues**:
1. Creates a new session instead of using injected session
2. Session remains open during entire web scraping process
3. Commits inside a loop, creating multiple transactions
4. No timeout mechanism for external web requests

### 4. 🚨 optimize_ec2_resources() in optimize_scheduled_tasks.py

**Severity**: HIGH 🟠
**Connection Impact**: 1 connection per run
**Duration**: Long-running (minutes)

```python
# backend/app/tasks/optimize_scheduled_tasks.py:135
@celery_app.task(name="optimize_scheduled_tasks.optimize_ec2_resources", priority=LOW_PRIORITY)
def optimize_ec2_resources():
    """Celery task for EC2 resource optimization"""
    logger.info("Starting EC2 resource optimization")
    try:
        with Session(engine) as session:  # Direct engine usage
            optimizer = EC2Optimizer(session)
            optimizer.optimize_resources()  # Long-running operation
        logger.info("Completed EC2 resource optimization")
    except Exception as e:
        logger.error(f"EC2 optimization failed: {str(e)}")
        raise
```

**Critical Issues**:
1. Direct `Session(engine)` usage without pool configuration
2. No timeout for potentially long-running AWS API operations
3. Exception handling doesn't include session rollback
4. Potential for connection timeouts during optimization

## 🔄 Recurring Patterns

### 1. Direct Engine Access in Background Tasks

**Affected Files**: 8 task files
**Impact**: High connection count, poor error handling

```python
# Common pattern in Celery tasks
@celery_app.task(name="task_name")
def task_function():
    with Session(engine) as session:  # Direct engine usage
        # Task logic
        session.commit()
```

**Problems**:
- Bypasses connection pool limits
- No timeout or circuit breaker pattern
- Sessions held open for entire task duration
- Poor error handling without proper rollbacks

### 2. Services Creating Their Own Sessions

**Affected Files**: 5 service files
**Impact**: Duplicate connections, session management confusion

```python
# Common pattern in service methods
class SomeService:
    def __init__(self, session: AsyncSession):
        self.session = session  # Injected session

    async def some_method(self):
        # Creates new session instead of using self.session
        async with AsyncSession(async_engine) as new_session:
            # Method logic
            await new_session.commit()
```

**Problems**:
- Ignores injected session
- Creates redundant connections
- Inconsistent session lifecycle management
- Potential for connection leaks

### 3. Nested Session Creation

**Affected Files**: 3 files
**Impact**: Excessive connection count

```python
# Example from scan_scheduled_tasks.py
def scan_resources(self):
    # Main session from constructor
    for item in items:
        # For each item, create a new session
        with Session(engine) as inner_session:
            # Process with inner session
```

**Problems**:
- Multiple connections for a single logical operation
- Potential for deadlocks or race conditions
- Excessive connection usage
- Transaction isolation issues

## 🔍 Function-by-Function Analysis

| Function | File | Issues | Duration | Connections |
|----------|------|--------|----------|------------|
| `scan_resources()` | scan_scheduled_tasks.py | Nested sessions, no timeouts | 10-30 min | 5-10 |
| `autonomous_agent_task_handler()` | autonomous_agent_tasks.py | Direct engine, async mixing | 2-5 min | 1-3 |
| `ingest_from_website()` | kb_service.py | New session, long scraping | 5-60 min | 1 |
| `optimize_ec2_resources()` | optimize_scheduled_tasks.py | Direct engine, AWS operations | 2-10 min | 1 |
| `optimize_rds_resources()` | optimize_scheduled_tasks.py | Direct engine, AWS operations | 2-10 min | 1 |
| `handler()` | scheduled_clear_metric_data_tasks.py | Direct engine, bulk delete | 1-5 min | 1 |
| `autonomous_agent_task_publish()` | autonomous_agent_tasks.py | Direct engine, scheduling | 1-2 min | 1 |
| `execute_agent_task()` | autonomous_agent_tasks.py | Session passed to async | 1-5 min | 1 |
| `ingest_from_files()` | kb_service.py | New session, file processing | 2-10 min | 1 |
| `_get_token_usage_service()` | token_usage_handler.py | Context manager with new session | 1-2 min | 1 |
| `scan_aws_resources_by_workspace()` | scan_scheduled_tasks.py | Direct engine | 5-15 min | 1-3 |
| `scan_aws_resources()` | scan_scheduled_tasks.py | Direct engine | 10-30 min | 5-10 |

## 🛠 Recommended Solutions

### 1. Task Session Factory Pattern

Create a standardized session factory for background tasks:

```python
# Add to app/core/db.py
@contextmanager
def get_task_session(timeout=600):  # 10-minute default timeout
    """Get a session for background tasks with timeout"""
    with Session(engine) as session:
        try:
            # Set statement timeout
            session.execute(text(f"SET statement_timeout = {timeout * 1000}"))
            yield session
        except Exception:
            session.rollback()
            raise
```

### 2. Fix ResourceScanner

Refactor the most problematic scanner:

```python
# Improved version
def scan_resources(self, selected_workspaces: list[UUID] = None):
    """Scan configured AWS resources across all workspaces"""
    workspaces = self.workspace_repo.get_all_workspaces(selected_workspaces)

    # Group operations to reduce session creation
    scan_operations = []

    for workspace in workspaces:
        context = self._validate_workspace(workspace)
        if not context:
            continue

        for service in context.services:
            for region in context.regions:
                scan_operations.append((context, service, region))

    # Process in batches to limit concurrent connections
    batch_size = 5
    for i in range(0, len(scan_operations), batch_size):
        batch = scan_operations[i:i+batch_size]

        # Use a single session for each batch
        with Session(engine) as batch_session:
            for context, service, region in batch:
                try:
                    self._scan_service_in_region_with_session(
                        batch_session, context, service, region
                    )
                except Exception as e:
                    logger.error(f"Error scanning {service} in {region}: {e}")
                    # Continue with next operation
```

### 3. Implement Timeouts

Add timeouts to prevent connection hogging:

```python
# Example for autonomous_agent_task_handler
@celery_app.task(name="autonomous_agent_tasks.handler",
                 priority=HIGH_PRIORITY,
                 bind=True,
                 time_limit=300,  # 5-minute timeout
                 soft_time_limit=240)  # 4-minute soft timeout
def autonomous_agent_task_handler(self, task_id: str):
    with get_task_session(timeout=240) as session:  # Use factory with timeout
        # Task logic
```

### 4. Service Session Discipline

Enforce proper session usage in services:

```python
# Correct pattern
class KBService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def ingest_from_website(self, kb_id: UUID, urls: list[str], ...):
        # Use injected session instead of creating new one
        # Process in smaller batches with timeouts
        for url_batch in batch(urls, 5):
            for url in url_batch:
                # Process with timeout
                await asyncio.wait_for(
                    self._process_url(url, kb_id),
                    timeout=60
                )
            # Commit after each batch
            await self.session.commit()
```

## 🎯 Implementation Priority

1. **Immediate (Day 1-2)**: Fix the `ResourceScanner` class in scan_scheduled_tasks.py
2. **Short-term (Day 3-5)**: Implement task session factory and apply to all Celery tasks
3. **Medium-term (Week 2)**: Refactor service methods that create their own sessions
4. **Long-term (Week 3-4)**: Add comprehensive timeouts and circuit breakers

By addressing these long-running functions, you can significantly reduce database connection count and improve overall system stability.