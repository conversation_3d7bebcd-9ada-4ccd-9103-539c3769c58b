# Multimodal File Upload Feature Specification

## Overview
This feature enables users to upload files (images, PDFs, CSVs, etc.) directly within the chat interface, with secure processing and attachment to messages in conversations.

## Requirements Clarifications

### File Constraints
- **Size Limit**: 25MB per file (enforced in both frontend and backend)
- **Security Validation**: Celery task fails if file exceeds 25MB during processing
- **No Thumbnails**: Display filename only (no thumbnail generation)

### File Preview Behavior
- **Images**: Popup dialog displaying full image
- **PDFs**: Dialog using `frontend/app/(dashboard)/kb/components/SimplePDFViewer.tsx`
- **Text Files (CSV, TXT)**: Preview first few lines with "Load More" button
- **Unsupported Files**: Show toast notification if frontend cannot display

### Agent Processing Integration
- **Images**: Send directly to agent as-is
- **PDFs**: Convert to series of PNG pages using Python library
- **Other Files**: Convert to raw text for agent consumption

### File Lifecycle
- **Retention**: Files cleaned up when conversations are deleted
- **Orphaned Files**: Future cronjob will clean attachments not linked to any message_id
- **Owner Tracking**: Add owner field for audit purposes

### User Experience Rules
- **Upload Validation Failures**: Show in UI, block message sending until failed files removed
- **Concurrent Operations**:
  - Block message sending during file validation
  - Allow file uploads during message streaming
  - Auto-fail uploads if user navigates away
- **Message Display**: Show filenames above user messages (no thumbnails)
- **Historical Messages**: Backend sends attachment metadata for old conversations
- **File Access**: Presigned download URLs for viewing/downloading

## Process Flow Diagrams

### File Upload Process
```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant S3
    participant Celery
    participant DB

    User->>Frontend: Drop/paste files
    Frontend->>Frontend: Validate size (25MB), type
    Frontend->>Backend: POST /attachments/presigned-urls
    Backend->>Backend: Security validation of metadata
    Backend->>S3: Generate presigned PUT URL
    Backend->>Frontend: Return presigned URLs
    Frontend->>S3: Direct upload file
    Frontend->>Backend: POST /attachments/confirm-uploads
    Backend->>Celery: Trigger validation task
    Backend->>Frontend: Return task_id

    loop Every 2 seconds (max 60s)
        Frontend->>Backend: GET /attachments/tasks/{task_id}
        Backend->>Frontend: Task status + progress
    end

    Celery->>S3: Download file for validation
    Celery->>Celery: Security scan (25MB check, content analysis)
    Celery->>DB: Create MessageAttachment record
    Celery->>Celery: Cleanup temp files
    Celery->>Backend: Return attachment_id

    Frontend->>Frontend: Show file ready for sending
```

### Message Sending with Attachments
```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant Agent
    participant FileProcessor

    User->>Frontend: Hit enter to send message
    Frontend->>Backend: POST /chat/{conversation_id}/stream (with attachment_ids)
    Backend->>DB: Create Message record
    Backend->>DB: Link attachments to message (update message_id)

    alt Images
        Backend->>Agent: Send image directly
    else PDFs
        Backend->>FileProcessor: Convert PDF to PNG pages
        FileProcessor->>Agent: Send PNG pages
    else Other Files
        Backend->>FileProcessor: Convert to raw text
        FileProcessor->>Agent: Send text content
    end

    Agent->>Backend: Process with file content
    Backend->>Frontend: Stream response
```

### File Access and Display
```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant S3

    Note over User,S3: Loading old conversation
    Backend->>Frontend: Send messages with attachment metadata
    Frontend->>Frontend: Display filenames above messages

    Note over User,S3: User wants to view file
    User->>Frontend: Click attachment filename
    Frontend->>Backend: GET /attachments/{attachment_id}/download-url
    Backend->>S3: Generate presigned GET URL
    Backend->>Frontend: Return presigned URL

    alt Images
        Frontend->>S3: Fetch image via presigned URL
        Frontend->>Frontend: Display in popup dialog
    else PDFs
        Frontend->>S3: Fetch PDF via presigned URL
        Frontend->>Frontend: Display in SimplePDFViewer
    else Text Files
        Frontend->>S3: Fetch file via presigned URL
        Frontend->>Frontend: Preview lines + Load More button
    else Unsupported
        Frontend->>Frontend: Show download toast
    end
```

## Architecture Components

### Frontend Components

#### 1. Message Input Enhancement (`frontend/components/chat/message-input.tsx`)
**Current State**: The component already has sophisticated mention functionality and state management.

**Required Enhancements**:
- **File Upload Zone**: Add drag-and-drop area above the text input
- **File Display Area**: Show uploaded filenames (no thumbnails)
- **Upload Progress**: Sequential file processing with progress indicators (0-100%)
- **File Actions**: Preview and remove functionality
- **Integration**: Clipboard paste support for images
- **Validation**: Block message sending when files are validating or failed

**Key Integration Points**:
- Extend existing state management pattern
- Follow the mention dropdown positioning approach
- Use existing utility functions for UI coordination

#### 2. File Upload Hook (`frontend/hooks/use-file-upload.ts`)
**New Component**: Custom hook for managing file upload state and API interactions.

**Responsibilities**:
- File validation (25MB limit, client-side pre-screening)
- Sequential upload queue management
- Progress tracking and status polling (max 60 seconds)
- API communication for presigned URLs and confirmations
- Auto-fail on navigation away

#### 3. File Preview Components
**Enhancement Required**:
- **Image Preview**: Full image popup dialog
- **PDF Preview**: Integration with existing `SimplePDFViewer.tsx`
- **Text Preview**: Custom component with pagination
- **Error Handling**: Toast notifications for unsupported files

#### 4. Message Sending Integration (`frontend/hooks/use-autonomous-message-stream.ts`)
**Current State**: Handles message streaming and state management.

**Required Enhancement**:
- Add `attachment_ids` parameter to message sending
- Include attachment metadata in message payload
- Block sending during file validation
- Allow uploads during streaming

### Backend API Endpoints

#### 1. Presigned URL Generation (`backend/app/api/routes/attachments.py`)
**New Route**: `POST /attachments/presigned-urls`

**Similar Pattern**: Follows `backend/app/api/routes/kb.py` presigned URL implementation

**Request/Response Models**:
- `AttachmentPresignedUrlRequest` - file metadata (filename, size, type)
- `AttachmentPresignedUrlResponse` - presigned URLs and storage keys

**Validation**: 25MB size limit, supported MIME types

#### 2. Upload Confirmation (`backend/app/api/routes/attachments.py`)
**New Route**: `POST /attachments/confirm-uploads`

**Functionality**:
- Verify files exist in storage
- Trigger security validation Celery task
- Return task ID for status tracking

#### 3. Task Status Monitoring (`backend/app/api/routes/attachments.py`)
**New Route**: `GET /attachments/tasks/{task_id}`

**Similar Pattern**: Uses existing `backend/app/api/routes/kb.py` task status pattern

#### 4. File Download URL (`backend/app/api/routes/attachments.py`)
**New Route**: `GET /attachments/{attachment_id}/download-url`

**Functionality**:
- Verify user permissions (conversation access)
- Generate presigned GET URL for file access
- Support frontend preview functionality

#### 5. Message API Enhancement (`backend/app/api/routes/autonomous_agent.py`)
**Current Endpoint**: `POST /chat/{conversation_id}/stream`

**Required Enhancement**:
- Add `attachment_ids` field to message payload
- Link attachments to messages during processing
- Process files for agent consumption

### Database Schema

#### MessageAttachment Model (`backend/app/models.py`)
**Current State**: Model already exists with required fields

**Fields Used**:
- `id`, `message_id`, `filename`, `original_filename`
- `file_type`, `file_size`, `storage_key`, `thumbnail_key`
- `created_at`, `updated_at`

**Required Addition**:
- `owner_id`: UUID field for audit tracking

**Key Relationship**: Links to `Message.attachments` via foreign key

### Security Validation

#### Enhanced Attachment Validator (`backend/app/services/security/attachment_validator.py`)
**Current State**: Comprehensive validation system exists

**Integration Points**:
- Pre-upload validation (file metadata + 25MB limit)
- Post-upload security scanning (downloaded file analysis)
- MIME type verification and magic number validation
- Content analysis for suspicious patterns
- **Critical**: Fail task if file exceeds 25MB during Celery processing

### Celery Task Processing

#### 1. Attachment Validation Task (`backend/app/tasks/attachment_tasks.py`)
**New Task**: `validate_attachment_task`

**Similar Pattern**: Follows `backend/app/tasks/kb_tasks.py` structure

**Process Flow**:
1. Download file from S3 using storage key
2. **Size Check**: Fail immediately if > 25MB
3. Run security validation using `AttachmentSecurityValidator`
4. Create `MessageAttachment` record in database with owner_id
5. Update task status with attachment ID
6. Clean up temporary files

#### 2. File Processing for Agent (`backend/app/services/file_processor.py`)
**New Service**: Convert files for agent consumption

**Processing Logic**:
- **Images**: Pass through unchanged
- **PDFs**: Convert to PNG pages using Python library (e.g., pdf2image, PyMuPDF)
- **Text Files**: Extract raw text content
- **Office Docs**: Extract text content

### Object Storage Integration

#### Storage Repository (`backend/app/repositories/object_storage/`)
**Current State**: S3/Minio abstraction layer exists

**Usage Pattern**:
- Generate presigned PUT URLs for direct upload
- Generate presigned GET URLs for file access/download
- File existence verification
- Download for security validation
- Cleanup operations

**Bucket Strategy**: Use dedicated attachment bucket (similar to `KB_BUCKET`)

### Service Layer

#### 1. Attachment Service (`backend/app/services/attachment_service.py`)
**New Service**: Business logic coordination

**Responsibilities**:
- Orchestrate upload workflow
- Coordinate with security validator
- Manage database operations
- Handle task lifecycle
- Permission checking for file access

#### 2. Repository Layer (`backend/app/repositories/attachment.py`)
**New Repository**: Data access layer

**Operations**:
- CRUD operations for MessageAttachment
- Attachment-message linking
- Status tracking and updates
- Owner-based queries

### Configuration and Schemas

#### 1. Request/Response Schemas (`backend/app/schemas/message_attachment.py`)
**New Schema File**: Pydantic models for API contracts

**Key Models**:
- `AttachmentFileInfo` - File metadata for uploads (25MB validation)
- `AttachmentPresignedUrlRequest/Response` - URL generation
- `AttachmentConfirmRequest` - Upload confirmation
- `AttachmentTaskStatusResponse` - Task monitoring
- `AttachmentDownloadResponse` - Download URL response

#### 2. MIME Type Configuration (`backend/app/schemas/mime_type.py`)
**Current File**: Extend existing validation for attachment types

**Supported Types**:
- Images: JPEG, PNG, GIF, WebP
- Documents: PDF, TXT, CSV, JSON
- Office: DOCX, XLSX

## Error Handling Strategy

### Frontend Error States
- **File validation failures**: Show specific error messages, prevent message sending
- **Upload failures**: Display error, allow removal and retry
- **Security validation failures**: Show failure status, require file removal
- **Navigation away**: Auto-fail in-progress uploads
- **Unsupported preview**: Toast notification with download option

### Backend Error Recovery
- **Size limit exceeded**: Immediate task failure in Celery
- **Orphaned file cleanup**: Future cronjob for unlinked attachments
- **Task failure recovery**: Standard Celery retry mechanisms
- **Permission checks**: Verify conversation access for file operations

## Performance Considerations

### Upload Optimization
- **Direct S3 Upload**: Bypass backend for file transfer
- **Sequential Processing**: Prevent resource exhaustion (no parallel uploads)
- **Progress Feedback**: Smooth UI experience with progress indicators
- **Timeout Management**: 60-second maximum polling period

### File Processing
- **PDF Conversion**: Use efficient Python libraries for PDF-to-PNG conversion
- **Text Extraction**: Streaming text extraction for large files
- **Memory Management**: Process files in chunks, immediate cleanup

### Storage Management
- **Unique Storage Keys**: Prevent naming conflicts
- **Bucket Organization**: Logical file organization by conversation/user
- **Lifecycle Policies**: Automatic cleanup tied to conversation deletion

## Implementation Files

### New Files Required
- `frontend/hooks/use-file-upload.ts`
- `frontend/components/chat/file-preview.tsx`
- `backend/app/api/routes/attachments.py`
- `backend/app/services/attachment_service.py`
- `backend/app/services/file_processor.py`
- `backend/app/repositories/attachment.py`
- `backend/app/schemas/message_attachment.py`
- `backend/app/tasks/attachment_tasks.py`

### Files to Modify
- `frontend/components/chat/message-input.tsx`
- `frontend/hooks/use-autonomous-message-stream.ts`
- `backend/app/api/routes/autonomous_agent.py`
- `backend/app/models.py` (add owner_id to MessageAttachment)
- `backend/app/services/security/attachment_validator.py` (add 25MB check)

### Integration Points
- Use existing `SimplePDFViewer.tsx` for PDF preview
- Follow existing presigned URL patterns from KB system
- Leverage existing Celery task infrastructure
- Maintain consistency with current error handling patterns

## Future Considerations
- **Cronjob Implementation**: Clean up orphaned attachments not linked to any message_id
- **Advanced File Processing**: OCR for images, advanced PDF text extraction
- **Storage Optimization**: File deduplication, compression
- **Analytics**: Track file usage patterns and storage metrics
