[tool.bandit]
exclude_dirs = ["tests", "venv", ".venv", "env", ".env", "node_modules"]
skips = ["B101", "B104"]  # Skip assert statements and hardcoded bind checks

[tool.ruff]
line-length = 100
target-version = "py310"
select = ["E", "F", "B", "I"]
ignore = ["E203", "E501"]
exclude = [
    ".git",
    ".ruff_cache",
    ".venv",
    "venv",
    "node_modules",
    "migrations",
    "alembic",
]

[tool.ruff.isort]
known-first-party = ["app"]
