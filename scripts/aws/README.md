# AWS Scripts

This folder contains scripts for AWS services integration, primarily focused on Amazon ECR (Elastic Container Registry).

## Scripts

### `setup-ecr.sh`
**Purpose:** Create and configure ECR repositories for all CloudThinker services

**Usage:**
```bash
./setup-ecr.sh [AWS_REGION] [REPOSITORY_PREFIX]
```

**Examples:**
```bash
./setup-ecr.sh us-east-1 cloudthinker
./setup-ecr.sh ap-southeast-1 mycompany
```

**Features:**
- Creates ECR repositories for all services (backend, frontend, executor, payment, slack-integration)
- Configures lifecycle policies for automatic image cleanup
- Enables vulnerability scanning
- Sets up AES256 encryption

### `ecr-login.sh`
**Purpose:** Authenticate Docker with Amazon ECR

**Usage:**
```bash
./ecr-login.sh [AWS_REGION]
```

**Examples:**
```bash
./ecr-login.sh us-east-1
./ecr-login.sh ap-southeast-1
```

**Features:**
- Authenticates Docker with ECR
- Validates AWS credentials
- Provides registry URL for reference

## Prerequisites

- AWS CLI installed and configured
- Docker installed
- Appropriate AWS permissions for ECR operations

## Quick Access

Using the scripts index:
```bash
# From project root
./scripts/index.sh aws setup-ecr ap-southeast-1 cloudthinker
./scripts/index.sh aws ecr-login ap-southeast-1
```

Direct access:
```bash
./scripts/aws/setup-ecr.sh ap-southeast-1 cloudthinker
./scripts/aws/ecr-login.sh ap-southeast-1
```
