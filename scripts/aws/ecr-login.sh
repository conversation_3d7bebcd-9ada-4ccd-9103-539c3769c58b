#!/bin/bash

# Script to login to ECR for local development
# Usage: ./scripts/ecr-login.sh [AWS_REGION]

set -e

# Configuration
AWS_REGION=${1:-us-east-1}

echo "Logging into Amazon ECR..."

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "Error: AWS CLI is not configured or credentials are invalid"
    echo "Please run 'aws configure' first"
    exit 1
fi

# Get AWS Account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
ECR_REGISTRY="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"

echo "AWS Account ID: $AWS_ACCOUNT_ID"
echo "ECR Registry: $ECR_REGISTRY"

# Login to ECR
aws ecr get-login-password --region "$AWS_REGION" | docker login --username AWS --password-stdin "$ECR_REGISTRY"

echo "✓ Successfully logged into ECR"
echo ""
echo "You can now pull/push images to:"
echo "  $ECR_REGISTRY/cloudthinker/*"
