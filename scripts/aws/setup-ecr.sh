#!/bin/bash

# Script to create ECR repositories for CloudThinker services
# Usage: ./scripts/setup-ecr.sh [AWS_REGION] [REPOSITORY_PREFIX]

set -e

# Configuration
AWS_REGION=${1:-us-east-1}
REPOSITORY_PREFIX=${2:-cloudthinker}

# Services that need ECR repositories
SERVICES=(
    "backend"
    "frontend"
    "executor"
    "payment"
    "slack-integration"
)

echo "Setting up ECR repositories in region: $AWS_REGION"
echo "Repository prefix: $REPOSITORY_PREFIX"
echo "----------------------------------------"

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "Error: AWS CLI is not configured or credentials are invalid"
    echo "Please run 'aws configure' first"
    exit 1
fi

# Get AWS Account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
echo "AWS Account ID: $AWS_ACCOUNT_ID"

# Create ECR repositories for each service
for service in "${SERVICES[@]}"; do
    REPO_NAME="${REPOSITORY_PREFIX}/${service}"
    echo "Creating ECR repository: $REPO_NAME"

    # Check if repository already exists
    if aws ecr describe-repositories --repository-names "$REPO_NAME" --region "$AWS_REGION" > /dev/null 2>&1; then
        echo "  ✓ Repository $REPO_NAME already exists"
    else
        # Create the repository
        aws ecr create-repository \
            --repository-name "$REPO_NAME" \
            --region "$AWS_REGION" \
            --image-scanning-configuration scanOnPush=true \
            --encryption-configuration encryptionType=AES256 > /dev/null

        echo "  ✓ Created repository $REPO_NAME"

        # Set lifecycle policy to manage image retention
        aws ecr put-lifecycle-policy \
            --repository-name "$REPO_NAME" \
            --region "$AWS_REGION" \
            --lifecycle-policy-text '{
                "rules": [
                    {
                        "rulePriority": 1,
                        "description": "Keep last 10 production images",
                        "selection": {
                            "tagStatus": "tagged",
                            "tagPrefixList": ["v", "prod"],
                            "countType": "imageCountMoreThan",
                            "countNumber": 10
                        },
                        "action": {
                            "type": "expire"
                        }
                    },
                    {
                        "rulePriority": 2,
                        "description": "Keep last 5 latest images",
                        "selection": {
                            "tagStatus": "tagged",
                            "tagPrefixList": ["latest"],
                            "countType": "imageCountMoreThan",
                            "countNumber": 5
                        },
                        "action": {
                            "type": "expire"
                        }
                    },
                    {
                        "rulePriority": 3,
                        "description": "Delete untagged images older than 1 day",
                        "selection": {
                            "tagStatus": "untagged",
                            "countType": "sinceImagePushed",
                            "countUnit": "days",
                            "countNumber": 1
                        },
                        "action": {
                            "type": "expire"
                        }
                    }
                ]
            }' > /dev/null

        echo "  ✓ Set lifecycle policy for $REPO_NAME"
    fi
done

echo "----------------------------------------"
echo "ECR setup complete!"
echo ""
echo "Repository URLs:"
for service in "${SERVICES[@]}"; do
    echo "  ${service}: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${REPOSITORY_PREFIX}/${service}"
done

echo ""
echo "Next steps:"
echo "1. Update your GitLab CI/CD variables:"
echo "   - AWS_ACCOUNT_ID: $AWS_ACCOUNT_ID"
echo "   - AWS_DEFAULT_REGION: $AWS_REGION"
echo "   - ECR_REPOSITORY_PREFIX: $REPOSITORY_PREFIX"
echo ""
echo "2. Ensure your GitLab runners have AWS credentials configured"
echo "3. Test the pipeline by pushing to your develop branch"
