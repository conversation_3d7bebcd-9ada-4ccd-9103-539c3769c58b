# Deployment Scripts

This folder contains scripts for deploying CloudThinker in various environments and configurations.

## Scripts

### `deploy-local.sh`
**Purpose:** Deploy CloudThinker locally with built images

**Usage:**
```bash
./deploy-local.sh [stack_name]
```

**Examples:**
```bash
./deploy-local.sh                    # Uses default stack name
./deploy-local.sh cloudthinker-dev   # Custom stack name
```

**Features:**
- Uses local `.env` file
- Builds Docker images locally
- Supports payment service toggle
- Provides service status and URLs

### `deploy-ecr.sh`
**Purpose:** Deploy CloudThinker using ECR images

**Usage:**
```bash
./deploy-ecr.sh [environment] [image_tag]
```

**Examples:**
```bash
./deploy-ecr.sh production latest
./deploy-ecr.sh staging abc123f
./deploy-ecr.sh development v1.2.3
```

**Features:**
- Uses `.env.ecr` configuration
- Pulls images from ECR
- Supports multiple environments
- Automatic ECR authentication

### `start-dev.sh`
**Purpose:** Start development environment with specific services

**Usage:**
```bash
./start-dev.sh
```

**Features:**
- Optimized for development workflow
- Fast startup with minimal services
- Development-specific configuration

### `start-minimal.sh`
**Purpose:** Start minimal CloudThinker setup

**Usage:**
```bash
./start-minimal.sh
```

**Features:**
- Minimal service set for testing
- Reduced resource usage
- Quick startup time

## Environment Files

- **Local Deployment:** Uses `.env` (builds images locally)
- **ECR Deployment:** Uses `.env.ecr` (pulls from ECR)

## Quick Access

Using the scripts index:
```bash
# From project root
./scripts/index.sh deployment deploy-local
./scripts/index.sh deployment deploy-ecr production latest
./scripts/index.sh deployment start-dev
```

Direct access:
```bash
./scripts/deployment/deploy-local.sh
./scripts/deployment/deploy-ecr.sh production latest
./scripts/deployment/start-dev.sh
```

## Service URLs (Local Development)

After deployment, services are available at:
- **Frontend:** http://localhost:5173
- **Backend API:** http://localhost:8000
- **API Documentation:** http://localhost:8000/docs
- **Slack Integration:** http://localhost:3000
