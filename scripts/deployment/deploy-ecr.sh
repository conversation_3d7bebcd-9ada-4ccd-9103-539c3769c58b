#!/bin/bash

# Script to deploy CloudThinker using ECR images
# Usage: ./scripts/deploy-ecr.sh [environment] [image_tag]

set -e

# Configuration
ENVIRONMENT=${1:-production}
IMAGE_TAG=${2:-latest}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "Deploying CloudThinker with ECR images"
echo "Environment: $ENVIRONMENT"
echo "Image Tag: $IMAGE_TAG"
echo "----------------------------------------"

# Check if ECR environment file exists
ECR_ENV_FILE="$PROJECT_ROOT/.env.ecr"
if [ ! -f "$ECR_ENV_FILE" ]; then
    echo "Error: ECR environment file not found at $ECR_ENV_FILE"
    echo "Please create .env.ecr with your ECR configuration"
    exit 1
fi

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "Error: AWS CLI is not configured or credentials are invalid"
    echo "Please run 'aws configure' first"
    exit 1
fi

# Source ECR environment variables
echo "Loading ECR environment configuration..."
set -a  # automatically export all variables
source "$ECR_ENV_FILE"
set +a

# Override TAG with provided image tag
export TAG="$IMAGE_TAG"

# Login to ECR
echo "Logging into Amazon ECR..."
aws ecr get-login-password --region "$AWS_DEFAULT_REGION" | docker login --username AWS --password-stdin "$ECR_REGISTRY"

# Pull latest images
echo "Pulling images from ECR..."
docker pull "$DOCKER_IMAGE_BACKEND:$TAG" || echo "Warning: Failed to pull backend image"
docker pull "$DOCKER_IMAGE_FRONTEND:$TAG" || echo "Warning: Failed to pull frontend image"
docker pull "$DOCKER_IMAGE_EXECUTOR:$TAG" || echo "Warning: Failed to pull executor image"
docker pull "$DOCKER_IMAGE_PAYMENT:$TAG" || echo "Warning: Failed to pull payment image"
docker pull "$DOCKER_IMAGE_SLACK_INTEGRATION:$TAG" || echo "Warning: Failed to pull slack-integration image"

# Set stack name based on environment
export STACK_NAME="cloudthinker-$ENVIRONMENT"

# Deploy with docker-compose
echo "Deploying with docker-compose..."
cd "$PROJECT_ROOT"

if [ "${PAYMENT_ENABLED}" = "True" ]; then
    echo "Deploying with payment services enabled..."
    docker compose -f docker-compose.yml -f docker-compose.prod.yml --project-name "$STACK_NAME" up -d
else
    echo "Deploying without payment services..."
    docker compose -f docker-compose.yml -f docker-compose.prod.yml --project-name "$STACK_NAME" up -d --scale payment=0 --scale payment-prestart=0
fi

echo "----------------------------------------"
echo "Deployment complete!"
echo "Stack name: $STACK_NAME"
echo "Environment: $ENVIRONMENT"
echo "Image tag: $TAG"
echo ""
echo "Services status:"
docker compose --project-name "$STACK_NAME" ps

echo ""
echo "To view logs:"
echo "  docker compose --project-name $STACK_NAME logs -f"
echo ""
echo "To stop services:"
echo "  docker compose --project-name $STACK_NAME down"
