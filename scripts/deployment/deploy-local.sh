#!/bin/bash

# Script to deploy CloudThinker locally with built images
# Usage: ./scripts/deploy-local.sh [stack_name]

set -e

# Configuration
STACK_NAME=${1:-cloudthinker-local}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "Deploying CloudThinker locally"
echo "Stack name: $STACK_NAME"
echo "----------------------------------------"

# Check if local environment file exists
LOCAL_ENV_FILE="$PROJECT_ROOT/.env"
if [ ! -f "$LOCAL_ENV_FILE" ]; then
    echo "Error: Local environment file not found at $LOCAL_ENV_FILE"
    echo "Please ensure .env file exists for local development"
    exit 1
fi

# Change to project root
cd "$PROJECT_ROOT"

# Build and deploy with docker-compose
echo "Building and deploying services..."

# Check if payment is enabled
PAYMENT_ENABLED=$(grep "^PAYMENT_ENABLED=" .env | cut -d'=' -f2 | tr -d '"' || echo "False")

if [ "${PAYMENT_ENABLED}" = "True" ]; then
    echo "Building and deploying with payment services enabled..."
    docker compose -f docker-compose.yml --project-name "$STACK_NAME" up -d --build
else
    echo "Building and deploying without payment services..."
    docker compose -f docker-compose.yml --project-name "$STACK_NAME" up -d --build --scale payment=0 --scale payment-prestart=0
fi

echo "----------------------------------------"
echo "Local deployment complete!"
echo "Stack name: $STACK_NAME"
echo ""
echo "Services status:"
docker compose --project-name "$STACK_NAME" ps

echo ""
echo "Application URLs:"
echo "  Frontend: http://localhost:5173"
echo "  Backend API: http://localhost:8000"
echo "  API Docs: http://localhost:8000/docs"
echo "  Slack Integration: http://localhost:3000"
echo ""
echo "To view logs:"
echo "  docker compose --project-name $STACK_NAME logs -f"
echo ""
echo "To stop services:"
echo "  docker compose --project-name $STACK_NAME down"
echo ""
echo "To rebuild a specific service:"
echo "  docker compose --project-name $STACK_NAME up -d --build [service_name]"
