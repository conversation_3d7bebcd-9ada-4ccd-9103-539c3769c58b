# Development Scripts

This folder contains scripts for development tools, code generation, and development environment setup.

## Scripts

### `generate-client.sh`
**Purpose:** Generate API client code from OpenAPI specifications

**Usage:**
```bash
./generate-client.sh
```

**Features:**
- Generates client libraries for API consumption
- Updates client code based on current API schema
- Supports multiple programming languages

### `install-hadolint.sh`
**Purpose:** Install Hadolint for Dockerfile linting

**Usage:**
```bash
./install-hadolint.sh
```

**Features:**
- Downloads and installs Hadolint binary
- Sets up Dockerfile linting capabilities
- Configures for CI/CD integration

## Prerequisites

- Development environment set up
- Internet connection for downloads
- Appropriate permissions for installation

## Quick Access

Using the scripts index:
```bash
# From project root
./scripts/index.sh development generate-client
./scripts/index.sh development install-hadolint
```

Direct access:
```bash
./scripts/development/generate-client.sh
./scripts/development/install-hadolint.sh
```

## Integration with Development Workflow

These scripts are typically used during:
- **Initial setup** of development environment
- **API changes** requiring client regeneration
- **Code quality** improvements with linting tools
- **CI/CD pipeline** setup and maintenance
