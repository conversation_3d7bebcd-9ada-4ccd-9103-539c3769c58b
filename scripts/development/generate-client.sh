#! /usr/bin/env bash

set -e
set -x

# Fallback to curl method
if ! curl -X GET "http://localhost:8000/api/v1/openapi.json" > openapi.json 2>/dev/null; then
  # Try python method if curl fails
  cd backend && python -c "import app.main; import json; print(json.dumps(app.main.app.openapi()))" > ../openapi.json
  cd ..
fi

node frontend/modify-openapi-operationids.js
mv openapi.json frontend/
cd frontend
npm run generate-client
npx biome format --write ./src/client
