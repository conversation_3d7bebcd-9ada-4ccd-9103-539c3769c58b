#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Installing Hadolint for Docker linting...${NC}"

# Detect OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    echo -e "${YELLOW}Detected Linux OS${NC}"
    sudo wget -O /usr/local/bin/hadolint https://github.com/hadolint/hadolint/releases/latest/download/hadolint-Linux-x86_64
    sudo chmod +x /usr/local/bin/hadolint
elif [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    echo -e "${YELLOW}Detected macOS${NC}"
    if command -v brew &> /dev/null; then
        brew install hadolint
    else
        echo -e "${YELLOW}Homebrew not found. Installing Hadolint manually...${NC}"
        sudo curl -Lo /usr/local/bin/hadolint https://github.com/hadolint/hadolint/releases/latest/download/hadolint-Darwin-x86_64
        sudo chmod +x /usr/local/bin/hadolint
    fi
else
    echo -e "${RED}Unsupported OS: $OSTYPE${NC}"
    echo -e "${YELLOW}Please install Hadolint manually: https://github.com/hadolint/hadolint#install${NC}"
    exit 1
fi

# Verify installation
if command -v hadolint &> /dev/null; then
    echo -e "${GREEN}Hadolint installed successfully!${NC}"
    hadolint --version
    exit 0
else
    echo -e "${RED}Failed to install Hadolint.${NC}"
    echo -e "${YELLOW}Please install it manually: https://github.com/hadolint/hadolint#install${NC}"
    exit 1
fi
