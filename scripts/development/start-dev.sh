#!/bin/bash

# Load environment variables from .env file
set -a
source .env
set +a

# Build compose file arguments
COMPOSE_FILES="-f docker-compose.yml -f docker-compose.override.yml"

# Add monitoring services if OBSERVABILITY is enabled
if [ "$OBSERVABILITY" = "true" ]; then
  echo "Observability enabled - including monitoring services..."
  COMPOSE_FILES="$COMPOSE_FILES -f docker-compose.monitoring.yml -f docker-compose.override.monitoring.yml"
else
  echo "Observability disabled - excluding monitoring services..."
fi

# Add payment profile if enabled
PROFILES=""
if [ "$PAYMENT_ENABLED" = "true" ]; then
  echo "Payment enabled - including payment services..."
  PROFILES="--profile payment"
  echo "Forward stripe webhook"
  stripe listen --forward-to http://localhost:8000/api/v1/subscriptions/webhook &
fi

# Enable docker compose watch for development
echo "Starting docker compose with watch mode..."
docker compose $COMPOSE_FILES $PROFILES watch
