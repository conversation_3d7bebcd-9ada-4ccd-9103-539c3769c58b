#!/bin/bash

# Load environment variables from .env file
set -a
source .env
set +a

# Force disable observability for minimal startup
export OBSERVABILITY=false

echo "Starting CloudThinker in minimal mode (no monitoring services)..."

# Build compose file arguments
COMPOSE_FILES="-f docker-compose.yml -f docker-compose.override.yml"

# Add payment profile if enabled
PROFILES=""
if [ "$PAYMENT_ENABLED" = "true" ]; then
  echo "Payment enabled - including payment services..."
  PROFILES="--profile payment"
  echo "Forward stripe webhook"
  stripe listen --forward-to http://localhost:8000/api/v1/subscriptions/webhook &
fi

# Enable docker compose watch for development
echo "Starting docker compose with watch mode (minimal setup)..."
docker compose $COMPOSE_FILES $PROFILES watch
