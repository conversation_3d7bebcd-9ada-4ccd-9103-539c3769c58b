# Git Scripts

This folder contains scripts for Git workflow automation, branch management, and repository maintenance.

## Scripts

### `rebase_develop.sh`
**Purpose:** Rebase current branch on develop branch

**Usage:**
```bash
./rebase_develop.sh
```

**Features:**
- Safely rebases current branch on latest develop
- <PERSON><PERSON> merge conflicts gracefully
- Maintains clean commit history
- Validates branch state before rebasing

## Git Workflow

These scripts support the following Git workflow:
- **Feature branches** created from develop
- **Regular rebasing** to keep branches up-to-date
- **Clean history** through proper rebasing
- **Conflict resolution** with guided assistance

## Prerequisites

- Git repository initialized
- Develop branch exists and is accessible
- Working directory is clean (no uncommitted changes)

## Quick Access

Using the scripts index:
```bash
# From project root
./scripts/index.sh git rebase_develop
```

Direct access:
```bash
./scripts/git/rebase_develop.sh
```

## Best Practices

- Always commit or stash changes before rebasing
- Review conflicts carefully during rebase
- Test functionality after rebasing
- Push rebased branches with force-with-lease

## Integration

These scripts integrate with:
- **Development workflow** for branch maintenance
- **Code review process** for clean history
- **Release preparation** for stable branches
