#!/bin/bash

# Get the current branch name and store it in the 'current_branch' variable
current_branch=$(git branch --show-current)

# Check if we successfully got the current branch name
if [ -z "$current_branch" ]; then
  echo "Error: Could not determine the current branch."
  exit 1
fi

echo "Currently on branch: $current_branch"

# Stash any uncommitted changes
echo "Stashing any uncommitted changes..."
git stash

# Checkout the 'develop' branch
echo "Checking out the 'develop' branch..."
git checkout develop

# Pull the latest changes from the remote 'origin' for the 'develop' branch
echo "Pulling the latest changes from origin develop..."
git pull origin develop

# Checkout back to the original branch
echo "Checking out back to branch: $current_branch"
git checkout "$current_branch"

# Rebase the current branch onto 'develop'
echo "Rebasing $current_branch onto develop..."
git rebase develop

# Attempt to pop the stashed changes
echo "Attempting to apply stashed changes..."
git stash pop

echo "Workflow completed."
