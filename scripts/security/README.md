# Security Scripts

This folder contains scripts for security validation, secret detection, and security compliance checks.

## Scripts

### `check-secrets.sh`
**Purpose:** Scan codebase for exposed secrets and sensitive information

**Usage:**
```bash
./check-secrets.sh
```

**Features:**
- Detects API keys, passwords, and tokens
- Scans commit history for leaked secrets
- Provides remediation suggestions
- Integrates with pre-commit hooks

## Security Checks

- **Secret Detection:** API keys, tokens, passwords
- **Credential Scanning:** Database connections, service accounts
- **Configuration Validation:** Security settings verification
- **Dependency Scanning:** Known vulnerabilities in dependencies

## Prerequisites

- Security scanning tools installed
- Access to codebase and git history
- Appropriate permissions for file scanning

## Quick Access

Using the scripts index:
```bash
# From project root
./scripts/index.sh security check-secrets
```

Direct access:
```bash
./scripts/security/check-secrets.sh
```

## Integration

These scripts are integrated into:
- **Pre-commit hooks** for automatic scanning
- **CI/CD pipeline** for continuous security validation
- **Development workflow** for proactive security measures

## Best Practices

- Run security checks before committing code
- Regularly update security scanning tools
- Review and remediate identified issues promptly
- Use environment variables for sensitive configuration
