# Testing Scripts

This folder contains scripts for running tests, validations, and quality assurance checks.

## Scripts

### `test-local.sh`
**Purpose:** Run local tests for CloudThinker services

**Usage:**
```bash
./test-local.sh
```

**Features:**
- Executes local test suites
- Validates service functionality
- Provides test results and coverage

### `test.sh`
**Purpose:** Run comprehensive test suite

**Usage:**
```bash
./test.sh
```

**Features:**
- Full test suite execution
- Integration and unit tests
- Test reporting and metrics

## Test Categories

- **Unit Tests:** Individual component testing
- **Integration Tests:** Service interaction testing
- **API Tests:** Endpoint validation
- **End-to-End Tests:** Complete workflow testing

## Prerequisites

- Development environment set up
- Test dependencies installed
- Services running (for integration tests)

## Quick Access

Using the scripts index:
```bash
# From project root
./scripts/index.sh testing test-local
./scripts/index.sh testing test
```

Direct access:
```bash
./scripts/testing/test-local.sh
./scripts/testing/test.sh
```

## CI/CD Integration

These scripts are integrated into:
- **Pre-commit hooks** for code quality
- **GitLab CI/CD pipeline** for automated testing
- **Development workflow** for continuous validation
